# 笔尖传奇写作 - 手机端部署指南

## 部署概述

本手机端版本是一个纯前端应用，支持PWA（渐进式Web应用）功能，可以部署到任何支持静态文件托管的服务器上。

## 文件结构

```
mobile/
├── index.html          # 首页入口
├── writing.html        # 写作界面
├── chat.html          # 对话界面
├── library.html       # 书架界面
├── profile.html       # 个人中心
├── manifest.json      # PWA配置文件
├── sw.js             # Service Worker
├── README.md         # 项目说明
└── DEPLOYMENT.md     # 部署指南
```

## 部署方式

### 1. 静态文件服务器部署

#### 方式一：Nginx
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/mobile;
    index index.html;
    
    # 支持PWA
    location /manifest.json {
        add_header Content-Type application/manifest+json;
    }
    
    location /sw.js {
        add_header Content-Type application/javascript;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
    
    # 支持SPA路由
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 缓存静态资源
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### 方式二：Apache
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/mobile
    
    # 支持PWA
    <Files "manifest.json">
        Header set Content-Type "application/manifest+json"
    </Files>
    
    <Files "sw.js">
        Header set Content-Type "application/javascript"
        Header set Cache-Control "no-cache, no-store, must-revalidate"
    </Files>
    
    # 支持SPA路由
    <Directory "/path/to/mobile">
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
    
    # 缓存静态资源
    <LocationMatch "\.(js|css|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
    </LocationMatch>
</VirtualHost>
```

### 2. CDN部署

#### 方式一：GitHub Pages
1. 将mobile文件夹内容推送到GitHub仓库
2. 在仓库设置中启用GitHub Pages
3. 选择分支和文件夹
4. 访问 `https://username.github.io/repository-name`

#### 方式二：Netlify
1. 将mobile文件夹拖拽到Netlify部署页面
2. 或连接GitHub仓库自动部署
3. 配置自定义域名（可选）

#### 方式三：Vercel
1. 安装Vercel CLI: `npm i -g vercel`
2. 在mobile文件夹中运行: `vercel`
3. 按提示完成部署

### 3. 云服务器部署

#### 阿里云OSS
```bash
# 安装ossutil
wget http://gosspublic.alicdn.com/ossutil/1.7.0/ossutil64
chmod 755 ossutil64

# 配置
./ossutil64 config

# 上传文件
./ossutil64 cp -r mobile/ oss://your-bucket-name/ --update
```

#### 腾讯云COS
```bash
# 安装coscmd
pip install coscmd

# 配置
coscmd config -a your-secret-id -s your-secret-key -b your-bucket -r your-region

# 上传文件
coscmd upload -r mobile/ /
```

## HTTPS配置

### 1. Let's Encrypt (免费SSL)
```bash
# 安装certbot
sudo apt-get install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 2. 云服务商SSL
- 阿里云：在控制台申请免费SSL证书
- 腾讯云：在SSL证书管理中申请
- Cloudflare：启用SSL/TLS加密

## PWA配置

### 1. 域名要求
- 必须使用HTTPS（localhost除外）
- 需要有效的SSL证书

### 2. 浏览器支持
- Chrome 45+
- Firefox 44+
- Safari 11.1+
- Edge 17+

### 3. 安装提示
用户可以通过以下方式安装PWA：
- Chrome：地址栏右侧的安装图标
- Safari：分享菜单 → 添加到主屏幕
- Edge：设置菜单 → 应用 → 安装此站点

## 性能优化

### 1. 文件压缩
```bash
# 启用gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
```

### 2. 缓存策略
```nginx
# HTML文件不缓存
location ~* \.html$ {
    add_header Cache-Control "no-cache, no-store, must-revalidate";
}

# 静态资源长期缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 3. CDN加速
- 使用CDN分发静态资源
- 配置合适的缓存策略
- 启用HTTP/2

## 监控与分析

### 1. 错误监控
```javascript
// 在每个页面添加错误监控
window.addEventListener('error', function(e) {
    console.error('页面错误:', e.error);
    // 发送错误信息到监控服务
});

window.addEventListener('unhandledrejection', function(e) {
    console.error('未处理的Promise错误:', e.reason);
    // 发送错误信息到监控服务
});
```

### 2. 性能监控
```javascript
// 页面加载性能
window.addEventListener('load', function() {
    const perfData = performance.getEntriesByType('navigation')[0];
    console.log('页面加载时间:', perfData.loadEventEnd - perfData.fetchStart);
});
```

### 3. 用户行为分析
- Google Analytics
- 百度统计
- 友盟统计

## 安全配置

### 1. 内容安全策略 (CSP)
```html
<meta http-equiv="Content-Security-Policy" content="
    default-src 'self';
    script-src 'self' 'unsafe-inline';
    style-src 'self' 'unsafe-inline';
    img-src 'self' data: https:;
    connect-src 'self' https:;
">
```

### 2. 其他安全头
```nginx
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
```

## 备份与恢复

### 1. 定期备份
```bash
#!/bin/bash
# 备份脚本
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf backup_$DATE.tar.gz mobile/
```

### 2. 版本控制
- 使用Git管理代码版本
- 定期推送到远程仓库
- 创建发布标签

## 故障排除

### 1. 常见问题
- PWA无法安装：检查HTTPS和manifest.json
- Service Worker不工作：检查文件路径和缓存策略
- 页面无法访问：检查服务器配置和DNS解析

### 2. 调试工具
- Chrome DevTools
- Firefox Developer Tools
- Lighthouse性能测试

## 更新部署

### 1. 版本更新流程
1. 修改代码
2. 更新版本号（sw.js中的CACHE_NAME）
3. 测试功能
4. 部署到生产环境
5. 验证更新

### 2. 回滚策略
- 保留上一版本的备份
- 快速回滚到稳定版本
- 监控回滚后的系统状态

---

**部署完成后，请访问您的域名测试所有功能是否正常工作。**
