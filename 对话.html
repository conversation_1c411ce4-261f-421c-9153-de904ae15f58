<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对话 - 笔尖传奇写作</title>
    <style>
        /* --- 1. 全局与动态配色系统 (与其他页面统一) --- */
        :root {
            /* 默认皮肤 (护眼蓝灰) */
            --bg-main: #F0F3F7;
            --bg-panel: #FAFBFC;
            --bg-panel-secondary: #F3F5F8;
            --bg-content: #F7F9FB;
            --text-dark: #2D3748;
            --text-light: #718096;
            --text-on-primary: #FFFFFF;
            --border-color: #E8EBEF;
            --gutter-color: transparent;
            --shadow-color: rgba(93, 156, 236, 0.08);
            --shadow-color-light: rgba(0, 0, 0, 0.04);
            --shadow-color-heavy: rgba(0, 0, 0, 0.12);
            /* 主题色 */
            --primary-color: #5D9CEC;
            --primary-color-hover: #4A89E2;
            --secondary-color: #8696A7;
            --accent-color: #48BB78;
            --accent-color-hover: #3AA967;
            --warning-color: #FF6B6B;
            --warning-color-hover: #FF5252;
            /* 内容区专用色 */
            --content-header-bg: #E8F2FF;
            --content-header-color: #2B5797;
            /* 布局尺寸 */
            --font-size-base: 16px;
            --font-size-sm: 14px;
            --font-size-lg: 18px;
            --header-height: 52px;
            --sidebar-width: 80px;
            --sidebar-collapsed-width: 4px;
            /* 对话界面专用 */
            --message-bg-user: var(--bg-panel);
            --message-bg-ai: var(--bg-content);
            --message-border-radius: 12px;
            --input-height: 120px;
        }

        [data-theme="green-leaf"] {
            --bg-main: #EFF3EF;
            --bg-panel: #F8FAF8;
            --bg-panel-secondary: #F0F3F0;
            --bg-content: #F5F8F5;
            --text-dark: #3E4A3E;
            --text-light: #6B7C6B;
            --border-color: #E0E6E0;
            --primary-color: #6A9C89;
            --primary-color-hover: #5A8C79;
            --secondary-color: #8A9B94;
            --accent-color: #E99469;
            --accent-color-hover: #D98459;
            --warning-color: #FF6B6B;
            --warning-color-hover: #FF5252;
            --shadow-color: rgba(106, 156, 137, 0.08);
            --content-header-bg: #E5F2E9;
            --content-header-color: #3A6B4F;
        }

        [data-theme="sepia"] {
            --bg-main: #FBF0D9;
            --bg-panel: #FAF4E8;
            --bg-panel-secondary: #F6ECDA;
            --bg-content: #FAF4E8;
            --text-dark: #5C4B33;
            --text-light: #8B7355;
            --border-color: #EAE0C8;
            --primary-color: #A67B5B;
            --primary-color-hover: #966B4B;
            --secondary-color: #B0A08D;
            --accent-color: #5D9CEC;
            --accent-color-hover: #4A89E2;
            --warning-color: #FF6B6B;
            --warning-color-hover: #FF5252;
            --shadow-color: rgba(166, 123, 91, 0.1);
            --content-header-bg: #F4E6D4;
            --content-header-color: #7A5A3A;
        }

        [data-theme="dark"] {
            --bg-main: #1A202C;
            --bg-panel: #2D3748;
            --bg-panel-secondary: #252E3E;
            --bg-content: #323B4C;
            --text-dark: #E2E8F0;
            --text-light: #A0AEC0;
            --border-color: #4A5568;
            --primary-color: #4A5568;
            --primary-color-hover: #718096;
            --secondary-color: #3B475C;
            --accent-color: #48BB78;
            --accent-color-hover: #3AA967;
            --warning-color: #FF6B6B;
            --warning-color-hover: #FF5252;
            --shadow-color: rgba(0, 0, 0, 0.2);
            --content-header-bg: #3A4558;
            --content-header-color: #CBD5E0;
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", "Helvetica Neue", sans-serif;
            background-color: var(--bg-main);
            margin: 0;
            padding: 0;
            color: var(--text-dark);
            font-size: var(--font-size-base);
            line-height: 1.7;
            -webkit-font-smoothing: antialiased;
            display: flex;
            height: 100vh;
            overflow: hidden;
            transition: background-color 0.3s, color 0.3s;
        }

        /* --- 左侧导航栏 --- */
        .sidebar-wrapper {
            position: relative;
            width: var(--sidebar-width);
            flex-shrink: 0;
            transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar-wrapper.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar {
            position: absolute;
            left: 0;
            top: 0;
            width: var(--sidebar-width);
            height: 100%;
            background: var(--bg-panel);
            box-shadow: 2px 0 8px var(--shadow-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 0;
            z-index: 100;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .sidebar-wrapper.collapsed .sidebar {
            transform: translateX(calc(-1 * var(--sidebar-width) + var(--sidebar-collapsed-width)));
        }

        .sidebar-trigger {
            position: fixed;
            left: 0;
            top: 0;
            width: 20px;
            height: 100%;
            z-index: 101;
        }

        .sidebar-wrapper:not(.collapsed) .sidebar-trigger {
            width: calc(var(--sidebar-width) + 20px);
        }

        .sidebar-content {
            opacity: 1;
            transition: opacity 0.2s;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100%;
        }
        .sidebar-wrapper.collapsed .sidebar-content {
            opacity: 0;
            pointer-events: none;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px var(--shadow-color);
            transition: transform 0.2s;
            flex-shrink: 0;
        }
        .user-avatar:hover {
            transform: scale(1.05);
        }
        .user-avatar img { 
            width: 100%; 
            height: 100%; 
            object-fit: cover; 
        }

        .nav-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            width: 100%;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            color: var(--text-light);
            transition: all 0.2s ease;
            padding: 8px;
            border-radius: 10px;
            width: 60px;
        }
        .nav-item:hover {
            color: var(--primary-color);
            background-color: var(--bg-panel-secondary);
        }
        .nav-item.active {
            color: var(--primary-color);
            background-color: var(--bg-panel-secondary);
        }
        .nav-item-icon {
            font-size: 20px;
            width: 24px;
            height: 24px;
            fill: currentColor;
        }
        .nav-item-text {
            font-size: 11px;
            font-weight: 500;
        }
        .sidebar-footer {
            margin-top: auto;
        }

        /* --- 主内容区 --- */
        .main-content {
            flex-grow: 1;
            display: flex;
            height: 100vh;
            overflow: hidden;
        }

        /* --- 对话界面布局 --- */
        .dialogue-container {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            background: var(--bg-main);
            position: relative;
        }

        /* 顶部工具栏 */
        .dialogue-header {
            height: var(--header-height);
            background: var(--bg-panel);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            flex-shrink: 0;
            box-shadow: 0 2px 8px var(--shadow-color-light);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .prompt-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .prompt-selector label {
            font-size: var(--font-size-sm);
            color: var(--text-light);
            font-weight: 500;
        }

        .btn-select-prompt {
            padding: 8px 16px;
            background: var(--bg-content);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-select-prompt:hover {
            background: var(--bg-panel-secondary);
            border-color: var(--primary-color);
        }

        .btn-create-prompt {
            padding: 8px 16px;
            background: var(--accent-color);
            color: var(--text-on-primary);
            border: none;
            border-radius: 8px;
            font-size: var(--font-size-sm);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-create-prompt:hover {
            background: var(--accent-color-hover);
            transform: translateY(-1px);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-btn {
            padding: 8px 16px;
            background: var(--bg-content);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .header-btn:hover {
            background: var(--bg-panel-secondary);
            border-color: var(--primary-color);
        }

        /* 切换模型按钮特殊样式 - 红色 */
        .switch-model-btn {
            background: var(--warning-color);
            color: var(--text-on-primary);
            border-color: var(--warning-color);
        }

        .switch-model-btn:hover {
            background: var(--warning-color-hover);
            border-color: var(--warning-color-hover);
        }

        .switch-model-btn svg {
            width: 16px;
            height: 16px;
        }

        /* 对话消息区域 */
        .messages-container {
            flex-grow: 1;
            overflow-y: auto;
            padding: 30px 40px;
            scroll-behavior: smooth;
        }

        .messages-wrapper {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* 消息样式 */
        .message {
            display: flex;
            gap: 12px;
            animation: fadeInUp 0.3s ease-out;
            position: relative;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: var(--font-size-sm);
        }

        .message.ai .message-avatar {
            background: var(--primary-color);
            color: var(--text-on-primary);
        }

        .message.user .message-avatar {
            background: var(--accent-color);
            color: var(--text-on-primary);
        }

        .message-content {
            max-width: 80%;
            padding: 16px 20px;
            border-radius: var(--message-border-radius);
            box-shadow: 0 2px 8px var(--shadow-color-light);
            transition: all 0.2s;
        }

        .message.ai .message-content {
            background: var(--message-bg-ai);
            border-top-left-radius: 4px;
        }

        .message.user .message-content {
            background: var(--message-bg-user);
            border-top-right-radius: 4px;
        }

        .message-text {
            font-size: var(--font-size-base);
            line-height: 1.6;
            color: var(--text-dark);
            white-space: pre-wrap;
            word-wrap: break-word;
            word-break: break-word;
            overflow-wrap: break-word;
        }

        .message-time {
            font-size: 12px;
            color: var(--text-light);
            margin-top: 4px;
            opacity: 0.7;
        }

        /* 消息操作栏 - 始终显示 */
        .message-actions {
            display: flex;
            gap: 12px;
            margin-top: 8px;
            align-items: center;
        }

        .message-action-btn {
            padding: 4px 8px;
            background: transparent;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 12px;
            color: var(--text-light);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .message-action-btn:hover {
            background: var(--bg-panel);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .message-action-btn svg {
            width: 14px;
            height: 14px;
        }

        /* 字数消耗标签 */
        .message-usage {
            font-size: 11px;
            color: var(--text-light);
            opacity: 0.7;
            margin-left: 8px;
        }

        /* 系统消息样式 */
        .system-message {
            text-align: center;
            padding: 8px 16px;
            margin: 10px auto;
            max-width: 400px;
            background: var(--bg-panel-secondary);
            border-radius: 20px;
            font-size: var(--font-size-sm);
            color: var(--text-light);
            animation: fadeIn 0.3s ease-out;
        }

        /* 输入区域 */
        .input-container {
            background: var(--bg-panel);
            border-top: 1px solid var(--border-color);
            padding: 20px 40px;
            flex-shrink: 0;
            box-shadow: 0 -2px 8px var(--shadow-color-light);
        }

        .input-wrapper {
            max-width: 600px;
            margin: 0 auto;
            display: flex;
            gap: 8px;
            align-items: flex-end;
        }

        /* 当前模型显示 */
        .current-model-display {
            padding: 12px 16px;
            background: var(--bg-panel-secondary);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
        }

        .model-label {
            font-size: var(--font-size-sm);
            color: var(--text-light);
            font-weight: 500;
        }

        .model-name {
            font-size: var(--font-size-base);
            color: var(--text-dark);
            font-weight: 600;
        }

        .input-box {
            flex-grow: 1;
            position: relative;
        }

        .input-textarea {
            width: 100%;
            min-height: 40px;
            max-height: var(--input-height);
            padding: 8px 50px 8px 16px;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            background: var(--bg-content);
            color: var(--text-dark);
            font-size: var(--font-size-base);
            line-height: 1.5;
            resize: none;
            transition: all 0.2s;
            font-family: inherit;
        }

        .input-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }

        .input-actions {
            position: absolute;
            right: 8px;
            bottom: 8px;
            display: flex;
            gap: 8px;
        }

        .input-action-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 6px;
            background: transparent;
            color: var(--text-light);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .input-action-btn:hover {
            background: var(--bg-panel);
            color: var(--primary-color);
        }

        /* 隐藏的文件输入 */
        .file-input-hidden {
            display: none;
        }

        .send-button {
            padding: 8px 20px;
            height: 40px;
            background: var(--primary-color);
            color: var(--text-on-primary);
            border: none;
            border-radius: 10px;
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .send-button:hover {
            background: var(--primary-color-hover);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px var(--shadow-color-heavy);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* 空状态 */
        .empty-state {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px;
            text-align: center;
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .empty-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 10px;
        }

        .empty-desc {
            font-size: var(--font-size-base);
            color: var(--text-light);
            max-width: 400px;
            line-height: 1.6;
        }

        /* 右侧面板 - 对话历史和关联附件 */
        .right-panel {
            width: 280px;
            background: var(--bg-panel);
            border-left: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            box-shadow: -2px 0 8px var(--shadow-color-light);
            overflow: hidden;
        }




        /* 弹窗遮罩 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2999;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .modal-overlay.show {
            display: block;
            opacity: 1;
        }

        /* 统一按钮样式系统 */
        .btn-primary, .btn-secondary, .btn-cancel {
            background: var(--bg-panel);
            color: var(--primary-color);
            border: 1px solid var(--border-color);
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: var(--font-size-sm);
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            text-decoration: none;
            outline: none;
            box-shadow: none;
        }

        .btn-primary:hover, .btn-secondary:hover, .btn-cancel:hover {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        /* 主要按钮变体 */
        .btn-primary {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background: var(--primary-color-hover);
            border-color: var(--primary-color-hover);
        }

        /* 取消按钮变体 */
        .btn-cancel {
            background: var(--bg-panel-secondary);
            color: var(--text-light);
            border-color: var(--border-color);
        }

        .btn-cancel:hover {
            background: var(--bg-panel);
            color: var(--text-dark);
            border-color: var(--border-color);
        }

        .panel-tabs {
            display: flex;
            height: var(--header-height);
            border-bottom: 1px solid var(--border-color);
        }

        .panel-tab {
            flex: 1;
            background: transparent;
            border: none;
            color: var(--text-light);
            font-size: var(--font-size-sm);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }

        .panel-tab:hover {
            color: var(--text-dark);
            background: var(--bg-panel-secondary);
        }

        .panel-tab.active {
            color: var(--primary-color);
            background: var(--bg-content);
        }

        .panel-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--primary-color);
        }

        /* 面板内容滑动动效 */
        .panel-content-wrapper {
            flex-grow: 1;
            position: relative;
            overflow: hidden;
        }

        .panel-content {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow-y: auto;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .panel-content.active {
            opacity: 1;
            transform: translateX(0);
        }

        .panel-content.active-left {
            transform: translateX(-100%);
        }

        /* 对话历史 */
        .history-list {
            padding: 12px;
        }

        .history-item {
            padding: 12px;
            margin-bottom: 8px;
            background: var(--bg-content);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid transparent;
            position: relative;
            opacity: 0;
            transform: translateY(10px);
            animation: slideInUp 0.3s ease-out forwards;
       }

       .history-item:nth-child(1) { animation-delay: 0.05s; }
       .history-item:nth-child(2) { animation-delay: 0.1s; }
       .history-item:nth-child(3) { animation-delay: 0.15s; }
       .history-item:nth-child(4) { animation-delay: 0.2s; }

       @keyframes slideInUp {
           to {
               opacity: 1;
               transform: translateY(0);
           }
       }

       .history-item:hover {
           background: var(--bg-panel-secondary);
           border-color: var(--border-color);
           transform: translateX(4px);
       }

       .history-item.active {
           background: var(--primary-color);
           color: var(--text-on-primary);
       }

       .history-title {
           font-size: var(--font-size-sm);
           font-weight: 500;
           margin-bottom: 4px;
           overflow: hidden;
           text-overflow: ellipsis;
           white-space: nowrap;
           padding-right: 24px;
       }

       .history-time {
           font-size: 12px;
           opacity: 0.7;
       }

       /* 历史记录删除按钮 */
       .history-delete {
           position: absolute;
           top: 12px;
           right: 12px;
           width: 20px;
           height: 20px;
           background: transparent;
           border: none;
           color: var(--text-light);
           cursor: pointer;
           opacity: 0;
           transition: all 0.2s;
           display: flex;
           align-items: center;
           justify-content: center;
       }

       .history-item:hover .history-delete {
           opacity: 1;
       }

       .history-delete:hover {
           color: var(--warning-color);
           transform: scale(1.2);
       }

       .history-item.active .history-delete {
           color: var(--text-on-primary);
       }

       .new-chat-btn {
           margin: 12px;
           width: calc(100% - 24px);
           padding: 10px;
           background: var(--accent-color);
           color: var(--text-on-primary);
           border: none;
           border-radius: 8px;
           font-size: var(--font-size-sm);
           font-weight: 500;
           cursor: pointer;
           transition: all 0.2s;
           display: flex;
           align-items: center;
           justify-content: center;
           gap: 6px;
       }

       .new-chat-btn:hover {
           background: var(--accent-color-hover);
           transform: translateY(-1px);
       }

       /* 关联附件 */
       .attachments-content {
           padding: 20px;
       }

       .attachment-group {
           margin-bottom: 20px;
           opacity: 0;
           transform: translateY(20px);
           animation: fadeInUp 0.4s ease-out forwards;
       }

       .attachment-group:nth-child(1) { animation-delay: 0.1s; }
       .attachment-group:nth-child(2) { animation-delay: 0.15s; }
       .attachment-group:nth-child(3) { animation-delay: 0.2s; }
       .attachment-group:nth-child(4) { animation-delay: 0.25s; }

       .attachment-label {
           display: block;
           font-size: var(--font-size-sm);
           font-weight: 500;
           color: var(--text-dark);
           margin-bottom: 8px;
       }

       .attachment-select {
           width: 100%;
           padding: 10px;
           border: 1px solid var(--border-color);
           border-radius: 8px;
           background: var(--bg-content);
           color: var(--text-dark);
           font-size: var(--font-size-sm);
           cursor: pointer;
           transition: all 0.2s;
       }

       .attachment-select:hover {
           border-color: var(--primary-color);
           transform: translateY(-1px);
           box-shadow: 0 2px 8px var(--shadow-color);
       }

       .attachment-select:focus {
           outline: none;
           border-color: var(--primary-color);
           box-shadow: 0 0 0 3px var(--shadow-color);
       }

       .attachment-hint {
           font-size: 12px;
           color: var(--text-light);
           margin-top: 4px;
       }

       /* 提示词市场弹窗 */
       .modal {
           display: none;
           position: fixed;
           inset: 0;
           background: rgba(0, 0, 0, 0);
           z-index: 1000;
           justify-content: center;
           align-items: center;
           transition: background-color 0.3s ease-out;
       }

       .modal.show {
           background: rgba(0, 0, 0, 0.6);
       }

       .modal-content {
           background: var(--bg-panel);
           padding: 30px;
           border-radius: 16px;
           box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
           max-width: 800px;
           width: 90%;
           max-height: 80vh;
           overflow-y: auto;
           transform: scale(0.9) translateY(20px);
           opacity: 0;
           transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
       }

       .modal.show .modal-content {
           transform: scale(1) translateY(0);
           opacity: 1;
       }

       .modal-header {
           margin-bottom: 24px;
       }

       .modal-title {
           font-size: 1.5rem;
           font-weight: 700;
           color: var(--text-dark);
           margin-bottom: 8px;
       }

       .modal-desc {
           font-size: var(--font-size-sm);
           color: var(--text-light);
       }

       /* 提示词分类 */
       .prompt-categories {
           display: flex;
           gap: 10px;
           margin-bottom: 20px;
           flex-wrap: wrap;
       }

       .category-btn {
           padding: 6px 16px;
           background: var(--bg-content);
           border: 1px solid var(--border-color);
           border-radius: 20px;
           font-size: var(--font-size-sm);
           color: var(--text-dark);
           cursor: pointer;
           transition: all 0.2s;
       }

       .category-btn:hover {
           background: var(--bg-panel-secondary);
           border-color: var(--primary-color);
           transform: translateY(-2px);
       }

       .category-btn.active {
           background: var(--primary-color);
           color: var(--text-on-primary);
           border-color: var(--primary-color);
       }

       /* 提示词网格 */
       .prompt-grid {
           display: grid;
           grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
           gap: 16px;
           margin-bottom: 20px;
       }

       .prompt-card {
           background: var(--bg-content);
           border: 1px solid var(--border-color);
           border-radius: 12px;
           padding: 16px;
           cursor: pointer;
           transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
           opacity: 0;
           transform: translateY(20px);
           animation: cardFadeIn 0.5s ease-out forwards;
       }

       .prompt-card:nth-child(n) { animation-delay: calc(0.05s * var(--card-index)); }

       @keyframes cardFadeIn {
           to {
               opacity: 1;
               transform: translateY(0);
           }
       }

       .prompt-card:hover {
           border-color: var(--primary-color);
           box-shadow: 0 8px 20px var(--shadow-color);
           transform: translateY(-4px) scale(1.02);
       }

       .prompt-card.selected {
           background: var(--bg-panel-secondary);
           border-color: var(--primary-color);
           box-shadow: 0 0 0 3px var(--shadow-color);
       }

       .prompt-name {
           font-size: var(--font-size-base);
           font-weight: 600;
           color: var(--text-dark);
           margin-bottom: 8px;
       }

       .prompt-desc {
           font-size: var(--font-size-sm);
           color: var(--text-light);
           line-height: 1.5;
           margin-bottom: 8px;
       }

       .prompt-tags {
           display: flex;
           gap: 6px;
           flex-wrap: wrap;
       }

       .prompt-tag {
           padding: 2px 8px;
           background: var(--bg-panel-secondary);
           border-radius: 4px;
           font-size: 12px;
           color: var(--text-light);
       }

       .prompt-actions {
           display: flex;
           gap: 8px;
           justify-content: flex-end;
           margin-top: 12px;
           opacity: 0;
           transition: opacity 0.2s;
       }

       .prompt-card:hover .prompt-actions {
           opacity: 1;
       }

       .prompt-action-btn {
           width: 32px;
           height: 32px;
           border: none;
           border-radius: 6px;
           background: var(--bg-panel-secondary);
           color: var(--text-light);
           cursor: pointer;
           display: flex;
           align-items: center;
           justify-content: center;
           transition: all 0.2s;
           font-size: 0;
       }

       .prompt-action-btn svg {
           width: 16px;
           height: 16px;
       }

       .prompt-action-btn:hover {
           background: var(--primary-color);
           color: white;
           transform: translateY(-1px);
       }

       .prompt-action-btn:nth-child(2):hover {
           background: #ef4444;
       }

       .modal-actions {
           display: flex;
           gap: 12px;
           justify-content: flex-end;
           margin-top: 24px;
       }

       .btn {
           padding: 10px 20px;
           border: none;
           border-radius: 8px;
           font-size: var(--font-size-sm);
           font-weight: 500;
           cursor: pointer;
           transition: all 0.2s;
       }

       .btn-cancel {
           background: var(--bg-content);
           color: var(--text-dark);
           border: 1px solid var(--border-color);
       }

       .btn-cancel:hover {
           background: var(--bg-panel-secondary);
       }

       .btn-confirm {
           background: var(--primary-color);
           color: var(--text-on-primary);
       }

       .btn-confirm:hover {
           background: var(--primary-color-hover);
           transform: translateY(-1px);
       }

       /* 创建提示词弹窗 */
       .form-group {
           margin-bottom: 20px;
           opacity: 0;
           transform: translateY(10px);
           animation: formFadeIn 0.4s ease-out forwards;
       }

       .form-group:nth-child(1) { animation-delay: 0.1s; }
       .form-group:nth-child(2) { animation-delay: 0.15s; }
       .form-group:nth-child(3) { animation-delay: 0.2s; }
       .form-group:nth-child(4) { animation-delay: 0.25s; }

       @keyframes formFadeIn {
           to {
               opacity: 1;
               transform: translateY(0);
           }
       }

       .form-label {
           display: block;
           font-size: var(--font-size-sm);
           font-weight: 500;
           color: var(--text-dark);
           margin-bottom: 8px;
       }

       .form-input,
       .form-textarea {
           width: 100%;
           padding: 12px;
           border: 1px solid var(--border-color);
           border-radius: 8px;
           background: var(--bg-content);
           color: var(--text-dark);
           font-size: var(--font-size-sm);
           transition: all 0.2s;
           font-family: inherit;
       }

       .form-textarea {
           min-height: 120px;
           resize: vertical;
       }

       .form-input:focus,
       .form-textarea:focus {
           outline: none;
           border-color: var(--primary-color);
           box-shadow: 0 0 0 3px var(--shadow-color);
       }

       .form-hint {
           font-size: 12px;
           color: var(--text-light);
           margin-top: 4px;
       }

       /* 模型选择弹窗 */
       .model-grid {
           display: grid;
           grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
           gap: 16px;
           margin-bottom: 20px;
       }

       .model-card {
           background: var(--bg-content);
           border: 2px solid var(--border-color);
           border-radius: 12px;
           padding: 20px;
           cursor: pointer;
           transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
           text-align: center;
           opacity: 0;
           transform: translateY(20px);
           animation: cardFadeIn 0.5s ease-out forwards;
       }

       .model-card:nth-child(n) { animation-delay: calc(0.08s * var(--card-index)); }

       .model-card:hover {
           border-color: var(--primary-color);
           box-shadow: 0 8px 20px var(--shadow-color);
           transform: translateY(-4px) scale(1.02);
       }

       .model-card.selected {
           background: var(--bg-panel-secondary);
           border-color: var(--primary-color);
           box-shadow: 0 0 0 3px var(--shadow-color);
       }

       .model-icon {
           font-size: 2.5rem;
           margin-bottom: 12px;
           opacity: 0.8;
       }

       .model-name {
           font-size: var(--font-size-base);
           font-weight: 600;
           color: var(--text-dark);
           margin-bottom: 8px;
       }

       .model-desc {
           font-size: var(--font-size-sm);
           color: var(--text-light);
           line-height: 1.4;
       }

       /* 提示内容区 */
       .prompt-hint-section {
           margin-top: 20px;
           padding: 16px;
           background: var(--bg-panel-secondary);
           border-radius: 12px;
           border: 1px solid var(--border-color);
           animation: fadeIn 0.3s ease-out;
       }

       .prompt-hint-title {
           font-size: var(--font-size-base);
           font-weight: 600;
           color: var(--text-dark);
           margin-bottom: 8px;
       }

       .prompt-hint-text {
           font-size: var(--font-size-sm);
           color: var(--text-light);
           line-height: 1.6;
       }

       /* 自定义滚动条 */
       ::-webkit-scrollbar {
           width: 6px;
           height: 6px;
       }

       ::-webkit-scrollbar-track {
           background: transparent;
       }

       ::-webkit-scrollbar-thumb {
           background: var(--border-color);
           border-radius: 3px;
           transition: background 0.2s;
       }

       ::-webkit-scrollbar-thumb:hover {
           background: var(--text-light);
       }

       /* 确认弹窗样式 */
       .custom-alert {
           display: none;
           position: fixed;
           inset: 0;
           background: rgba(0, 0, 0, 0);
           z-index: 2000;
           justify-content: center;
           align-items: center;
           transition: background-color 0.3s ease-out;
       }

       .custom-alert.show {
           background: rgba(0, 0, 0, 0.5);
       }

       .custom-alert-content {
           background: var(--bg-panel);
           padding: 24px 30px;
           border-radius: 12px;
           box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
           max-width: 400px;
           width: 90%;
           text-align: center;
           transform: scale(0.9) translateY(-20px);
           opacity: 0;
           transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
       }

       .custom-alert.show .custom-alert-content {
           transform: scale(1) translateY(0);
           opacity: 1;
       }

       .custom-alert-icon {
           width: 48px;
           height: 48px;
           margin: 0 auto 16px;
           border-radius: 50%;
           display: flex;
           align-items: center;
           justify-content: center;
           font-size: 24px;
       }

       .custom-alert-icon.success {
           background: var(--primary-color);
           color: white;
       }

       .custom-alert-icon.error {
           background: var(--primary-color);
           color: white;
       }

       .custom-alert-icon.info {
           background: var(--primary-color);
           color: white;
       }

       .custom-alert-icon.warning {
           background: var(--primary-color);
           color: white;
       }

       .custom-alert-message {
           font-size: var(--font-size-base);
           color: var(--text-dark);
           line-height: 1.5;
           margin-bottom: 20px;
           white-space: pre-line;
       }

       .custom-alert-buttons {
           display: flex;
           gap: 12px;
           justify-content: center;
       }

       .custom-alert-button {
           background: var(--primary-color);
           color: var(--text-on-primary);
           border: none;
           padding: 10px 24px;
           border-radius: 8px;
           font-size: var(--font-size-sm);
           font-weight: 500;
           cursor: pointer;
           transition: all 0.2s;
           min-width: 80px;
       }

       .custom-alert-button:hover {
           background: var(--primary-color-hover);
           transform: translateY(-1px);
       }

       .custom-alert-button:active {
           transform: translateY(0);
       }

       .custom-alert-button.secondary {
           background: var(--bg-panel-secondary);
           color: var(--text-dark);
       }

       .custom-alert-button.secondary:hover {
           background: var(--secondary-color);
           color: var(--text-on-primary);
       }

       /* 思考动画样式 */
        .thinking-indicator {
            color: var(--text-light);
            font-style: italic;
            display: inline-flex;
            align-items: center;
            gap: 2px;
            font-size: 14px;
        }

        /* 思考中消息的特殊样式 */
        .message.ai:has(.thinking-indicator) .message-content {
            max-width: 140px;
            padding: 6px 12px;
            background: var(--message-bg-ai);
            border-top-left-radius: 4px;
            line-height: 1.2;
            height: 40px;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .message.ai:has(.thinking-indicator) .message-text {
            font-size: 14px;
            display: flex;
            align-items: center;
        }

       .thinking-indicator .dot-1,
       .thinking-indicator .dot-2,
       .thinking-indicator .dot-3 {
           animation: thinking-dots 1.4s infinite;
           font-weight: bold;
       }

       .thinking-indicator .dot-1 {
           animation-delay: 0s;
       }

       .thinking-indicator .dot-2 {
           animation-delay: 0.2s;
       }

       .thinking-indicator .dot-3 {
           animation-delay: 0.4s;
       }

       @keyframes thinking-dots {
           0%, 60%, 100% {
               opacity: 0.3;
               transform: scale(1);
           }
           30% {
               opacity: 1;
               transform: scale(1.2);
           }
       }

       /* 加载按钮样式 */
       .loading-dots {
           display: inline-flex;
           gap: 2px;
       }

       .loading-dots::after {
           content: '...';
           animation: loading-dots 1.4s infinite;
       }

       @keyframes loading-dots {
           0%, 60%, 100% {
               opacity: 0.3;
           }
           30% {
               opacity: 1;
           }
       }

       /* 响应式布局 - 移动端适配 */
       @media (max-width: 768px) {
           .messages-container {
               padding: 20px 16px;
           }
           
           .messages-wrapper {
               max-width: 100%;
               gap: 24px;
           }
           
           .message-content {
               max-width: 90%;
               padding: 12px 16px;
           }
           
           .input-container {
               padding: 16px;
           }
           
           .input-wrapper {
               max-width: 350px;
               gap: 6px;
           }
           
           .right-panel {
               display: none;
           }
           
           .dialogue-header {
               padding: 0 16px;
               flex-wrap: wrap;
               gap: 8px;
           }
           
           .header-left, .header-right {
               gap: 8px;
           }
           
           .btn-select-prompt, .btn-create-prompt, .header-btn {
               padding: 6px 12px;
               font-size: 12px;
           }
       }
       
       /* 平板端适配 */
       @media (min-width: 769px) and (max-width: 1024px) {
           .messages-container {
               padding: 25px 24px;
           }
           
           .messages-wrapper {
               max-width: 1000px;
               gap: 28px;
           }
           
           .message-content {
               max-width: 85%;
               padding: 14px 18px;
           }
           
           .input-container {
               padding: 18px 24px;
           }
           
           .input-wrapper {
               max-width: 500px;
           }
           
           .right-panel {
               width: 240px;
           }
       }
       
       /* 大屏幕适配 */
       @media (min-width: 1400px) {
           .messages-container {
               padding: 40px 60px;
           }
           
           .messages-wrapper {
               max-width: 1400px;
               gap: 36px;
           }
           
           .message-content {
               max-width: 75%;
               padding: 18px 24px;
           }
           
           .input-container {
               padding: 24px 60px;
           }
           
           .input-wrapper {
               max-width: 600px;
           }
           
           .right-panel {
               width: 320px;
           }
       }
   </style>
</head>
<body>

   <!-- 鼠标感应区域 -->
   <div class="sidebar-trigger" id="sidebarTrigger"></div>

   <!-- 左侧导航栏包装器 -->
   <div class="sidebar-wrapper collapsed" id="sidebarWrapper">
       <!-- 左侧导航栏 -->
       <nav class="sidebar" id="sidebar">
           <div class="sidebar-content">
               <a href="#" class="user-avatar">
                   <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="User Avatar">
               </a>
               <div class="nav-group">
                   <div class="nav-item" title="首页" onclick="window.location.href='首页.html'">
                       <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                           <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                       </svg>
                       <span class="nav-item-text">首页</span>
                   </div>
                   <div class="nav-item" title="书架" onclick="window.location.href='书架.html'">
                       <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                           <path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/>
                       </svg>
                       <span class="nav-item-text">书架</span>
                   </div>
                   <div class="nav-item" title="创意" onclick="window.location.href='创意.html'">
                       <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                           <path d="M14.06,9.02l0.91,0.91L5.66,19.24L4.75,18.33L14.06,9.02 M17.66,3c-0.25,0-0.51,0.1-0.7,0.29l-1.83,1.83l3.75,3.75l1.83-1.83c0.39-0.39,0.39-1.02,0-1.41l-2.34-2.34C18.17,3.09,17.92,3,17.66,3L17.66,3z M12.06,6.19L3,15.25V19.24h3.99l9.06-9.06L12.06,6.19z"/>
                       </svg>
                       <span class="nav-item-text">创意</span>
                   </div>
                   <div class="nav-item active" title="对话" onclick="window.location.href='对话.html'">
                       <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                           <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                       </svg>
                       <span class="nav-item-text">对话</span>
                   </div>
                   <div class="nav-item" title="模拟" onclick="window.location.href='模拟.html'">
                       <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                           <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z M12 4c1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3 1.34 3 3 3z"/>
                       </svg>
                       <span class="nav-item-text">模拟</span>
                   </div>
               </div>
               <div class="sidebar-footer nav-group">
                   <div class="nav-item" title="教程" onclick="window.location.href='教程.html'">
                       <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                           <path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/>
                       </svg>
                       <span class="nav-item-text">教程</span>
                   </div>
                   <div class="nav-item" title="邀请" onclick="window.location.href='邀请.html'">
                       <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                           <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                       </svg>
                       <span class="nav-item-text">邀请</span>
                   </div>
                   <div class="nav-item" title="夜间模式" onclick="toggleNightMode()">
                       <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                           <path d="M9.37 5.51C9.19 6.15 9.1 6.82 9.1 7.5c0 4.08 3.32 7.4 7.4 7.4.68 0 1.35-.09 1.99-.27C17.45 17.19 14.93 19 12 19c-3.86 0-7-3.14-7-7 0-2.93 1.81-5.45 4.37-6.49z"/>
                       </svg>
                       <span class="nav-item-text">夜间</span>
                   </div>
               </div>
           </div>
       </nav>
   </div>

   <!-- 主内容区 -->
   <main class="main-content">
       <!-- 对话界面 -->
       <div class="dialogue-container">
           <!-- 顶部工具栏 -->
           <div class="dialogue-header">
               <div class="header-left">
                   <div class="prompt-selector">
                       <label>提示词:</label>
                       <button class="btn-select-prompt" onclick="openPromptMarket()">
                           <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                               <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                           </svg>
                           选择提示词
                       </button>
                   </div>
                   <button class="btn-create-prompt" onclick="openCreatePromptModal()">
                       <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                           <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                       </svg>
                       创建提示词
                   </button>
               </div>
               <div class="header-right">
                   <button class="header-btn switch-model-btn" onclick="openModelSelect()" id="headerModelBtn">
                       <svg viewBox="0 0 24 24" fill="currentColor">
                           <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                       </svg>
                       切换模型
                   </button>
                   <button class="header-btn" onclick="saveDialogue()">保存对话</button>
                   <button class="header-btn" onclick="clearChat()">清空对话</button>
                   <button class="header-btn" onclick="exportDialogue()">导出对话</button>
               </div>
           </div>

           <!-- 消息显示区域 -->
           <div class="messages-container" id="messagesContainer">
               <!-- 空状态 -->
               <div class="empty-state" id="emptyState">
                   <div class="empty-icon">💬</div>
                   <div class="empty-title">开始一段新的对话</div>
                   <div class="empty-desc">选择一个提示词或直接输入您的问题，我将为您提供帮助</div>
               </div>

               <!-- 消息列表 -->
               <div class="messages-wrapper" id="messagesWrapper" style="display: none;">
                   <!-- 消息会动态添加到这里 -->
               </div>
           </div>

           <!-- 输入区域 -->
           <div class="input-container">
               <div class="input-wrapper">

                   <div class="input-box">
                       <textarea 
                           class="input-textarea" 
                           id="messageInput"
                           placeholder="输入您的消息..."
                           rows="1"
                       ></textarea>
                       <div class="input-actions">
                           <button class="input-action-btn" title="上传文件" onclick="triggerFileUpload()">
                               <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                   <path d="M16.5 6v11.5c0 2.21-1.79 4-4 4s-4-1.79-4-4V5c0-1.38 1.12-2.5 2.5-2.5s2.5 1.12 2.5 2.5v10.5c0 .55-.45 1-1 1s-1-.45-1-1V6H10v9.5c0 1.38 1.12 2.5 2.5 2.5s2.5-1.12 2.5-2.5V5c0-2.21-1.79-4-4-4S7 2.79 7 5v12.5c0 3.04 2.46 5.5 5.5 5.5s5.5-2.46 5.5-5.5V6h-1.5z"/>
                               </svg>
                           </button>
                       </div>
                   </div>
                   <button class="send-button" id="sendButton">
                       <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                           <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                       </svg>
                       发送
                   </button>
               </div>
               <!-- 隐藏的文件输入 -->
               <input type="file" class="file-input-hidden" id="fileInput" multiple>
           </div>
       </div>

       <!-- 右侧面板 -->
       <div class="right-panel">
           <!-- 标签切换 -->
           <div class="panel-tabs">
               <button class="panel-tab active" onclick="switchTab('history')">对话历史</button>
               <button class="panel-tab" onclick="switchTab('attachments')">关联附件</button>
           </div>

           <!-- 面板内容包装器 -->
           <div class="panel-content-wrapper">
               <!-- 对话历史内容 -->
               <div class="panel-content active" id="historyPanel">
                   <button class="new-chat-btn" onclick="newChat()">
                       <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                           <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                       </svg>
                       新建对话
                   </button>
                   <div class="history-list" id="historyList">
                       <!-- 历史记录会动态添加到这里 -->
                   </div>
               </div>

               <!-- 关联附件内容 -->
               <div class="panel-content" id="attachmentsPanel">
                   <div class="attachments-content">
                       <div class="attachment-group">
                           <label class="attachment-label">便签</label>
                           <div style="display: flex; gap: 8px;">
                               <select class="attachment-select" style="flex: 1;">
                                   <option value="">选择便签...</option>
                                   <option>重要设定笔记</option>
                                   <option>灵感记录</option>
                                   <option>情节大纲</option>
                               </select>
                               <button class="btn-secondary" onclick="openNoteManager()" style="padding: 8px 12px; font-size: 12px;">管理</button>
                           </div>
                           <div class="attachment-hint">关联便签内容到对话</div>
                       </div>
                       <div class="attachment-group">
                           <label class="attachment-label">人设卡</label>
                           <div style="display: flex; gap: 8px;">
                               <select class="attachment-select" style="flex: 1;">
                                   <option value="">选择人设...</option>
                                   <option>主角设定</option>
                                   <option>配角设定</option>
                                   <option>反派设定</option>
                               </select>
                               <button class="btn-secondary" onclick="openCharacterManager()" style="padding: 8px 12px; font-size: 12px;">管理</button>
                           </div>
                           <div class="attachment-hint">添加角色设定到上下文</div>
                       </div>
                       <div class="attachment-group">
                           <label class="attachment-label">章节</label>
                           <div style="display: flex; gap: 8px;">
                               <select class="attachment-select" style="flex: 1;">
                                   <option value="">选择章节...</option>
                                   <option>第1章 开端</option>
                                   <option>第2章 发展</option>
                                   <option>第3章 高潮</option>
                               </select>
                               <button class="btn-secondary" onclick="openChapterManager()" style="padding: 8px 12px; font-size: 12px;">管理</button>
                           </div>
                           <div class="attachment-hint">引用章节内容</div>
                       </div>
                       <div class="attachment-group">
                           <label class="attachment-label">知识卡</label>
                           <div style="display: flex; gap: 8px;">
                               <select class="attachment-select" style="flex: 1;">
                                   <option value="">选择知识卡...</option>
                                   <option>世界观设定</option>
                                   <option>魔法体系</option>
                                   <option>地理设定</option>
                               </select>
                               <button class="btn-secondary" onclick="openKnowledgeManager()" style="padding: 8px 12px; font-size: 12px;">管理</button>
                           </div>
                           <div class="attachment-hint">添加背景知识</div>
                       </div>

                   </div>
               </div>
           </div>
       </div>
   </main>

   


   <!-- 提示词市场弹窗 -->
   <div class="modal" id="promptMarketModal">
       <div class="modal-content">
           <div class="modal-header">
               <h2 class="modal-title">提示词市场</h2>
               <p class="modal-desc">选择合适的提示词，让AI更好地理解您的需求</p>
           </div>
           
           <!-- 分类标签 -->
           <div class="prompt-categories">
               <button class="category-btn active" onclick="filterPrompts('all')">全部</button>
               <button class="category-btn" onclick="filterPrompts('my')">我的</button>
               <button class="category-btn" onclick="filterPrompts('longform')">长篇创作</button>
               <button class="category-btn" onclick="filterPrompts('shortform')">短篇精品</button>
               <button class="category-btn" onclick="filterPrompts('creative')">脑洞创意</button>
               <button class="category-btn" onclick="filterPrompts('plot')">情节优化</button>
               <button class="category-btn" onclick="filterPrompts('character')">人物塑造</button>
               <button class="category-btn" onclick="filterPrompts('writing')">文笔润色</button>
           </div>

           <!-- 提示词网格 -->
           <div class="prompt-grid" id="promptGrid">
               <!-- 内容由JS动态生成 -->
           </div>

           <div class="modal-actions">
               <button class="btn btn-cancel" onclick="closePromptMarket()">取消</button>
               <button class="btn btn-confirm" onclick="selectPrompt()">确认选择</button>
           </div>
       </div>
   </div>

   <!-- 创建提示词弹窗 -->
   <div class="modal" id="createPromptModal">
       <div class="modal-content">
           <div class="modal-header">
               <h2 class="modal-title">创建自定义提示词</h2>
               <p class="modal-desc">创建您专属的提示词，让AI更好地理解您的需求</p>
           </div>
           <form id="promptForm">
               <div class="form-group">
                   <label class="form-label">提示词名称</label>
                   <input type="text" class="form-input" id="promptNameInput" placeholder="例如：小说创作助手" required>
                   <div class="form-hint">给您的提示词起一个易于识别的名称</div>
               </div>
               <div class="form-group">
                   <label class="form-label">提示词内容</label>
                   <textarea class="form-textarea" id="promptContentInput" placeholder="请输入提示词的具体内容..." required></textarea>
                   <div class="form-hint">详细描述AI的角色、任务和输出要求</div>
               </div>
               <div class="form-group">
                   <label class="form-label">使用场景说明（可选）</label>
                   <input type="text" class="form-input" id="promptSceneInput" placeholder="简要说明这个提示词的使用场景">
               </div>
               <div class="form-group">
                   <label class="form-label">操作提示（可选）</label>
                   <textarea class="form-textarea" id="promptHintInput" placeholder="例如：请收到AI响应后，根据提示进行操作"></textarea>
                   <div class="form-hint">用户收到AI响应后应该如何操作的提示说明</div>
               </div>
               <div class="modal-actions">
                   <button type="button" class="btn btn-cancel" onclick="closeCreatePromptModal()">取消</button>
                   <button type="submit" class="btn btn-confirm">保存提示词</button>
               </div>
           </form>
       </div>
   </div>

   <!-- 修改提示词弹窗 -->
   <div class="modal" id="editPromptModal">
       <div class="modal-content">
           <div class="modal-header">
               <h2 class="modal-title">修改提示词</h2>
               <p class="modal-desc">修改您的自定义提示词内容</p>
           </div>
           <form id="editPromptForm">
               <input type="hidden" id="editPromptId">
               <div class="form-group">
                   <label class="form-label">提示词名称</label>
                   <input type="text" class="form-input" id="editPromptNameInput" placeholder="例如：小说创作助手" required>
                   <div class="form-hint">给您的提示词起一个易于识别的名称</div>
               </div>
               <div class="form-group">
                   <label class="form-label">提示词内容</label>
                   <textarea class="form-textarea" id="editPromptContentInput" placeholder="请输入提示词的具体内容..." required></textarea>
                   <div class="form-hint">详细描述AI的角色、任务和输出要求</div>
               </div>
               <div class="form-group">
                   <label class="form-label">使用场景说明（可选）</label>
                   <input type="text" class="form-input" id="editPromptSceneInput" placeholder="简要说明这个提示词的使用场景">
               </div>
               <div class="form-group">
                   <label class="form-label">操作提示（可选）</label>
                   <textarea class="form-textarea" id="editPromptHintInput" placeholder="例如：请收到AI响应后，根据提示进行操作"></textarea>
                   <div class="form-hint">用户收到AI响应后应该如何操作的提示说明</div>
               </div>
               <div class="modal-actions">
                   <button type="button" class="btn btn-cancel" onclick="closeEditPromptModal()">取消</button>
                   <button type="submit" class="btn btn-confirm">保存修改</button>
               </div>
           </form>
       </div>
   </div>

   <!-- 模型选择弹窗 -->
   <div class="modal" id="modelSelectModal">
       <div class="modal-content">
           <div class="modal-header">
               <h2 class="modal-title">选择AI模型</h2>
               <p class="modal-desc">不同的模型有不同的特长，选择最适合您需求的模型</p>
           </div>
           
           <!-- 模型网格 -->
           <div class="model-grid" id="modelGrid">
               <!-- 内容由JS动态生成 -->
           </div>

           <!-- 提示内容区 -->
           <div class="prompt-hint-section" id="promptHintSection" style="display: none;">
               <div class="prompt-hint-title">操作提示</div>
               <div class="prompt-hint-text" id="promptHintText">请收到AI响应后，根据提示进行操作</div>
           </div>

           <div class="modal-actions">
               <button class="btn btn-cancel" onclick="closeModelSelect()">取消</button>
               <button class="btn btn-confirm" onclick="confirmModelSelect()">确认选择</button>
           </div>
       </div>
   </div>

   <script>
       // --- 主题同步功能 ---
       function initTheme() {
           const savedTheme = localStorage.getItem('theme') || 'default';
           document.documentElement.setAttribute('data-theme', savedTheme);
       }

       // 初始化主题
       initTheme();

       // 监听localStorage变化，实现跨页面主题同步
       window.addEventListener('storage', (e) => {
           if (e.key === 'theme') {
               document.documentElement.setAttribute('data-theme', e.newValue);
           }
       });

       // 夜间模式快捷切换
       function toggleNightMode() {
           const currentTheme = document.documentElement.getAttribute('data-theme');
           const newTheme = currentTheme === 'dark' ? 'default' : 'dark';
           document.documentElement.setAttribute('data-theme', newTheme);
           localStorage.setItem('theme', newTheme);
       }

       // --- 对话历史管理 ---
       let dialogueHistory = JSON.parse(localStorage.getItem('dialogueHistory')) || [];
       let currentDialogueId = null;
       let currentMessages = [];
       let currentModel = '逻辑精准'; // 默认模型
       let currentPrompt = null; // 当前选中的提示词
       let promptHintText = '请收到AI响应后，根据提示进行操作'; // 默认操作提示
       let isSelectingModelForPrompt = false; // 是否正在为提示词选择模型
       
       // API配置
       const API_KEYS = [
           'sk-IfjKldk4NVW9Xv3YAb3888319a3c4c359a91001675F0D472',
           'sk-52LhmKStDRjcLEag71721dD970754681Ba3eA73127504f38',
           'sk-b6hs0cC3MDrLqtnm05E8BbEd38A44a278e19674f39828a2d',
           'sk-WEgHx5YWScgwJJyiFbBf2a1aF18746558d0183B8D4D82b87',
           'sk-HDjwnNTX5iN7yPe89aD26cE5FdE94bCc97C0A14c70C00f37',
           'sk-Xza4J2v9EzcdkNupD6F9551b61D847A790E8B3C96533Ba3d',
           'sk-fBRUpiszWkUkBaVA8687Fd52C44f4c01BcC940Ee44D4Dd03',
           'sk-T5zmOVGwhyt0cEWFA4710b38E0344e769c2eE09c023fB768',
           'sk-dNru55Cuxppsu08q53190cE0669242C3B99b266d7dDf2c77',
           'sk-ENdJAbu1t4vCaCYSAfA51fDaE1184f6a823b57B2104dB804'
       ];
       
       // ========== API接口配置 ==========
       // 主要API接口地址 - 用于AI对话的核心接口
       const API_BASE_URL = 'https://api.bijianchuanqi.cn';
       
       // 模型映射 - 将用户友好的模型名称映射到API模型ID
       const MODEL_MAPPING = {
           '细腻贴合': 'doubao-seed-1-6-250615',
           '逻辑精准': 'gemini-2.5-pro',
           '灵活创意': 'claude-3-7-sonnet-20250219',
           '稳定版': 'gemini-2.5-flash',
           '综合版': 'claude-sonnet-4-20250514',
           '测试版': 'claude-opus-4-20250514'
       };
       
       // API密钥轮换索引
       let currentApiKeyIndex = 0;
       
       // 获取下一个API密钥
       function getNextApiKey() {
           const key = API_KEYS[currentApiKeyIndex];
           currentApiKeyIndex = (currentApiKeyIndex + 1) % API_KEYS.length;
           return key;
       }

       // 生成唯一ID
       function generateId() {
           return Date.now().toString(36) + Math.random().toString(36).substr(2);
       }

       // 自动生成对话标题
       function generateTitle(messages) {
           if (messages.length === 0) return '新对话';
           
           // 优先使用AI的第一条消息作为标题
           const firstAiMessage = messages.find(m => m.sender === 'ai');
           if (firstAiMessage) {
               // 提取AI消息的前30个字符作为摘要
               let summary = firstAiMessage.text.replace(/[\n\r]/g, ' ').trim();
               return summary.slice(0, 30) + (summary.length > 30 ? '...' : '');
           }
           
           // 如果没有AI消息，使用固定名称+序列号
           const existingDialogues = dialogueHistory.filter(d => d.title.startsWith('对话'));
           const nextNumber = existingDialogues.length + 1;
           return `对话 ${nextNumber}`;
       }

       // 保存当前对话到历史
       function saveCurrentDialogue(forceSave = false) {
           if (currentMessages.length === 0) return;
           
           // 只有在强制保存或已存在的对话时才保存
           if (!forceSave && !currentDialogueId) return;
           
           // 创建消息副本，确保保存的用户消息不包含提示词
           const messagesToSave = currentMessages.map(msg => {
               if (msg.sender === 'user' && msg.hasPrompt) {
                   // 对于包含提示词的用户消息，只保存处理后的文本
                   return {
                       ...msg,
                       text: msg.text, // 已经是处理后的文本（不包含提示词）
                       originalText: undefined // 不保存原始输入到历史记录
                   };
               }
               return msg;
           });
           
           const dialogue = {
               id: currentDialogueId || generateId(),
               title: generateTitle(currentMessages),
               messages: messagesToSave,
               timestamp: new Date().getTime()
           };
           
           // 如果是已存在的对话，更新它
           const existingIndex = dialogueHistory.findIndex(d => d.id === currentDialogueId);
           if (existingIndex !== -1) {
               dialogueHistory[existingIndex] = dialogue;
           } else if (forceSave) {
               // 只有在强制保存时才添加新对话到历史记录
               dialogueHistory.unshift(dialogue);
               currentDialogueId = dialogue.id; // 设置当前对话ID
           }
           
           // 限制历史记录数量为50条
           if (dialogueHistory.length > 50) {
               dialogueHistory = dialogueHistory.slice(0, 50);
           }
           
           localStorage.setItem('dialogueHistory', JSON.stringify(dialogueHistory));
           renderHistoryList();
       }

       // 渲染历史记录列表
       function renderHistoryList() {
           const historyList = document.getElementById('historyList');
           historyList.innerHTML = '';
           
           dialogueHistory.forEach((dialogue, index) => {
               const historyItem = document.createElement('div');
               historyItem.className = 'history-item';
               if (dialogue.id === currentDialogueId) {
                   historyItem.classList.add('active');
               }
               
               const timeAgo = getTimeAgo(dialogue.timestamp);
               
               historyItem.innerHTML = `
                   <div class="history-title">${dialogue.title}</div>
                   <div class="history-time">${timeAgo}</div>
                   <button class="history-delete" onclick="deleteHistory('${dialogue.id}')" title="删除">
                       <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                           <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                       </svg>
                   </button>
               `;
               
               historyItem.addEventListener('click', (e) => {
                   if (!e.target.closest('.history-delete')) {
                       loadDialogue(dialogue.id);
                   }
               });
               
               historyList.appendChild(historyItem);
           });
       }

       // 计算时间差
       function getTimeAgo(timestamp) {
           const now = new Date().getTime();
           const diff = now - timestamp;
           const minutes = Math.floor(diff / 60000);
           const hours = Math.floor(diff / 3600000);
           const days = Math.floor(diff / 86400000);
           
           if (minutes < 1) return '刚刚';
           if (minutes < 60) return `${minutes}分钟前`;
           if (hours < 24) return `${hours}小时前`;
           if (days < 7) return `${days}天前`;
           return new Date(timestamp).toLocaleDateString('zh-CN');
       }

       // 加载对话
       function loadDialogue(dialogueId) {
           // 移除自动保存，只有用户主动保存或新建对话时才保存
           // saveCurrentDialogue();
           
           const dialogue = dialogueHistory.find(d => d.id === dialogueId);
           if (dialogue) {
               currentDialogueId = dialogue.id;
               currentMessages = [...dialogue.messages];
               
               // 清空界面并重新渲染消息
               const messagesWrapper = document.getElementById('messagesWrapper');
               messagesWrapper.innerHTML = '';
               
               currentMessages.forEach(msg => {
                   addMessageToUI(msg.text, msg.sender, msg.time, msg.wordCount, msg.model);
               });
               
               // 显示消息区域
               document.getElementById('emptyState').style.display = 'none';
               messagesWrapper.style.display = 'flex';
               
               // 更新历史列表的激活状态
               renderHistoryList();
           }
       }

       // 删除历史记录
       async function deleteHistory(dialogueId) {
           const confirmed = await showCustomConfirm('确定要删除这条对话记录吗？', '删除对话');
           if (confirmed) {
               dialogueHistory = dialogueHistory.filter(d => d.id !== dialogueId);
               localStorage.setItem('dialogueHistory', JSON.stringify(dialogueHistory));
               
               // 如果删除的是当前对话，清空界面
               if (dialogueId === currentDialogueId) {
                   currentDialogueId = null;
                   currentMessages = [];
                   document.getElementById('messagesWrapper').innerHTML = '';
                   document.getElementById('emptyState').style.display = 'flex';
                   document.getElementById('messagesWrapper').style.display = 'none';
               }
               
               renderHistoryList();
           }
       }

       // --- 侧边栏自动隐藏功能 ---
       const sidebarWrapper = document.getElementById('sidebarWrapper');
       const sidebarTrigger = document.getElementById('sidebarTrigger');
       let sidebarTimer;

       function showSidebar() {
           clearTimeout(sidebarTimer);
           sidebarWrapper.classList.remove('collapsed');
       }

       function hideSidebar() {
           clearTimeout(sidebarTimer);
           sidebarTimer = setTimeout(() => {
               sidebarWrapper.classList.add('collapsed');
           }, 300);
       }

       sidebarTrigger.addEventListener('mouseenter', showSidebar);
       sidebarWrapper.addEventListener('mouseenter', showSidebar);
       sidebarWrapper.addEventListener('mouseleave', hideSidebar);

       // --- 对话功能 ---
       const messageInput = document.getElementById('messageInput');
       const sendButton = document.getElementById('sendButton');
       const messagesWrapper = document.getElementById('messagesWrapper');
       const emptyState = document.getElementById('emptyState');

       // 自动调整输入框高度
       messageInput.addEventListener('input', function() {
           this.style.height = 'auto';
           this.style.height = Math.min(this.scrollHeight, 120) + 'px';
       });

       // 发送消息
       async function sendMessage() {
           const text = messageInput.value.trim();
           if (!text) return;

           // 如果是新对话，创建新的对话ID
           if (!currentDialogueId) {
               currentDialogueId = generateId();
           }

           // 隐藏空状态
           if (emptyState.style.display !== 'none') {
               emptyState.style.display = 'none';
               messagesWrapper.style.display = 'flex';
           }

           // 检查是否包含提示词内容
           let userDisplayText = text;
           let isPromptIncluded = false;
           
           // 如果有选中的提示词，检查用户输入是否包含提示词
           if (currentPrompt && currentPrompt.content) {
               // 简单检查：如果用户输入明显比提示词长很多，可能包含了提示词
               if (text.length > currentPrompt.content.length * 0.8) {
                   isPromptIncluded = true;
                   // 尝试提取用户的实际问题（去除提示词部分）
                   // 这里使用简单的启发式方法：查找常见的分隔符后的内容
                   const separators = ['\n\n用户问题：', '\n\n问题：', '\n\n请回答：', '\n\n我的问题是：'];
                   for (const sep of separators) {
                       if (text.includes(sep)) {
                           userDisplayText = text.split(sep)[1] || text;
                           break;
                       }
                   }
                   // 如果没有找到分隔符，使用最后一段作为用户问题
                   if (userDisplayText === text) {
                       const paragraphs = text.split('\n\n');
                       if (paragraphs.length > 1) {
                           userDisplayText = paragraphs[paragraphs.length - 1];
                       }
                   }
               }
           }

           // 添加用户消息到数组（只保存用户的实际问题，不包含提示词）
           const userMessage = {
               id: generateId(),
               sender: 'user',
               text: userDisplayText, // 只保存用户的实际问题
               originalText: text, // 保存完整的原始输入（包含提示词）用于API调用
               time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
               wordCount: countChineseCharacters(userDisplayText),
               model: currentModel,
               hasPrompt: isPromptIncluded
           };
           
           currentMessages.push(userMessage);
           // 在UI中只显示用户的实际问题
           addMessageToUI(userDisplayText, 'user', userMessage.time, userMessage.wordCount);

           // 清空输入框
           messageInput.value = '';
           messageInput.style.height = 'auto';

           // 禁用发送按钮并显示加载状态
           sendButton.disabled = true;
           const originalButtonText = sendButton.innerHTML;
           sendButton.innerHTML = '<span class="loading-dots"></span>';
           
           // 添加思考动画消息
           const thinkingMessageId = 'thinking-' + Date.now();
           const thinkingDiv = document.createElement('div');
           thinkingDiv.className = 'message ai';
           thinkingDiv.id = thinkingMessageId;
           thinkingDiv.innerHTML = `
               <div class="message-avatar">AI</div>
               <div class="message-content">
                   <div class="message-text">
                       <span class="thinking-indicator">思考中<span class="dot-1">.</span><span class="dot-2">.</span><span class="dot-3">.</span></span>
                   </div>
               </div>
           `;
           messagesWrapper.appendChild(thinkingDiv);
           messagesWrapper.scrollTop = messagesWrapper.scrollHeight;

           try {
               // ========== 调用真实API接口 ==========
               const aiResponse = await generateAIResponse(text);
               
               // 移除思考动画
               const thinkingElement = document.getElementById(thinkingMessageId);
               if (thinkingElement) {
                   thinkingElement.remove();
               }
               const aiMessage = {
                   id: generateId(),
                   sender: 'ai',
                   text: aiResponse,
                   time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
                   wordCount: countChineseCharacters(aiResponse),
                   model: currentModel
               };
               
               currentMessages.push(aiMessage);
               addMessageToUI(aiResponse, 'ai', aiMessage.time, aiMessage.wordCount, currentModel);
               
               // 移除自动保存，只有用户主动保存或新建对话时才保存
           } catch (error) {
               console.error('发送消息失败:', error);
               
               // 显示错误消息
               const errorMessage = {
                   id: generateId(),
                   sender: 'ai',
                   text: '抱歉，消息发送失败。请检查网络连接后重试。',
                   time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
                   wordCount: 0,
                   model: currentModel
               };
               
               currentMessages.push(errorMessage);
               addMessageToUI(errorMessage.text, 'ai', errorMessage.time, errorMessage.wordCount, currentModel);
           } finally {
               // 恢复发送按钮
               sendButton.disabled = false;
               sendButton.innerHTML = originalButtonText;
               
               // 确保移除思考动画（如果还存在）
               const thinkingElement = document.getElementById(thinkingMessageId);
               if (thinkingElement) {
                   thinkingElement.remove();
               }
           }
       }

       // 按照中文写作习惯统计字符数量的函数
        function countChineseCharacters(text) {
            // 如果输入为空或undefined，返回0
            if (!text || typeof text !== 'string') {
                return 0;
            }
            
            // 创建临时DOM元素来转换为纯文本
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = text;
            
            // 移除所有SVG元素
            const svgElements = tempDiv.querySelectorAll('svg');
            svgElements.forEach(svg => svg.remove());
            
            // 移除所有按钮元素
            const buttonElements = tempDiv.querySelectorAll('button');
            buttonElements.forEach(btn => btn.remove());
            
            // 移除所有操作相关的元素
            const actionElements = tempDiv.querySelectorAll('.message-actions, .message-action-btn, .message-usage');
            actionElements.forEach(elem => elem.remove());
            
            // 获取纯文本内容
            let plainText = tempDiv.textContent || tempDiv.innerText || '';
            
            // 更全面的表情符号和特殊符号移除
            // 基本表情符号
            plainText = plainText.replace(/[\u{1F600}-\u{1F64F}]/gu, ''); // 表情符号
            plainText = plainText.replace(/[\u{1F300}-\u{1F5FF}]/gu, ''); // 杂项符号和象形文字
            plainText = plainText.replace(/[\u{1F680}-\u{1F6FF}]/gu, ''); // 交通和地图符号
            plainText = plainText.replace(/[\u{1F700}-\u{1F77F}]/gu, ''); // 炼金术符号
            plainText = plainText.replace(/[\u{1F780}-\u{1F7FF}]/gu, ''); // 几何形状扩展
            plainText = plainText.replace(/[\u{1F800}-\u{1F8FF}]/gu, ''); // 补充箭头-C
            plainText = plainText.replace(/[\u{1F900}-\u{1F9FF}]/gu, ''); // 补充符号和象形文字
            plainText = plainText.replace(/[\u{1FA00}-\u{1FA6F}]/gu, ''); // 象棋符号
            plainText = plainText.replace(/[\u{1FA70}-\u{1FAFF}]/gu, ''); // 扩展A符号和象形文字
            plainText = plainText.replace(/[\u{1F000}-\u{1F02F}]/gu, ''); // 麻将牌
            plainText = plainText.replace(/[\u{1F030}-\u{1F093}]/gu, ''); // 多米诺骨牌
            plainText = plainText.replace(/[\u{1F0A0}-\u{1F0FF}]/gu, ''); // 扑克牌
            
            // 国旗和区域指示符
            plainText = plainText.replace(/[\u{1F1E0}-\u{1F1FF}]/gu, ''); // 国旗
            
            // 杂项技术符号
            plainText = plainText.replace(/[\u{2300}-\u{23FF}]/gu, ''); // 杂项技术
            plainText = plainText.replace(/[\u{2400}-\u{243F}]/gu, ''); // 控制图片
            plainText = plainText.replace(/[\u{2440}-\u{245F}]/gu, ''); // 光学字符识别
            
            // 装饰符号
            plainText = plainText.replace(/[\u{2700}-\u{27BF}]/gu, ''); // 装饰符号
            
            // 杂项符号
            plainText = plainText.replace(/[\u{2600}-\u{26FF}]/gu, ''); // 杂项符号
            plainText = plainText.replace(/[\u{2B00}-\u{2BFF}]/gu, ''); // 杂项符号和箭头
            
            // 变体选择器和组合字符
            plainText = plainText.replace(/[\u{FE00}-\u{FE0F}]/gu, ''); // 变体选择器
            plainText = plainText.replace(/[\u{E0100}-\u{E01EF}]/gu, ''); // 变体选择器补充
            
            // 零宽字符
            plainText = plainText.replace(/[\u200B-\u200D\uFEFF]/g, ''); // 零宽字符
            plainText = plainText.replace(/[\u2060-\u2064]/g, ''); // 不可见格式字符
            
            // 移除常见的特殊符号和操作文字
            plainText = plainText.replace(/[★☆♠♥♦♣♤♡♢♧]/g, '');
            plainText = plainText.replace(/[©®™]/g, '');
            plainText = plainText.replace(/[℃℉°]/g, ''); // 温度和度数符号
            plainText = plainText.replace(/[①②③④⑤⑥⑦⑧⑨⑩]/g, ''); // 圆圈数字
            plainText = plainText.replace(/[⚠⚡⭐]/g, ''); // 警告、闪电、星星
            
            // 移除操作按钮相关文字
            plainText = plainText.replace(/复制|重新生成|翻译|删除|已复制|原文/g, '');
            
            // 移除多余的空白字符
            plainText = plainText.replace(/\s+/g, ' ').trim();
            
            let count = 0;
            for (let char of plainText) {
                // 中文字符（包括中文标点）
                if (/[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff\u3040-\u309f\u30a0-\u30ff]/.test(char)) {
                    count++;
                }
                // 英文字母和数字
                else if (/[a-zA-Z0-9]/.test(char)) {
                    count++;
                }
                // 基本标点符号（更严格的筛选）
                else if (/[,.!?;:()\[\]{}"'`~@#$%^&*+=|\\/_-]/.test(char)) {
                    count++;
                }
            }
            return count;
        }

       // 添加消息到UI
       function addMessageToUI(text, sender, time, wordCount, model, isPromptContent = false) {
           const messageDiv = document.createElement('div');
           messageDiv.className = `message ${sender}`;
           messageDiv.dataset.messageId = generateId();
           
           const avatarText = sender === 'user' ? '我' : 'AI';
           const modelDisplay = model || currentModel;
           
           // 如果是提示词内容，完全隐藏
           let displayText = text;
           if (isPromptContent && sender === 'user') {
               // 完全隐藏提示词内容，不显示任何信息
               return;
           } else if (sender === 'ai') {
               // AI内容智能排版
               displayText = formatAIContent(text);
           }
           
           messageDiv.innerHTML = `
               <div class="message-avatar">${avatarText}</div>
               <div class="message-content">
                   <div class="message-text">${displayText}</div>
                   <div class="message-time">${time || new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}</div>
                   <div class="message-actions">
                       <button class="message-action-btn" onclick="copyMessage(this)" title="复制">
                           <svg viewBox="0 0 24 24" fill="currentColor">
                               <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                           </svg>
                           复制
                       </button>
                       ${sender === 'ai' ? `
                       <button class="message-action-btn" onclick="regenerateMessage(this)" title="重新生成">
                           <svg viewBox="0 0 24 24" fill="currentColor">
                               <path d="M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z"/>
                           </svg>
                           重新生成
                       </button>
                       ` : ''}
                       <button class="message-action-btn" onclick="translateMessage(this)" title="翻译">
                           <svg viewBox="0 0 24 24" fill="currentColor">
                               <path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/>
                           </svg>
                           翻译
                       </button>
                       <button class="message-action-btn" onclick="deleteMessage(this)" title="删除">
                           <svg viewBox="0 0 24 24" fill="currentColor">
                               <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                           </svg>
                           删除
                       </button>
                       ${sender === 'ai' ? `<span class="message-usage">${wordCount || 0}字 | ${modelDisplay}</span>` : `<span class="message-usage">${wordCount || 0}字</span>`}
                   </div>
               </div>
           `;
           
           messagesWrapper.appendChild(messageDiv);
           
           // 滚动到底部
           messagesWrapper.scrollTop = messagesWrapper.scrollHeight;
       }

       // AI内容智能排版函数
       // 标点符号转换函数
       function convertPunctuation(text) {
           return text
               .replace(/,/g, '，')     // 逗号
               .replace(/\./g, '。')     // 句号
               .replace(/;/g, '；')     // 分号
               .replace(/:/g, '：')     // 冒号
               .replace(/\?/g, '？')    // 问号
               .replace(/!/g, '！')     // 感叹号
               .replace(/\(/g, '（')    // 左括号
               .replace(/\)/g, '）')    // 右括号
               .replace(/"/g, '"')     // 双引号
               .replace(/'/g, "'")     // 单引号
               .replace(/\[/g, '【')    // 左方括号
               .replace(/\]/g, '】')    // 右方括号
               .replace(/{/g, '｛')     // 左花括号
               .replace(/}/g, '｝')     // 右花括号
               .replace(/~/g, '～');    // 波浪号
       }

       function formatAIContent(text) {
           // 首先转换标点符号
           text = convertPunctuation(text);
           
           // 处理换行和段落
           let formatted = text.replace(/\n\n/g, '</p><p>')
                              .replace(/\n/g, '<br>')
                              .replace(/^/, '<p>')
                              .replace(/$/, '</p>');
           
           // 处理列表
           formatted = formatted.replace(/(<p>|<br>)([0-9]+\.|[•\-\*])\s/g, '$1<span style="font-weight: 500; color: var(--primary-color);">$2</span> ');
           
           // 处理标题（以#开头的行）
           formatted = formatted.replace(/(<p>)#+\s*([^<]+)(<\/p>)/g, '$1<strong style="font-size: 1.1em; color: var(--primary-color); display: block; margin: 8px 0;">$2</strong>$3');
           
           // 处理粗体文本
           formatted = formatted.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
           
           // 处理斜体文本
           formatted = formatted.replace(/\*([^*]+)\*/g, '<em>$1</em>');
           
           // 处理代码块
           formatted = formatted.replace(/```([\s\S]*?)```/g, '<pre style="background: var(--bg-secondary); padding: 12px; border-radius: 6px; margin: 8px 0; overflow-x: auto;"><code>$1</code></pre>');
           
           // 处理行内代码
           formatted = formatted.replace(/`([^`]+)`/g, '<code style="background: var(--bg-secondary); padding: 2px 6px; border-radius: 4px; font-family: monospace;">$1</code>');
           
           return formatted;
       }

       // 真实API调用生成AI响应
       async function generateAIResponse(userInput) {
           const apiKey = getNextApiKey();
           const modelName = MODEL_MAPPING[currentModel] || MODEL_MAPPING['综合版'];
           
           // 构建消息历史
           const messages = [];
           
           // 如果有选中的提示词，添加系统消息
           if (currentPrompt && currentPrompt.content) {
               messages.push({
                   role: 'system',
                   content: currentPrompt.content
               });
           }
           
           // 添加历史消息（最近50轮）
           const recentMessages = currentMessages.slice(-50);
           for (const msg of recentMessages) {
               if (msg.sender === 'user') {
                   messages.push({
                       role: 'user',
                       content: msg.text // 使用处理后的文本（不包含提示词）
                   });
               } else if (msg.sender === 'ai') {
                   messages.push({
                       role: 'assistant',
                       content: msg.text
                   });
               }
           }
           
           // 添加当前用户输入（使用原始输入，可能包含提示词）
           messages.push({
               role: 'user',
               content: userInput
           });
           
           try {
               const response = await fetch(`${API_BASE_URL}/v1/chat/completions`, {
                   method: 'POST',
                   headers: {
                       'Content-Type': 'application/json',
                       'Authorization': `Bearer ${apiKey}`
                   },
                   body: JSON.stringify({
                       model: modelName,
                       messages: messages,
                       max_tokens: 16000,
                       temperature: 1,
                       stream: false
                   })
               });
               
               if (!response.ok) {
                   throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
               }
               
               const data = await response.json();
               
               if (data.choices && data.choices.length > 0) {
                   return data.choices[0].message.content;
               } else {
                   throw new Error('API响应格式错误');
               }
           } catch (error) {
               console.error('API调用错误:', error);
               
               // 如果API调用失败，返回错误提示
               return `抱歉，当前网络连接不稳定或API服务暂时不可用。\n\n错误信息: ${error.message}\n\n请稍后重试，或检查网络连接。如果问题持续存在，请联系技术支持。`;
           }
       }

       // 翻译文本智能排版函数
       function formatTranslatedText(text) {
           if (!text) return text;
           
           let formatted = text;
           
           // 1. 统一换行符
           formatted = formatted.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
           
           // 2. 处理多余的空行（保留段落间的空行，但限制连续空行数量）
           formatted = formatted.replace(/\n{4,}/g, '\n\n\n');
           
           // 3. 处理中英文之间的空格（保持适当间距）
           formatted = formatted.replace(/([\u4e00-\u9fa5])([a-zA-Z0-9])/g, '$1 $2');
           formatted = formatted.replace(/([a-zA-Z0-9])([\u4e00-\u9fa5])/g, '$1 $2');
           
           // 4. 处理中文标点符号（保持基本格式，不过度处理）
           formatted = formatted.replace(/\s+，/g, '，');
           formatted = formatted.replace(/\s+。/g, '。');
           formatted = formatted.replace(/\s+；/g, '；');
           formatted = formatted.replace(/\s+：/g, '：');
           formatted = formatted.replace(/\s+？/g, '？');
           formatted = formatted.replace(/\s+！/g, '！');
           
           // 5. 处理引号（保持原有格式）
           formatted = formatted.replace(/\s+"/g, '"');
           formatted = formatted.replace(/"\s+/g, '" ');
           
           // 6. 处理括号（保持适当间距）
           formatted = formatted.replace(/\s+（/g, '（');
           formatted = formatted.replace(/）\s+/g, '） ');
           
           // 7. 保留段落结构，只去除行首行尾的过多空格
           formatted = formatted.split('\n').map(line => {
               // 保留缩进，只去除行尾空格
               return line.replace(/\s+$/g, '');
           }).join('\n');
           
           // 8. 去除首尾空白
           formatted = formatted.trim();
           
           return formatted;
       }

       // 消息操作功能
       function copyMessage(btn) {
           const messageText = btn.closest('.message-content').querySelector('.message-text').textContent;
           navigator.clipboard.writeText(messageText).then(() => {
               const originalText = btn.innerHTML;
               btn.innerHTML = '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/></svg>已复制';
               setTimeout(() => {
                   btn.innerHTML = originalText;
               }, 2000);
           });
       }

       async function regenerateMessage(btn) {
           // 显示功能开发中的提醒
           await showCustomAlert('重新生成功能正在开发中，敬请期待！', 'info');
           return;
           
           const messageDiv = btn.closest('.message');
           const messageContent = messageDiv.querySelector('.message-content');
           const messageText = messageContent.querySelector('.message-text');
           const messageUsage = messageContent.querySelector('.message-usage');
           const originalText = messageText.textContent;
           
           // 找到对应的消息索引
           const messageIndex = Array.from(messagesWrapper.children).indexOf(messageDiv);
           
           // 检查当前消息是否为AI消息
           if (!messageDiv.classList.contains('ai')) {
               await showCustomAlert('只能重新生成AI回复', 'error');
               return;
           }
           
           // 调试信息（可在控制台查看）
           console.log('重新生成调试信息:');
           console.log('DOM消息索引:', messageIndex);
           console.log('currentMessages长度:', currentMessages.length);
           console.log('currentMessages内容:', currentMessages);
           
           // 找到最后一条用户消息作为重新生成的输入
           let lastUserMessage = '';
           let lastUserOriginalText = ''; // 保存用户的原始输入（可能包含提示词）
           let userMessageIndex = -1;
           let currentAIMessageIndex = -1;
           
           // 首先找到当前AI消息在currentMessages数组中的实际位置
           // 通过比较消息内容来匹配，考虑到可能的格式化差异
           for (let i = 0; i < currentMessages.length; i++) {
               if (currentMessages[i].sender === 'ai') {
                   // 移除HTML标签和多余空白进行比较
                   const msgText = currentMessages[i].text.replace(/<[^>]*>/g, '').trim();
                   const originalTextClean = originalText.replace(/<[^>]*>/g, '').trim();
                   
                   if (msgText === originalTextClean || 
                       msgText.includes(originalTextClean) || 
                       originalTextClean.includes(msgText)) {
                       currentAIMessageIndex = i;
                       break;
                   }
               }
           }
           
           console.log('找到的AI消息索引:', currentAIMessageIndex);
           
           // 从找到的AI消息位置往前查找最近的用户消息
           if (currentAIMessageIndex >= 0) {
               for (let i = currentAIMessageIndex - 1; i >= 0; i--) {
                   if (currentMessages[i] && currentMessages[i].sender === 'user') {
                       lastUserMessage = currentMessages[i].text;
                       // 如果有原始输入（包含提示词），优先使用原始输入
                       lastUserOriginalText = currentMessages[i].originalText || currentMessages[i].text;
                       userMessageIndex = i;
                       break;
                   }
               }
           }
           
           // 如果还是没有找到，使用备用方案
           if (!lastUserMessage) {
               console.log('使用备用查找方案');
               
               // 方案1：使用最后一条用户消息
               const userMessages = currentMessages.filter(msg => msg.sender === 'user');
               if (userMessages.length > 0) {
                   const lastUser = userMessages[userMessages.length - 1];
                   lastUserMessage = lastUser.text;
                   lastUserOriginalText = lastUser.originalText || lastUser.text;
                   userMessageIndex = currentMessages.findIndex(msg => msg.id === lastUser.id);
                   console.log('找到备用用户消息:', lastUserMessage);
               }
               
               // 方案2：如果仍然没有找到，尝试使用DOM索引逻辑
               if (!lastUserMessage && messageIndex > 0) {
                   console.log('使用DOM索引备用方案');
                   // 从DOM索引往前查找用户消息
                   for (let i = messageIndex - 1; i >= 0; i--) {
                       const domMsg = messagesWrapper.children[i];
                       if (domMsg && domMsg.classList.contains('user')) {
                           // 获取这个DOM元素的文本内容
                           const userTextElement = domMsg.querySelector('.message-text');
                           if (userTextElement) {
                               lastUserMessage = userTextElement.textContent.trim();
                               console.log('从DOM找到用户消息:', lastUserMessage);
                               break;
                           }
                       }
                   }
               }
           }
           
           console.log('最终用户消息:', lastUserMessage);
           console.log('用户消息索引:', userMessageIndex);
           
           if (!lastUserMessage) {
               console.error('重新生成失败：无法找到用户消息');
               console.error('currentMessages:', currentMessages);
               console.error('messageIndex:', messageIndex);
               
               // 提供更详细的错误信息
               let errorMsg = '无法找到对应的用户消息';
               if (currentMessages.length === 0) {
                   errorMsg = '当前对话为空，请先发送消息';
               } else if (currentMessages.filter(msg => msg.sender === 'user').length === 0) {
                   errorMsg = '当前对话中没有用户消息';
               } else {
                   errorMsg = '消息匹配失败，请刷新页面后重试';
               }
               
               await showCustomAlert(errorMsg, 'error');
               return;
           }
           
           // 添加加载动画
           messageText.innerHTML = '<span style="opacity: 0.5;">正在重新生成...</span>';
           
           try {
               // 构建完整的上下文进行重新生成
               const apiKey = getNextApiKey();
               const modelName = MODEL_MAPPING[currentModel] || MODEL_MAPPING['综合版'];
               
               // 构建消息历史
               const messages = [];
               
               // 如果有选中的提示词，添加系统消息
               if (currentPrompt && currentPrompt.content) {
                   messages.push({
                       role: 'system',
                       content: currentPrompt.content
                   });
               }
               
               // 添加历史消息（最近10轮，但只到当前消息之前）
               const contextMessages = currentMessages.slice(0, messageIndex);
               const recentMessages = contextMessages.slice(-20); // 增加上下文长度
               for (const msg of recentMessages) {
                   if (msg.sender === 'user') {
                       messages.push({
                           role: 'user',
                           content: msg.text // 历史消息使用处理后的文本（不包含提示词）
                       });
                   } else if (msg.sender === 'ai') {
                       messages.push({
                           role: 'assistant',
                           content: msg.text
                       });
                   }
               }
               
               // 确保包含触发重新生成的用户消息（使用原始输入，可能包含提示词）
               if (lastUserOriginalText && !messages.some(m => m.role === 'user' && m.content === lastUserOriginalText)) {
                   messages.push({
                       role: 'user',
                       content: lastUserOriginalText
                   });
               }
               
               // 调用AI API重新生成
               const response = await fetch(`${API_BASE_URL}/v1/chat/completions`, {
                   method: 'POST',
                   headers: {
                       'Content-Type': 'application/json',
                       'Authorization': `Bearer ${apiKey}`
                   },
                   body: JSON.stringify({
                       model: modelName,
                       messages: messages,
                       max_tokens: 2000,
                       temperature: 0.7,
                       stream: false
                   })
               });
               
               if (!response.ok) {
                   throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
               }
               
               const data = await response.json();
               
               let newResponse;
               if (data.choices && data.choices.length > 0) {
                   newResponse = data.choices[0].message.content;
               } else {
                   throw new Error('API响应格式错误');
               }
               
               // 更新UI
               messageText.textContent = newResponse;
               const wordCount = countChineseCharacters(newResponse);
               messageUsage.textContent = wordCount + '字 | ' + currentModel;
               
               // 更新消息记录
               if (currentMessages[messageIndex]) {
                   currentMessages[messageIndex].text = newResponse;
                   currentMessages[messageIndex].wordCount = wordCount;
                   currentMessages[messageIndex].model = currentModel;
               }
               
               showNotification('重新生成成功', 'success');
           } catch (error) {
               // 恢复原文本
               messageText.textContent = originalText;
               showNotification('重新生成失败，请稍后重试', 'error');
               console.error('重新生成失败:', error);
           }
       }

       async function translateMessage(btn) {
           const messageText = btn.closest('.message-content').querySelector('.message-text');
           const originalText = messageText.textContent;
           
           // 检查是否已经翻译
           if (messageText.dataset.translated === 'true') {
               // 恢复原文
               messageText.textContent = messageText.dataset.originalText;
               messageText.dataset.translated = 'false';
               btn.innerHTML = '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/></svg>翻译';
           } else {
               // 保存原文并开始翻译
               messageText.dataset.originalText = originalText;
               
               // 显示翻译中状态
               const originalBtnContent = btn.innerHTML;
               btn.innerHTML = '<span style="opacity: 0.5;">翻译中...</span>';
               btn.disabled = true;
               
               try {
                   // 构建翻译请求，包含智能排版要求
                   const translatePrompt = `请将以下内容翻译成中文，并进行智能排版优化。要求：
1. 保持原文的段落结构和格式
2. 确保标点符号使用正确的中文标点
3. 适当调整句子长度，使其更符合中文表达习惯
4. 保持专业术语的准确性
5. 只返回翻译和排版后的结果，不要添加任何解释

原文内容：
${originalText}`;
                   
                   // 调用AI API进行翻译，使用指定的翻译模型
                   const translatedText = await callTranslateAPI(translatePrompt);
                   
                   // 对翻译结果进行后处理，进一步优化排版
                   const formattedText = formatTranslatedText(translatedText);
                   
                   // 更新UI
                   messageText.textContent = formattedText;
                   messageText.dataset.translated = 'true';
                   btn.innerHTML = '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/></svg>原文';
                   btn.disabled = false;
                   
                   showNotification('翻译完成', 'success');
               } catch (error) {
                   // 恢复按钮状态
                   btn.innerHTML = originalBtnContent;
                   btn.disabled = false;
                   
                   showNotification('翻译失败，请稍后重试', 'error');
                   console.error('翻译失败:', error);
               }
           }
       }

       async function deleteMessage(btn) {
           const confirmed = await showCustomConfirm('确定要删除这条消息吗？', '删除消息');
           if (confirmed) {
               const messageDiv = btn.closest('.message');
               const messageIndex = Array.from(messagesWrapper.children).indexOf(messageDiv);
               
               // 从消息数组中删除
               currentMessages.splice(messageIndex, 1);
               
               // 从UI中删除
               messageDiv.style.opacity = '0';
               messageDiv.style.transform = 'translateX(20px)';
               setTimeout(() => {
                   messageDiv.remove();
                   
                   // 如果没有消息了，显示空状态
                   if (messagesWrapper.children.length === 0) {
                       emptyState.style.display = 'flex';
                       messagesWrapper.style.display = 'none';
                       currentDialogueId = null;
                       currentMessages = [];
                   }
                   
                   // 移除自动保存，只有用户主动保存或新建对话时才保存
               }, 300);
           }
       }

       // 绑定发送事件
       sendButton.addEventListener('click', sendMessage);
       messageInput.addEventListener('keydown', (e) => {
           if (e.key === 'Enter' && !e.shiftKey) {
               e.preventDefault();
               sendMessage();
           }
       });

       // 文件上传
       function triggerFileUpload() {
           document.getElementById('fileInput').click();
       }

       document.getElementById('fileInput').addEventListener('change', (e) => {
           const files = e.target.files;
           if (files.length > 0) {
               let fileNames = [];
               for (let i = 0; i < files.length; i++) {
                   fileNames.push(files[i].name);
               }
               // 添加系统消息
               const systemMsg = document.createElement('div');
               systemMsg.className = 'system-message';
               systemMsg.textContent = `已上传文件: ${fileNames.join(', ')}`;
               messagesWrapper.appendChild(systemMsg);
               
               // 隐藏空状态
               if (emptyState.style.display !== 'none') {
                   emptyState.style.display = 'none';
                   messagesWrapper.style.display = 'flex';
               }
               
               // 清空文件选择
               e.target.value = '';
           }
       });

       // --- 右侧面板功能 ---
       function switchTab(tab) {
           // 切换标签样式
           const tabs = document.querySelectorAll('.panel-tab');
           tabs.forEach(t => t.classList.remove('active'));
            event.target.classList.add('active');
            
            // 切换内容面板
            const panels = document.querySelectorAll('.panel-content');
            panels.forEach(p => {
                p.classList.remove('active', 'active-left');
            });
            
            if (tab === 'history') {
                document.getElementById('historyPanel').classList.add('active');
                document.getElementById('attachmentsPanel').classList.add('active-left');
            } else {
                document.getElementById('attachmentsPanel').classList.add('active');
                document.getElementById('historyPanel').classList.add('active-left');
            }
        }

        // 新建对话
        function newChat() {
            // 强制保存当前对话到历史记录
            saveCurrentDialogue(true);
            
            // 创建新对话
            currentDialogueId = null;
            currentMessages = [];
            
            // 重置提示词相关状态
            currentPrompt = null;
            promptHintText = '请收到AI响应后，根据提示进行操作';
            isSelectingModelForPrompt = false;
            document.querySelector('.btn-select-prompt').textContent = '选择提示词';
            
            // 清空界面
            messagesWrapper.innerHTML = '';
            emptyState.style.display = 'flex';
            messagesWrapper.style.display = 'none';
            
            // 更新历史列表
            renderHistoryList();
        }

        // 清空对话
        async function clearChat() {
            const confirmed = await showCustomConfirm('确定要清空当前对话吗？此操作不可撤销。', '清空对话');
            if (confirmed) {
                // 如果有对话ID，从历史记录中删除
                if (currentDialogueId) {
                    dialogueHistory = dialogueHistory.filter(d => d.id !== currentDialogueId);
                    localStorage.setItem('dialogueHistory', JSON.stringify(dialogueHistory));
                    renderHistoryList();
                }
                
                currentMessages = [];
                currentDialogueId = null;
                
                // 重置提示词相关状态
                currentPrompt = null;
                promptHintText = '请收到AI响应后，根据提示进行操作';
                isSelectingModelForPrompt = false;
                document.querySelector('.btn-select-prompt').textContent = '选择提示词';
                
                messagesWrapper.innerHTML = '';
                emptyState.style.display = 'flex';
                messagesWrapper.style.display = 'none';
            }
        }

        // 导出对话
        async function exportDialogue() {
            if (currentMessages.length === 0) {
                await showCustomAlert('当前没有对话内容可导出', 'warning');
                return;
            }
            
            let dialogueText = '对话记录导出\n';
            dialogueText += '导出时间: ' + new Date().toLocaleString('zh-CN') + '\n';
            dialogueText += '====================\n\n';
            
            currentMessages.forEach(msg => {
                const sender = msg.sender === 'user' ? '用户' : 'AI';
                dialogueText += `[${sender}] ${msg.time}\n${msg.text}\n字数: ${msg.wordCount}字\n`;
                if (msg.model) {
                    dialogueText += `模型: ${msg.model}\n`;
                }
                dialogueText += '\n';
            });
            
            // 创建Blob并下载
            const blob = new Blob([dialogueText], { type: 'text/plain;charset=utf-8' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `对话记录_${new Date().getTime()}.txt`;
            link.click();
        }

        // 保存对话
        async function saveDialogue() {
            if (currentMessages.length === 0) {
                await showCustomAlert('当前没有对话内容可保存', 'warning');
                return;
            }
            
            // 强制保存当前对话到历史记录
            saveCurrentDialogue(true);
            
            // 显示成功提示
            await showCustomAlert('对话已保存到历史记录中', 'success');
        }

        // --- 提示词市场功能 ---
        // 预制提示词已删除，用户可自定义创建提示词
        const prompts = [];
        let userPrompts = []; // 用户创建的提示词

        let currentCategory = 'all';
        let selectedPromptId = null;

        // 加载用户创建的提示词
        function loadUserPrompts() {
            const saved = localStorage.getItem('userPrompts');
            if (saved) {
                userPrompts = JSON.parse(saved);
            }
        }

        // 保存用户创建的提示词
        function saveUserPrompts() {
            localStorage.setItem('userPrompts', JSON.stringify(userPrompts));
        }

        // 初始化时加载用户提示词
        loadUserPrompts();

        function openPromptMarket() {
            const modal = document.getElementById('promptMarketModal');
            modal.style.display = 'flex';
            
            // 延迟添加show类以触发动画
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
            
            // 初始化提示词列表
            renderPrompts();
        }

        function closePromptMarket() {
            const modal = document.getElementById('promptMarketModal');
            modal.classList.remove('show');
            
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        function filterPrompts(category) {
            currentCategory = category;
            
            // 更新分类按钮状态
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 重新渲染提示词
            renderPrompts();
        }

        function renderPrompts() {
            const grid = document.getElementById('promptGrid');
            grid.innerHTML = '';
            
            let filteredPrompts = [];
            if (currentCategory === 'all') {
                filteredPrompts = [...prompts, ...userPrompts];
            } else if (currentCategory === 'my') {
                filteredPrompts = userPrompts;
            } else {
                filteredPrompts = prompts.filter(p => p.category === currentCategory);
            }
            
            if (filteredPrompts.length === 0) {
                grid.innerHTML = '<div style="text-align: center; color: var(--text-secondary); padding: 40px;">暂无提示词</div>';
                return;
            }
            
            filteredPrompts.forEach((prompt, index) => {
                const card = document.createElement('div');
                card.className = 'prompt-card';
                card.style.setProperty('--card-index', index);
                if (prompt.id === selectedPromptId) {
                    card.classList.add('selected');
                }
                
                // 检查是否为用户创建的提示词
                const isUserPrompt = userPrompts.some(up => up.id === prompt.id);
                
                card.innerHTML = `
                    <div class="prompt-name">${prompt.name}</div>
                    <div class="prompt-desc">${prompt.scene || prompt.desc || '用户自定义提示词'}</div>
                    <div class="prompt-tags">
                        ${prompt.tags ? prompt.tags.map(tag => `<span class="prompt-tag">${tag}</span>`).join('') : '<span class="prompt-tag">自定义</span>'}
                    </div>
                    ${isUserPrompt ? `
                        <div class="prompt-actions">
                            <button class="prompt-action-btn" onclick="editUserPrompt('${prompt.id}'); event.stopPropagation();" title="修改">
                                <svg viewBox="0 0 24 24" fill="currentColor"><path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/></svg>
                            </button>
                            <button class="prompt-action-btn" onclick="deleteUserPrompt('${prompt.id}'); event.stopPropagation();" title="删除">
                                <svg viewBox="0 0 24 24" fill="currentColor"><path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/></svg>
                            </button>
                        </div>
                    ` : ''}
                `;
                
                card.addEventListener('click', () => {
                    // 移除其他卡片的选中状态
                    document.querySelectorAll('.prompt-card').forEach(c => c.classList.remove('selected'));
                    card.classList.add('selected');
                    selectedPromptId = prompt.id;
                });
                
                grid.appendChild(card);
            });
        }

        async function selectPrompt() {
            if (!selectedPromptId) {
                await showCustomAlert('请先选择一个提示词', 'warning');
                return;
            }
            
            // 从预制提示词和用户提示词中查找
            const selected = prompts.find(p => p.id === selectedPromptId) || 
                           userPrompts.find(p => p.id === selectedPromptId);
            
            if (!selected) {
                await showCustomAlert('提示词不存在', 'error');
                return;
            }
            
            currentPrompt = selected;
            promptHintText = selected.hint || '请收到AI响应后，根据提示进行操作';
            document.querySelector('.btn-select-prompt').textContent = selected.name;
            
            closePromptMarket();
            
            // 标记正在为提示词选择模型
            isSelectingModelForPrompt = true;
            
            // 打开模型选择弹窗
            openModelSelect();
        }

        // --- 创建提示词功能 ---
        function openCreatePromptModal() {
            const modal = document.getElementById('createPromptModal');
            modal.style.display = 'flex';
            
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
        }

        function closeCreatePromptModal() {
            const modal = document.getElementById('createPromptModal');
            modal.classList.remove('show');
            
            setTimeout(() => {
                modal.style.display = 'none';
                document.getElementById('promptForm').reset();
            }, 300);
        }

        // 提示词表单提交
        document.getElementById('promptForm').addEventListener('submit', (e) => {
            e.preventDefault();
            
            const name = document.getElementById('promptNameInput').value;
            const content = document.getElementById('promptContentInput').value;
            const scene = document.getElementById('promptSceneInput').value;
            const hint = document.getElementById('promptHintInput').value;
            
            // 创建新的提示词对象
            const newPrompt = {
                id: generateId(),
                name: name,
                content: content,
                scene: scene,
                hint: hint || '请收到AI响应后，根据提示进行操作',
                createdAt: new Date().toISOString()
            };
            
            // 添加到用户提示词数组
            userPrompts.push(newPrompt);
            
            // 保存到localStorage
            saveUserPrompts();
            
            // 设置为当前提示词
            currentPrompt = newPrompt;
            promptHintText = newPrompt.hint;
            
            // 更新选择按钮文本
            document.querySelector('.btn-select-prompt').textContent = name;
            
            closeCreatePromptModal();
            
            // 标记正在为提示词选择模型
            isSelectingModelForPrompt = true;
            
            // 打开模型选择弹窗
            openModelSelect();
        });

        // 修改提示词表单提交
        document.getElementById('editPromptForm').addEventListener('submit', (e) => {
            e.preventDefault();
            
            const id = document.getElementById('editPromptId').value;
            const name = document.getElementById('editPromptNameInput').value;
            const content = document.getElementById('editPromptContentInput').value;
            const scene = document.getElementById('editPromptSceneInput').value;
            const hint = document.getElementById('editPromptHintInput').value;
            
            // 查找并更新提示词
            const promptIndex = userPrompts.findIndex(p => p.id === id);
            if (promptIndex !== -1) {
                userPrompts[promptIndex] = {
                    ...userPrompts[promptIndex],
                    name: name,
                    content: content,
                    scene: scene,
                    hint: hint || '请收到AI响应后，根据提示进行操作',
                    updatedAt: new Date().toISOString()
                };
                
                // 保存到localStorage
                saveUserPrompts();
                
                // 如果当前选中的是被修改的提示词，更新当前提示词
                if (currentPrompt && currentPrompt.id === id) {
                    currentPrompt = userPrompts[promptIndex];
                    promptHintText = currentPrompt.hint;
                    document.querySelector('.btn-select-prompt').textContent = name;
                }
                
                closeEditPromptModal();
                
                // 重新渲染提示词列表
                renderPrompts();
                
                showNotification('提示词修改成功', 'success');
            }
        });

        // 修改用户提示词
        function editUserPrompt(promptId) {
            const prompt = userPrompts.find(p => p.id === promptId);
            if (!prompt) {
                showNotification('提示词不存在', 'error');
                return;
            }
            
            // 填充表单
            document.getElementById('editPromptId').value = prompt.id;
            document.getElementById('editPromptNameInput').value = prompt.name;
            document.getElementById('editPromptContentInput').value = prompt.content;
            document.getElementById('editPromptSceneInput').value = prompt.scene || '';
            document.getElementById('editPromptHintInput').value = prompt.hint || '';
            
            // 打开修改弹窗
            openEditPromptModal();
        }

        // 删除用户提示词
        async function deleteUserPrompt(promptId) {
            const prompt = userPrompts.find(p => p.id === promptId);
            if (!prompt) {
                showNotification('提示词不存在', 'error');
                return;
            }
            
            const confirmed = await showCustomConfirm(`确定要删除提示词"${prompt.name}"吗？`, '删除提示词');
            if (!confirmed) return;
            
            // 从数组中删除
            const promptIndex = userPrompts.findIndex(p => p.id === promptId);
            if (promptIndex !== -1) {
                userPrompts.splice(promptIndex, 1);
                
                // 保存到localStorage
                saveUserPrompts();
                
                // 如果删除的是当前选中的提示词，清空当前提示词
                if (currentPrompt && currentPrompt.id === promptId) {
                    currentPrompt = null;
                    promptHintText = '';
                    document.querySelector('.btn-select-prompt').textContent = '选择提示词';
                }
                
                // 重新渲染提示词列表
                renderPrompts();
                
                showNotification('提示词删除成功', 'success');
            }
        }

        // 打开修改提示词弹窗
        function openEditPromptModal() {
            const modal = document.getElementById('editPromptModal');
            modal.style.display = 'flex';
            
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
        }

        // 关闭修改提示词弹窗
        function closeEditPromptModal() {
            const modal = document.getElementById('editPromptModal');
            modal.classList.remove('show');
            
            setTimeout(() => {
                modal.style.display = 'none';
                document.getElementById('editPromptForm').reset();
            }, 300);
        }

        // 翻译API调用函数
        async function callTranslateAPI(prompt) {
            const apiKey = getNextApiKey();
            const modelName = 'gemini-2.5-flash-lite-preview-06-17';
            
            try {
                const response = await fetch(`${API_BASE_URL}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        model: modelName,
                        messages: [{
                            role: 'user',
                            content: prompt
                        }],
                        max_tokens: 4000,
                        temperature: 0.3,
                        stream: false
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`翻译API请求失败: ${response.status} ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.choices && data.choices.length > 0) {
                    return data.choices[0].message.content;
                } else {
                    throw new Error('翻译API响应格式错误');
                }
            } catch (error) {
                console.error('翻译API调用失败:', error);
                throw error;
            }
        }

        // --- 模型选择功能 ---
        const models = [
            { id: 'delicate', name: '细腻贴合', icon: '🎨', desc: '注重情感表达和细节描写' },
            { id: 'logical', name: '逻辑精准', icon: '🎯', desc: '强调逻辑严谨和结构清晰' },
            { id: 'creative', name: '灵活创意', icon: '💡', desc: '激发创意思维和想象力' },
            { id: 'stable', name: '稳定版', icon: '🛡️', desc: '平衡各方面的综合表现' },
            { id: 'comprehensive', name: '综合版', icon: '🌟', desc: '集成多种能力的全能模型' },
            { id: 'beta', name: '测试版', icon: '🚀', desc: '最新功能的实验性模型' }
        ];

        let selectedModelId = 'logical';

        function openModelSelect() {
            const modal = document.getElementById('modelSelectModal');
            modal.style.display = 'flex';
            
            // 如果是为提示词选择模型，显示提示内容
            const hintSection = document.getElementById('promptHintSection');
            const hintTextEl = document.getElementById('promptHintText');
            
            if (isSelectingModelForPrompt && promptHintText) {
                hintSection.style.display = 'block';
                hintTextEl.textContent = promptHintText;
            } else {
                hintSection.style.display = 'none';
            }
            
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
            
            renderModels();
        }

        function closeModelSelect() {
            const modal = document.getElementById('modelSelectModal');
            modal.classList.remove('show');
            
            setTimeout(() => {
                modal.style.display = 'none';
                // 不在这里重置isSelectingModelForPrompt，让confirmModelSelect函数来处理
                modal.dataset.confirmed = '';
            }, 300);
        }

        function renderModels() {
            const grid = document.getElementById('modelGrid');
            grid.innerHTML = '';
            
            models.forEach((model, index) => {
                const card = document.createElement('div');
                card.className = 'model-card';
                card.style.setProperty('--card-index', index);
                
                const modelName = model.name;
                if (modelName === currentModel) {
                    card.classList.add('selected');
                    selectedModelId = model.id;
                }
                
                card.innerHTML = `
                    <div class="model-icon">${model.icon}</div>
                    <div class="model-name">${model.name}</div>
                    <div class="model-desc">${model.desc}</div>
                `;
                
                card.addEventListener('click', () => {
                    document.querySelectorAll('.model-card').forEach(c => c.classList.remove('selected'));
                    card.classList.add('selected');
                    selectedModelId = model.id;
                });
                
                grid.appendChild(card);
            });
        }

        function confirmModelSelect() {
            const selected = models.find(m => m.id === selectedModelId);
            if (!selected) return;
            
            // 检查是否与当前模型一致
            const previousModel = currentModel;
            
            // 更新全局默认模型
            currentModel = selected.name;
            
            // 标记是否需要显示切换信息（避免重复显示）
            let shouldShowSwitchMessage = false;
            
            // 只有在模型不一致时才显示切换信息
            if (messagesWrapper.children.length > 0 && previousModel !== selected.name) {
                shouldShowSwitchMessage = true;
                const systemMsg = document.createElement('div');
                systemMsg.className = 'system-message';
                systemMsg.textContent = `已切换到模型：${selected.name}`;
                messagesWrapper.appendChild(systemMsg);
                messagesWrapper.scrollTop = messagesWrapper.scrollHeight;
            }
            
            // 如果是为提示词选择的模型，标记为确认状态
            if (isSelectingModelForPrompt && currentPrompt) {
                document.getElementById('modelSelectModal').dataset.confirmed = 'true';
            }
            
            closeModelSelect();
            
            // 如果是为提示词选择的模型，自动发送提示词内容
            if (isSelectingModelForPrompt && currentPrompt) {
                console.log('开始自动发送提示词:', {
                    isSelectingModelForPrompt,
                    currentPrompt: currentPrompt.name,
                    content: currentPrompt.content
                });
                
                // 隐藏空状态
                if (emptyState.style.display !== 'none') {
                    emptyState.style.display = 'none';
                    messagesWrapper.style.display = 'flex';
                }
                
                // 只有在模型不一致且之前没有显示过切换信息时才显示
                if (previousModel !== selected.name && !shouldShowSwitchMessage) {
                    const systemMsg = document.createElement('div');
                    systemMsg.className = 'system-message';
                    systemMsg.textContent = `已切换到模型：${selected.name}`;
                    messagesWrapper.appendChild(systemMsg);
                }
                
                // 隐藏操作提示，不显示任何提示词相关信息
                // if (promptHintText && promptHintText !== '请收到AI响应后，根据提示进行操作') {
                //     setTimeout(() => {
                //         const hintMsg = document.createElement('div');
                //         hintMsg.className = 'system-message';
                //         hintMsg.textContent = `💡 操作提示: ${promptHintText}`;
                //         messagesWrapper.appendChild(hintMsg);
                //     }, 500);
                // }
                
                // 自动发送提示词内容给API
                setTimeout(async () => {
                    const promptContent = currentPrompt.content || currentPrompt.name;
                    
                    // 重新获取DOM元素，确保在正确的作用域中
                    const sendButton = document.getElementById('sendButton');
                    const messagesWrapper = document.getElementById('messagesWrapper');
                    
                    // 不将提示词消息添加到UI和currentMessages中，直接调用API
                    // 提示词内容只用于API调用，不显示在界面上
                    
                    // 禁用发送按钮并显示加载状态
                    sendButton.disabled = true;
                    const originalButtonText = sendButton.innerHTML;
                    sendButton.innerHTML = '<span class="loading-dots"></span>';
                    
                    // 添加思考动画消息
                    const thinkingMessageId = 'thinking-' + Date.now();
                    const thinkingDiv = document.createElement('div');
                    thinkingDiv.className = 'message ai';
                    thinkingDiv.id = thinkingMessageId;
                    thinkingDiv.innerHTML = `
                        <div class="message-avatar">AI</div>
                        <div class="message-content">
                            <div class="message-text">
                                <span class="thinking-indicator">思考中<span class="dot-1">.</span><span class="dot-2">.</span><span class="dot-3">.</span></span>
                            </div>
                        </div>
                    `;
                    messagesWrapper.appendChild(thinkingDiv);
                    messagesWrapper.scrollTop = messagesWrapper.scrollHeight;
                    
                    try {
                        // 直接调用AI API，使用提示词内容
                        const apiKey = getNextApiKey();
                        const modelName = MODEL_MAPPING[currentModel] || MODEL_MAPPING['综合版'];
                        
                        // 构建消息历史
                        const messages = [];
                        
                        // 添加历史消息（最近50轮）
                        const recentMessages = currentMessages.slice(-50);
                        for (const msg of recentMessages) {
                            if (msg.sender === 'user') {
                                messages.push({
                                    role: 'user',
                                    content: msg.text
                                });
                            } else if (msg.sender === 'ai') {
                                messages.push({
                                    role: 'assistant',
                                    content: msg.text
                                });
                            }
                        }
                        
                        // 添加提示词作为用户消息（而不是系统消息）
                        messages.push({
                            role: 'user',
                            content: promptContent
                        });
                        
                        console.log('发送的消息结构:', messages);
                        
                        // 调用AI API
                        const response = await fetch(`${API_BASE_URL}/v1/chat/completions`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${apiKey}`
                            },
                            body: JSON.stringify({
                                model: modelName,
                                messages: messages,
                                max_tokens: 16000,
                                temperature: 1,
                                stream: false
                            })
                        });
                        
                        if (!response.ok) {
                            throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                        }
                        
                        const data = await response.json();
                        
                        let aiResponse;
                        if (data.choices && data.choices.length > 0) {
                            aiResponse = data.choices[0].message.content;
                        } else {
                            throw new Error('API响应格式错误');
                        }
                        
                        // 移除思考动画
                        const thinkingElement = document.getElementById(thinkingMessageId);
                        if (thinkingElement) {
                            thinkingElement.remove();
                        }
                        
                        const aiMessage = {
                            id: generateId(),
                            text: aiResponse,
                            sender: 'ai',
                            time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
                            wordCount: countChineseCharacters(aiResponse),
                            model: currentModel
                        };
                        currentMessages.push(aiMessage);
                        addMessageToUI(aiMessage.text, aiMessage.sender, aiMessage.time, aiMessage.wordCount, aiMessage.model);
                        
                        // 移除自动保存，只有用户主动保存或新建对话时才保存
                        
                    } catch (error) {
                        // 移除思考动画
                        const thinkingElement = document.getElementById(thinkingMessageId);
                        if (thinkingElement) {
                            thinkingElement.remove();
                        }
                        
                        const errorMessage = {
                            id: generateId(),
                            text: '抱歉，AI响应出现错误，请稍后重试。',
                            sender: 'ai',
                            time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
                            wordCount: 0,
                            model: currentModel
                        };
                        currentMessages.push(errorMessage);
                        addMessageToUI(errorMessage.text, errorMessage.sender, errorMessage.time, errorMessage.wordCount, errorMessage.model);
                    } finally {
                        // 恢复发送按钮
                        sendButton.disabled = false;
                        sendButton.innerHTML = originalButtonText;
                        
                        // 确保移除思考动画（如果还存在）
                        const thinkingElement = document.getElementById(thinkingMessageId);
                        if (thinkingElement) {
                            thinkingElement.remove();
                        }
                        
                        // 重置提示词选择标记
                        isSelectingModelForPrompt = false;
                    }
                }, 1000);
            } else {
                // 如果不是为提示词选择模型，立即重置标记
                isSelectingModelForPrompt = false;
            }
            // 重复的模型切换逻辑已移除，统一在上面处理
        }

        // 点击弹窗外部关闭
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    if (modal.id === 'promptMarketModal') {
                        closePromptMarket();
                    } else if (modal.id === 'createPromptModal') {
                        closeCreatePromptModal();
                    } else if (modal.id === 'modelSelectModal') {
                        closeModelSelect();
                    }
                }
            });
        });

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            renderHistoryList();
            
            // 默认显示新对话界面，不自动加载历史记录
            // 用户可以手动点击历史记录来加载
        });
        // 移除页面关闭时的自动保存，只有用户主动保存或新建对话时才保存
        // window.addEventListener('beforeunload', () => {
        //     saveCurrentDialogue();
        // });

        // 显示成功提示的函数
        function showSuccessNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'notification success';
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4CAF50;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-size: 14px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;
            
            document.body.appendChild(notification);
            
            // 显示动画
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 10);
            
            // 自动隐藏
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
        
        // --- 自定义提醒弹窗功能 ---
        function showCustomAlert(message, type = 'info') {
            const alertElement = document.getElementById('customAlert');
            const iconElement = document.getElementById('customAlertIcon');
            const messageElement = document.getElementById('customAlertMessage');
            
            // 设置消息内容
            messageElement.textContent = message;
            
            // 设置图标和样式
            iconElement.className = 'custom-alert-icon ' + type;
            switch(type) {
                case 'success':
                    iconElement.textContent = '✓';
                    break;
                case 'error':
                    iconElement.textContent = '✕';
                    break;
                case 'warning':
                    iconElement.textContent = '⚠';
                    break;
                case 'info':
                default:
                    iconElement.textContent = 'ℹ';
                    break;
            }
            
            // 显示弹窗
            alertElement.style.display = 'flex';
            setTimeout(() => {
                alertElement.classList.add('show');
            }, 10);
        }
        
        function closeCustomAlert() {
            const alertElement = document.getElementById('customAlert');
            alertElement.classList.remove('show');
            setTimeout(() => {
                alertElement.style.display = 'none';
            }, 300);
        }
        
        // 重写alert函数
        function alert(message) {
            showCustomAlert(message, 'info');
        }
        
        // 添加成功和错误提示的便捷函数
        function showSuccess(message) {
            showCustomAlert(message, 'success');
        }
        
        function showError(message) {
            showCustomAlert(message, 'error');
        }
        
        // 点击弹窗外部关闭
        document.getElementById('customAlert').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCustomAlert();
            }
        });
        
        // ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const alertElement = document.getElementById('customAlert');
                if (alertElement.classList.contains('show')) {
                    closeCustomAlert();
                }
            }
        });
        
        // 自定义确认对话框
        function showCustomConfirm(message, title = '确认操作') {
            return new Promise((resolve) => {
                // 创建确认弹窗HTML
                const confirmHTML = `
                    <div class="custom-alert" id="customConfirm">
                        <div class="custom-alert-content">
                            <div class="custom-alert-icon warning">⚠</div>
                            <div class="custom-alert-message">${message}</div>
                            <div class="custom-alert-buttons">
                                <button class="custom-alert-button secondary" onclick="closeCustomConfirm(false)">取消</button>
                                <button class="custom-alert-button" onclick="closeCustomConfirm(true)">确定</button>
                            </div>
                        </div>
                    </div>
                `;
                
                // 添加到页面
                document.body.insertAdjacentHTML('beforeend', confirmHTML);
                
                const confirmElement = document.getElementById('customConfirm');
                
                // 显示动画
                confirmElement.style.display = 'flex';
                setTimeout(() => {
                    confirmElement.classList.add('show');
                }, 10);
                
                // 关闭函数
                window.closeCustomConfirm = function(result) {
                    confirmElement.classList.remove('show');
                    setTimeout(() => {
                        if (confirmElement.parentNode) {
                            confirmElement.parentNode.removeChild(confirmElement);
                        }
                    }, 300);
                    resolve(result);
                };
            });
        }



        
        // --- 附件管理功能 ---
        
        // 便签管理
        function openNoteManager() {
            showAttachmentManager('便签', 'note', [
                { id: 1, name: '创作灵感', content: '记录突然想到的创意点子和灵感片段' },
                { id: 2, name: '人物设定', content: '主要角色的基本信息和性格特点' },
                { id: 3, name: '情节大纲', content: '故事的主要情节线和转折点' }
            ]);
        }
        
        // 人设卡管理
        function openCharacterManager() {
            showAttachmentManager('人设卡', 'character', [
                { id: 1, name: '李明', content: '男主角，25岁，程序员，性格内向但善良' },
                { id: 2, name: '张小雨', content: '女主角，23岁，设计师，活泼开朗，喜欢旅行' },
                { id: 3, name: '王老师', content: '配角，50岁，大学教授，智慧长者' }
            ]);
        }
        
        // 章节管理
        function openChapterManager() {
            showAttachmentManager('章节', 'chapter', [
                { id: 1, name: '第一章：相遇', content: '男女主角在咖啡厅的偶然相遇' },
                { id: 2, name: '第二章：误会', content: '因为一个小误会产生的矛盾' },
                { id: 3, name: '第三章：和解', content: '通过朋友的帮助重新认识彼此' }
            ]);
        }
        
        // 知识卡管理
        function openKnowledgeManager() {
            showAttachmentManager('知识卡', 'knowledge', [
                { id: 1, name: '写作技巧', content: '如何塑造立体的人物形象' },
                { id: 2, name: '情节设计', content: '三幕式结构的运用方法' },
                { id: 3, name: '对话技巧', content: '让对话更自然生动的方法' }
            ]);
        }
        
        // 通用附件管理器
        function showAttachmentManager(title, type, items) {
            // 创建弹窗HTML
            const modalHTML = `
                <div class="attachment-manager-overlay" id="attachmentManagerOverlay">
                    <div class="attachment-manager-modal" id="attachmentManagerModal">
                        <div class="attachment-manager-header">
                            <h3 class="attachment-manager-title">${title}管理</h3>
                            <button class="attachment-manager-close" onclick="closeAttachmentManager()">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                                </svg>
                            </button>
                        </div>
                        <div class="attachment-manager-body">
                            <div class="attachment-manager-toolbar">
                                <button class="btn-primary" onclick="addNewAttachment('${type}')">
                                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px; margin-right: 6px;">
                                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                                    </svg>
                                    新建${title}
                                </button>
                            </div>
                            <div class="attachment-list" id="attachmentList">
                                ${items.map((item, index) => `
                                    <div class="attachment-item" style="--item-index: ${index}">
                                        <div class="attachment-info">
                                            <div class="attachment-name">${item.name}</div>
                                            <div class="attachment-content">${item.content}</div>
                                        </div>
                                        <div class="attachment-actions">
                                            <button class="attachment-action-btn edit" onclick="editAttachment(${item.id}, '${type}')" title="编辑">
                                                <svg viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                                                </svg>
                                            </button>
                                            <button class="attachment-action-btn use" onclick="useAttachment(${item.id}, '${type}')" title="使用">
                                                <svg viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                                                </svg>
                                            </button>
                                            <button class="attachment-action-btn delete" onclick="deleteAttachment(${item.id}, '${type}')" title="删除">
                                                <svg viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 添加样式
            if (!document.getElementById('attachmentManagerStyles')) {
                const styles = document.createElement('style');
                styles.id = 'attachmentManagerStyles';
                styles.textContent = `
                    .attachment-manager-overlay {
                        position: fixed;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: rgba(0, 0, 0, 0.5);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 2000;
                        opacity: 0;
                        transition: opacity 0.3s ease;
                    }
                    
                    .attachment-manager-overlay.show {
                        opacity: 1;
                    }
                    
                    .attachment-manager-modal {
                        background: var(--bg-panel);
                        border-radius: 12px;
                        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
                        transform: scale(0.9);
                        transition: transform 0.3s ease;
                        max-width: 800px;
                        width: 90%;
                        max-height: 80vh;
                        display: flex;
                        flex-direction: column;
                    }
                    
                    .attachment-manager-overlay.show .attachment-manager-modal {
                        transform: scale(1);
                    }
                    
                    .attachment-manager-header {
                        padding: 20px 24px;
                        border-bottom: 1px solid var(--border-color);
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                    
                    .attachment-manager-title {
                        font-size: 18px;
                        font-weight: 600;
                        color: var(--text-dark);
                        margin: 0;
                    }
                    
                    .attachment-manager-close {
                        width: 32px;
                        height: 32px;
                        border: none;
                        background: transparent;
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: var(--text-light);
                        border-radius: 6px;
                        transition: all 0.2s;
                    }
                    
                    .attachment-manager-close:hover {
                        background: var(--bg-panel-secondary);
                        color: var(--text-dark);
                    }
                    
                    .attachment-manager-close svg {
                        width: 20px;
                        height: 20px;
                    }
                    
                    .attachment-manager-body {
                        padding: 24px;
                        flex: 1;
                        overflow: hidden;
                        display: flex;
                        flex-direction: column;
                    }
                    
                    .attachment-manager-toolbar {
                        margin-bottom: 20px;
                    }
                    
                    .attachment-list {
                        flex: 1;
                        overflow-y: auto;
                    }
                    
                    .attachment-item {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 16px;
                        background: var(--bg-panel-secondary);
                        border: 1px solid var(--border-color);
                        border-radius: 8px;
                        margin-bottom: 12px;
                        opacity: 0;
                        transform: translateX(-20px);
                        animation: itemSlideIn 0.3s ease-out forwards;
                        animation-delay: calc(0.05s * var(--item-index));
                        transition: all 0.2s;
                    }
                    
                    .attachment-item:hover {
                        border-color: var(--primary-color);
                        transform: translateX(0) translateY(-2px);
                    }
                    
                    @keyframes itemSlideIn {
                        to {
                            opacity: 1;
                            transform: translateX(0);
                        }
                    }
                    
                    .attachment-info {
                        flex: 1;
                    }
                    
                    .attachment-name {
                        font-weight: 500;
                        color: var(--text-dark);
                        margin-bottom: 6px;
                        font-size: 16px;
                    }
                    
                    .attachment-content {
                        font-size: 14px;
                        color: var(--text-light);
                        line-height: 1.4;
                    }
                    
                    .attachment-actions {
                        display: flex;
                        gap: 8px;
                    }
                    
                    .attachment-action-btn {
                        width: 36px;
                        height: 36px;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: all 0.2s;
                    }
                    
                    .attachment-action-btn svg {
                        width: 16px;
                        height: 16px;
                    }
                    
                    .attachment-action-btn.edit {
                        background: var(--primary-color);
                        color: white;
                    }
                    
                    .attachment-action-btn.edit:hover {
                        background: var(--primary-color-hover);
                    }
                    
                    .attachment-action-btn.use {
                        background: #4CAF50;
                        color: white;
                    }
                    
                    .attachment-action-btn.use:hover {
                        background: #45a049;
                    }
                    
                    .attachment-action-btn.delete {
                        background: #f44336;
                        color: white;
                    }
                    
                    .attachment-action-btn.delete:hover {
                        background: #da190b;
                    }
                `;
                document.head.appendChild(styles);
            }
            
            // 添加到页面
            document.body.insertAdjacentHTML('beforeend', modalHTML);
            
            // 显示动画
            const overlay = document.getElementById('attachmentManagerOverlay');
            setTimeout(() => {
                overlay.classList.add('show');
            }, 10);
        }
        
        // 关闭附件管理器
        function closeAttachmentManager() {
            const overlay = document.getElementById('attachmentManagerOverlay');
            if (overlay) {
                overlay.classList.remove('show');
                setTimeout(() => {
                    overlay.remove();
                }, 300);
            }
        }
        
        // 附件操作函数
        function addNewAttachment(type) {
            showCustomAlert(`新建${type}功能开发中...`);
        }
        
        function editAttachment(id, type) {
            showCustomAlert(`编辑${type} ID:${id} 功能开发中...`);
        }
        
        function useAttachment(id, type) {
            // 将附件内容添加到输入框
            const input = document.getElementById('messageInput');
            const currentText = input.value.trim();
            const attachmentText = `[使用${type} ID:${id}]`;
            
            if (currentText) {
                input.value = currentText + '\n\n' + attachmentText;
            } else {
                input.value = attachmentText;
            }
            
            // 自动调整输入框高度
            input.style.height = 'auto';
            input.style.height = Math.min(input.scrollHeight, 120) + 'px';
            
            // 关闭弹窗
            closeAttachmentManager();
            
            // 聚焦输入框
            input.focus();
            
            // 显示成功提示
            showNotification(`${type}已添加到输入框`, 'success');
        }
        
        async function deleteAttachment(id, type) {
            const confirmed = await showCustomConfirm(`确定要删除这个${type}吗？`, `删除${type}`);
            if (confirmed) {
                showCustomAlert(`删除${type} ID:${id} 功能开发中...`);
            }
        }
        
        // 通知函数
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-size: 14px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;
            
            document.body.appendChild(notification);
            
            // 显示动画
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 10);
            
            // 自动隐藏
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
        
        // 点击遮罩层关闭弹窗
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('attachment-manager-overlay')) {
                closeAttachmentManager();
            }
        });
        
        // 页面加载完成后显示默认模型信息
        document.addEventListener('DOMContentLoaded', function() {
            // 隐藏空状态
            if (emptyState.style.display !== 'none') {
                emptyState.style.display = 'none';
                messagesWrapper.style.display = 'flex';
            }
            
            // 显示默认模型信息
            const systemMsg = document.createElement('div');
            systemMsg.className = 'system-message';
            systemMsg.textContent = `当前模型：${currentModel}`;
            messagesWrapper.appendChild(systemMsg);
            messagesWrapper.scrollTop = messagesWrapper.scrollHeight;
        });
    </script>

    <!-- 自定义弹窗 -->
    <div class="custom-alert" id="customAlert">
        <div class="custom-alert-content">
            <div class="custom-alert-icon" id="customAlertIcon">
                ℹ
            </div>
            <div class="custom-alert-message" id="customAlertMessage">
                这是一条提醒消息
            </div>
            <button class="custom-alert-button" id="customAlertButton" onclick="closeCustomAlert()">
                确定
            </button>
        </div>
    </div>

</body>
</html>