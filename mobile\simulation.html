<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <title>模拟 - 笔尖传奇</title>
    <style>
        /* --- 移动端模拟界面CSS --- */
        :root {
            /* 基础颜色 */
            --bg-main: #F0F3F7;
            --bg-panel: #FAFBFC;
            --bg-content: #F7F9FB;
            --text-dark: #2D3748;
            --text-light: #718096;
            --text-on-primary: #FFFFFF;
            --border-color: #E8EBEF;
            --shadow-color: rgba(93, 156, 236, 0.08);
            --shadow-color-light: rgba(0, 0, 0, 0.04);
            --primary-color: #5D9CEC;
            --primary-color-hover: #4A89E2;
            --accent-color: #48BB78;
            --warning-color: #FF6B6B;
            
            /* 模拟界面专用颜色 */
            --character-bg-user: #E8F4FF;
            --character-bg-ai: #F0F8FF;
            --character-bg-system: #FFF8E8;
            --message-border-radius: 12px;
            
            /* 移动端尺寸 */
            --mobile-header-height: 56px;
            --mobile-input-height: 80px;
            --mobile-padding: 16px;
            --mobile-border-radius: 12px;
            --mobile-font-size: 16px;
            --mobile-font-size-sm: 14px;
            --mobile-font-size-lg: 18px;
        }

        [data-theme="green-leaf"] {
            --bg-main: #EFF3EF;
            --bg-panel: #F8FAF8;
            --bg-content: #F5F8F5;
            --text-dark: #3E4A3E;
            --text-light: #6B7C6B;
            --border-color: #E0E6E0;
            --primary-color: #6A9C89;
            --primary-color-hover: #5A8C79;
            --accent-color: #E99469;
            --shadow-color: rgba(106, 156, 137, 0.08);
            --character-bg-user: #E5F2E9;
            --character-bg-ai: #F0F8F0;
            --character-bg-system: #FFF8E8;
        }

        [data-theme="sepia"] {
            --bg-main: #FBF0D9;
            --bg-panel: #FAF4E8;
            --bg-content: #FAF4E8;
            --text-dark: #5C4B33;
            --text-light: #8B7355;
            --border-color: #EAE0C8;
            --primary-color: #A67B5B;
            --primary-color-hover: #966B4B;
            --accent-color: #5D9CEC;
            --shadow-color: rgba(166, 123, 91, 0.1);
            --character-bg-user: #F4E6D4;
            --character-bg-ai: #F8F0E0;
            --character-bg-system: #FFF8E8;
        }

        [data-theme="dark"] {
            --bg-main: #1A202C;
            --bg-panel: #2D3748;
            --bg-content: #323B4C;
            --text-dark: #E2E8F0;
            --text-light: #A0AEC0;
            --border-color: #4A5568;
            --primary-color: #4A5568;
            --primary-color-hover: #718096;
            --accent-color: #48BB78;
            --shadow-color: rgba(0, 0, 0, 0.2);
            --character-bg-user: #3A4558;
            --character-bg-ai: #404858;
            --character-bg-system: #4A4558;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", "Helvetica Neue", sans-serif;
            background-color: var(--bg-main);
            color: var(--text-dark);
            font-size: var(--mobile-font-size);
            line-height: 1.6;
            height: 100vh;
            overflow: hidden;
            transition: background-color 0.3s, color 0.3s;
        }

        /* --- 模拟容器 --- */
        .simulation-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* --- 顶部导航栏 --- */
        .simulation-header {
            height: var(--mobile-header-height);
            background: var(--bg-panel);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--mobile-padding);
            position: relative;
            z-index: 100;
            box-shadow: 0 2px 8px var(--shadow-color-light);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            width: 36px;
            height: 36px;
            border: none;
            background: transparent;
            color: var(--text-dark);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .back-btn:hover {
            background: var(--bg-content);
        }

        .simulation-title {
            font-size: var(--mobile-font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-btn {
            padding: 6px 12px;
            background: var(--bg-content);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: var(--mobile-font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .header-btn:hover {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        .header-btn.primary {
            background: var(--accent-color);
            color: var(--text-on-primary);
            border-color: var(--accent-color);
        }

        /* --- 角色指示器 --- */
        .character-indicator {
            background: var(--bg-panel);
            border-bottom: 1px solid var(--border-color);
            padding: 12px var(--mobile-padding);
            display: flex;
            align-items: center;
            gap: 12px;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .character-indicator::-webkit-scrollbar {
            display: none;
        }

        .indicator-label {
            font-size: var(--mobile-font-size-sm);
            color: var(--text-light);
            font-weight: 500;
            white-space: nowrap;
        }

        .active-characters {
            display: flex;
            gap: 8px;
            flex-wrap: nowrap;
        }

        .character-chip {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            background: var(--bg-content);
            border-radius: 20px;
            font-size: var(--mobile-font-size-sm);
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
            flex-shrink: 0;
        }

        .character-chip:hover {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        .character-chip-avatar {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary-color);
            color: var(--text-on-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: 600;
        }

        .character-chip-name {
            font-weight: 500;
        }

        .character-chip-model {
            font-size: 10px;
            color: var(--text-light);
            padding: 2px 4px;
            background: var(--bg-panel);
            border-radius: 8px;
        }

        /* --- 聊天区域 --- */
        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: var(--mobile-padding);
            scroll-behavior: smooth;
        }

        .chat-wrapper {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        /* --- 消息样式 --- */
        .chat-message {
            display: flex;
            gap: 12px;
            animation: fadeInUp 0.3s ease-out;
            max-width: 90%;
        }

        .chat-message.user {
            flex-direction: row-reverse;
            align-self: flex-end;
        }

        .chat-message.ai {
            align-self: flex-start;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .chat-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 12px;
            box-shadow: 0 2px 8px var(--shadow-color);
        }

        .chat-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .chat-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
        }

        .chat-name {
            font-weight: 600;
            font-size: var(--mobile-font-size-sm);
            color: var(--text-dark);
        }

        .chat-model {
            font-size: 11px;
            color: var(--text-light);
            padding: 2px 6px;
            background: var(--bg-content);
            border-radius: 8px;
        }

        .chat-time {
            font-size: 11px;
            color: var(--text-light);
            margin-left: auto;
        }

        .chat-bubble {
            padding: 12px 16px;
            border-radius: var(--message-border-radius);
            font-size: var(--mobile-font-size);
            line-height: 1.5;
            word-wrap: break-word;
            box-shadow: 0 1px 3px var(--shadow-color-light);
        }

        .chat-message.user .chat-bubble {
            background: var(--character-bg-user);
            border-bottom-right-radius: 4px;
        }

        .chat-message.ai .chat-bubble {
            background: var(--character-bg-ai);
            border-bottom-left-radius: 4px;
        }

        .chat-message.system .chat-bubble {
            background: var(--character-bg-system);
            text-align: center;
            font-style: italic;
        }

        /* --- 输入区域 --- */
        .input-section {
            background: var(--bg-panel);
            border-top: 1px solid var(--border-color);
            padding: var(--mobile-padding);
            box-shadow: 0 -2px 8px var(--shadow-color-light);
            min-height: var(--mobile-input-height);
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-textarea {
            flex: 1;
            min-height: 40px;
            max-height: 120px;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 12px 16px;
            font-size: var(--mobile-font-size);
            font-family: inherit;
            background: var(--bg-content);
            color: var(--text-dark);
            resize: none;
            outline: none;
            transition: border-color 0.2s;
        }

        .input-textarea:focus {
            border-color: var(--primary-color);
        }

        .send-btn {
            width: 44px;
            height: 44px;
            border: none;
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        .send-btn:hover {
            background: var(--primary-color-hover);
            transform: scale(1.05);
        }

        .send-btn:disabled {
            background: var(--text-light);
            cursor: not-allowed;
            transform: none;
        }

        /* --- 空状态 --- */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            padding: 40px 20px;
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: var(--mobile-font-size-lg);
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-dark);
        }

        .empty-desc {
            font-size: var(--mobile-font-size-sm);
            color: var(--text-light);
            line-height: 1.5;
            margin-bottom: 24px;
        }

        .empty-action {
            padding: 12px 24px;
            background: var(--accent-color);
            color: var(--text-on-primary);
            border: none;
            border-radius: var(--mobile-border-radius);
            font-size: var(--mobile-font-size);
            cursor: pointer;
            transition: all 0.2s;
        }

        .empty-action:hover {
            background: var(--accent-color);
            transform: translateY(-2px);
        }

        /* --- 响应式优化 --- */
        @media (max-width: 480px) {
            .chat-message {
                max-width: 95%;
            }
            
            .chat-avatar {
                width: 32px;
                height: 32px;
                font-size: 11px;
            }
            
            .chat-bubble {
                padding: 10px 14px;
                font-size: 15px;
            }
        }

        /* --- 加载状态 --- */
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid var(--text-light);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="simulation-container">
        <!-- 顶部导航栏 -->
        <header class="simulation-header">
            <div class="header-left">
                <button class="back-btn" onclick="goBack()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
                    </svg>
                </button>
                <h1 class="simulation-title">角色模拟</h1>
            </div>
            <div class="header-right">
                <button class="header-btn" onclick="selectCharacter()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
                    </svg>
                    选择角色
                </button>
                <button class="header-btn primary" onclick="createCharacter()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                    创建角色
                </button>
                <button class="header-btn" onclick="showModelSelector()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    模型
                </button>
            </div>
        </header>

        <!-- 角色指示器 -->
        <div class="character-indicator">
            <span class="indicator-label">当前角色:</span>
            <div class="active-characters" id="activeCharacters">
                <span class="empty-characters">暂无角色，请先创建或选择角色</span>
            </div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-container" id="chatContainer">
            <div class="chat-wrapper" id="chatWrapper">
                <!-- 空状态 -->
                <div class="empty-state" id="emptyState">
                    <div class="empty-icon">🎭</div>
                    <div class="empty-title">开始角色模拟</div>
                    <div class="empty-desc">创建或选择角色，开始沉浸式的角色扮演体验</div>
                    <button class="empty-action" onclick="createCharacter()">创建第一个角色</button>
                </div>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-section">
            <div class="input-container">
                <textarea
                    class="input-textarea"
                    id="messageInput"
                    placeholder="输入消息与角色对话..."
                    rows="1"
                    onkeydown="handleKeyDown(event)"
                    oninput="autoResize(this)"
                ></textarea>
                <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let characters = [];
        let activeCharacters = [];
        let messages = [];
        let isLoading = false;

        // AI模型配置 - 六个核心模型
        const availableModels = [
            { id: 'delicate', name: '细腻贴合', desc: '注重情感细节和角色深度', icon: '🎭', model: 'doubao-1-5-thinking-pro-250415' },
            { id: 'precise', name: '逻辑精准', desc: '擅长逻辑推理和分析', icon: '🧠', model: 'gemini-2.5-pro' },
            { id: 'creative', name: '灵活创意', desc: '富有创造力和想象力', icon: '✨', model: 'claude-3-7-sonnet-20250219' },
            { id: 'stable', name: '稳定版', desc: '平衡稳定的通用模型', icon: '⚖️', model: 'gemini-2.5-flash' },
            { id: 'comprehensive', name: '综合版', desc: '多功能综合型模型', icon: '🌟', model: 'claude-sonnet-4-20250514' },
            { id: 'test', name: '测试版', desc: '最新实验性功能', icon: '🔬', model: 'doubao-seed-1-6-250615' }
        ];

        // API配置
        const API_BASE_URL = 'http://bjcqapi.cn/v1/chat/completions';
        const API_KEYS = [
            'sk-proj-1234567890abcdef',
            'sk-proj-abcdef1234567890',
            'sk-proj-fedcba0987654321'
        ];
        let currentKeyIndex = 0;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTheme();
            loadCharacters();
            loadMessages();
            updateCharacterIndicator();
            renderMessages();
        });

        // 主题管理
        function loadTheme() {
            const savedTheme = localStorage.getItem('mobile-theme') || 'default';
            document.documentElement.setAttribute('data-theme', savedTheme);
        }

        // 返回功能
        function goBack() {
            window.location.href = 'index.html';
        }

        // 加载角色数据
        function loadCharacters() {
            const savedCharacters = localStorage.getItem('mobile-simulation-characters');
            if (savedCharacters) {
                characters = JSON.parse(savedCharacters);
            }

            const savedActiveCharacters = localStorage.getItem('mobile-simulation-active-characters');
            if (savedActiveCharacters) {
                activeCharacters = JSON.parse(savedActiveCharacters);
            }
        }

        // 保存角色数据
        function saveCharacters() {
            localStorage.setItem('mobile-simulation-characters', JSON.stringify(characters));
            localStorage.setItem('mobile-simulation-active-characters', JSON.stringify(activeCharacters));
        }

        // 加载消息历史
        function loadMessages() {
            const savedMessages = localStorage.getItem('mobile-simulation-messages');
            if (savedMessages) {
                messages = JSON.parse(savedMessages);
            }
        }

        // 保存消息历史
        function saveMessages() {
            localStorage.setItem('mobile-simulation-messages', JSON.stringify(messages));
        }

        // 更新角色指示器
        function updateCharacterIndicator() {
            const container = document.getElementById('activeCharacters');

            if (activeCharacters.length === 0) {
                container.innerHTML = '<span class="empty-characters">暂无角色，请先创建或选择角色</span>';
                return;
            }

            container.innerHTML = activeCharacters.map(character => `
                <div class="character-chip" onclick="selectCharacterModel('${character.id}')">
                    <div class="character-chip-avatar">${character.avatar || character.name.charAt(0)}</div>
                    <span class="character-chip-name">${character.name}</span>
                    <span class="character-chip-model">${character.selectedModel || '细腻贴合'}</span>
                </div>
            `).join('');
        }

        // 渲染消息
        function renderMessages() {
            const wrapper = document.getElementById('chatWrapper');
            const emptyState = document.getElementById('emptyState');

            if (messages.length === 0) {
                emptyState.style.display = 'flex';
                return;
            }

            emptyState.style.display = 'none';

            const messagesHtml = messages.map(message => {
                const isUser = message.type === 'user';
                const character = isUser ? null : activeCharacters.find(c => c.id === message.characterId);

                return `
                    <div class="chat-message ${message.type}">
                        <div class="chat-avatar" style="background: ${isUser ? 'var(--primary-color)' : 'var(--accent-color)'}">
                            ${isUser ? '我' : (character ? character.avatar || character.name.charAt(0) : 'AI')}
                        </div>
                        <div class="chat-content">
                            <div class="chat-header">
                                <span class="chat-name">${isUser ? '我' : (character ? character.name : 'AI')}</span>
                                ${character ? `<span class="chat-model">${character.selectedModel || '细腻贴合'}</span>` : ''}
                                <span class="chat-time">${formatTime(message.timestamp)}</span>
                            </div>
                            <div class="chat-bubble">${message.content}</div>
                        </div>
                    </div>
                `;
            }).join('');

            wrapper.innerHTML = messagesHtml;
            scrollToBottom();
        }

        // 格式化时间
        function formatTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        }

        // 滚动到底部
        function scrollToBottom() {
            const container = document.getElementById('chatContainer');
            setTimeout(() => {
                container.scrollTop = container.scrollHeight;
            }, 100);
        }

        // 自动调整输入框高度
        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        // 处理键盘事件
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const content = input.value.trim();

            if (!content || isLoading) return;

            if (activeCharacters.length === 0) {
                showToast('请先创建或选择角色');
                return;
            }

            // 添加用户消息
            const userMessage = {
                id: generateId(),
                type: 'user',
                content: content,
                timestamp: Date.now()
            };

            messages.push(userMessage);
            input.value = '';
            input.style.height = 'auto';

            renderMessages();
            saveMessages();

            // 设置加载状态
            isLoading = true;
            updateSendButton();

            // 为每个活跃角色生成回复
            for (const character of activeCharacters) {
                try {
                    const response = await generateCharacterResponse(character, content);

                    const aiMessage = {
                        id: generateId(),
                        type: 'ai',
                        characterId: character.id,
                        content: response,
                        timestamp: Date.now()
                    };

                    messages.push(aiMessage);
                    renderMessages();
                    saveMessages();
                } catch (error) {
                    console.error('生成角色回复失败:', error);
                    showToast(`${character.name} 回复失败`);
                }
            }

            isLoading = false;
            updateSendButton();
        }

        // 更新发送按钮状态
        function updateSendButton() {
            const btn = document.getElementById('sendBtn');
            if (isLoading) {
                btn.innerHTML = '<div class="loading"></div>';
                btn.disabled = true;
            } else {
                btn.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                `;
                btn.disabled = false;
            }
        }

        // 生成角色回复
        async function generateCharacterResponse(character, userMessage) {
            const model = availableModels.find(m => m.name === (character.selectedModel || '细腻贴合'));
            const modelId = model ? model.model : 'doubao-seed-1-6-250615';

            const systemPrompt = `你是${character.name}，${character.description || '一个有趣的角色'}。

角色设定：
- 性格：${character.personality || '友善、聪明'}
- 背景：${character.background || '普通人'}
- 说话风格：${character.style || '自然、亲切'}

请严格按照这个角色的设定来回复，保持角色的一致性。回复要简洁自然，符合移动端对话习惯。`;

            const messages = [
                { role: 'system', content: systemPrompt },
                { role: 'user', content: userMessage }
            ];

            const response = await callAI(messages, modelId);
            return response;
        }

        // 调用AI API
        async function callAI(messages, model) {
            const apiKey = getNextApiKey();

            const response = await fetch(API_BASE_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`,
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                },
                body: JSON.stringify({
                    model: model,
                    messages: messages,
                    temperature: 0.8,
                    max_tokens: 200
                })
            });

            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }

            const data = await response.json();
            return data.choices[0].message.content;
        }

        // 获取下一个API密钥
        function getNextApiKey() {
            const key = API_KEYS[currentKeyIndex];
            currentKeyIndex = (currentKeyIndex + 1) % API_KEYS.length;
            return key;
        }

        // 生成唯一ID
        function generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        // 选择角色
        function selectCharacter() {
            if (characters.length === 0) {
                showToast('暂无角色，请先创建角色');
                return;
            }

            const options = characters.map((char, index) =>
                `${index + 1}. ${char.name} - ${char.description || '无描述'}`
            ).join('\n');

            const choice = prompt(`选择角色:\n\n${options}\n\n请输入数字(1-${characters.length}):`);

            if (choice && choice >= 1 && choice <= characters.length) {
                const selectedCharacter = characters[choice - 1];

                // 检查是否已经激活
                if (!activeCharacters.find(c => c.id === selectedCharacter.id)) {
                    activeCharacters.push(selectedCharacter);
                    saveCharacters();
                    updateCharacterIndicator();
                    showToast(`已激活角色: ${selectedCharacter.name}`);
                } else {
                    showToast('该角色已经激活');
                }
            }
        }

        // 创建角色
        function createCharacter() {
            const name = prompt('请输入角色名称:');
            if (!name || !name.trim()) return;

            const description = prompt('请输入角色描述:') || '';
            const personality = prompt('请输入角色性格:') || '友善、聪明';
            const background = prompt('请输入角色背景:') || '普通人';
            const style = prompt('请输入说话风格:') || '自然、亲切';

            const newCharacter = {
                id: generateId(),
                name: name.trim(),
                description: description.trim(),
                personality: personality.trim(),
                background: background.trim(),
                style: style.trim(),
                avatar: name.charAt(0),
                selectedModel: '细腻贴合',
                createdAt: Date.now()
            };

            characters.push(newCharacter);
            activeCharacters.push(newCharacter);
            saveCharacters();
            updateCharacterIndicator();
            showToast(`角色 ${newCharacter.name} 创建成功！`);
        }

        // 显示模型选择器
        function showModelSelector() {
            if (activeCharacters.length === 0) {
                showToast('请先选择或创建角色');
                return;
            }

            const models = availableModels.map((model, index) =>
                `${index + 1}. ${model.icon} ${model.name} - ${model.desc}`
            ).join('\n');

            const choice = prompt(`选择AI模型:\n\n${models}\n\n请输入数字(1-${availableModels.length}):`);

            if (choice && choice >= 1 && choice <= availableModels.length) {
                const selectedModel = availableModels[choice - 1];

                // 为所有活跃角色设置模型
                activeCharacters.forEach(character => {
                    character.selectedModel = selectedModel.name;
                });

                saveCharacters();
                updateCharacterIndicator();
                showToast(`已切换到 ${selectedModel.name} 模型`);
            }
        }

        // 为特定角色选择模型
        function selectCharacterModel(characterId) {
            const character = activeCharacters.find(c => c.id === characterId);
            if (!character) return;

            const models = availableModels.map((model, index) =>
                `${index + 1}. ${model.icon} ${model.name} - ${model.desc}`
            ).join('\n');

            const choice = prompt(`为 ${character.name} 选择AI模型:\n\n${models}\n\n请输入数字(1-${availableModels.length}):`);

            if (choice && choice >= 1 && choice <= availableModels.length) {
                const selectedModel = availableModels[choice - 1];
                character.selectedModel = selectedModel.name;

                saveCharacters();
                updateCharacterIndicator();
                showToast(`${character.name} 已切换到 ${selectedModel.name} 模型`);
            }
        }

        // 提示消息
        function showToast(message) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: var(--text-dark);
                color: var(--text-on-primary);
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 1000;
                animation: fadeIn 0.3s ease-out;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 2000);
        }
    </script>
</body>
</html>
