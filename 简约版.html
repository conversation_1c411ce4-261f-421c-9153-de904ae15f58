<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 智能写作助手 (流程引导版)</title>
    <style>
        /* --- 1. 全局与动态配色系统 (与专业版统一) --- */
        :root {
            /* 默认皮肤 (护眼蓝灰) */
            --bg-main: #F0F3F7;
            --bg-panel: #FAFBFC;
            --bg-panel-secondary: #F3F5F8;
            --bg-content: #F7F9FB;
            --text-dark: #2D3748;
            --text-light: #718096;
            --text-on-primary: #FFFFFF;
            --border-color: #E8EBEF;
            --gutter-color: transparent;
            --shadow-color: rgba(93, 156, 236, 0.08);
            --shadow-color-light: rgba(0, 0, 0, 0.04);
            --shadow-color-heavy: rgba(0, 0, 0, 0.12);
            /* 主题色 */
            --primary-color: #5D9CEC;
            --primary-color-hover: #4A89E2;
            --secondary-color: #8696A7;
            --accent-color: #48BB78;
            --accent-color-hover: #3AA967;
            --warning-color: #FF6B6B;
            --warning-color-hover: #FF5252;
            /* 内容区专用色 */
            --content-header-bg: #E8F2FF;
            --content-header-color: #2B5797;
            /* 布局尺寸 */
            --font-size-base: 16px;
            --font-size-sm: 14px;
            --font-size-lg: 18px;
            --header-height: 52px;
            --sidebar-width: 80px;
            --sidebar-collapsed-width: 4px;
        }

        [data-theme="green-leaf"] {
            --bg-main: #EFF3EF;
            --bg-panel: #F8FAF8;
            --bg-panel-secondary: #F0F3F0;
            --bg-content: #F5F8F5;
            --text-dark: #3E4A3E;
            --text-light: #6B7C6B;
            --border-color: #E0E6E0;
            --primary-color: #6A9C89;
            --primary-color-hover: #5A8C79;
            --secondary-color: #8A9B94;
            --accent-color: #E99469;
            --accent-color-hover: #D98459;
            --shadow-color: rgba(106, 156, 137, 0.08);
            --content-header-bg: #E5F2E9;
            --content-header-color: #3A6B4F;
        }

        [data-theme="sepia"] {
            --bg-main: #FBF0D9;
            --bg-panel: #FAF4E8;
            --bg-panel-secondary: #F6ECDA;
            --bg-content: #FAF4E8;
            --text-dark: #5C4B33;
            --text-light: #8B7355;
            --border-color: #EAE0C8;
            --primary-color: #A67B5B;
            --primary-color-hover: #966B4B;
            --secondary-color: #B0A08D;
            --accent-color: #5D9CEC;
            --accent-color-hover: #4A89E2;
            --shadow-color: rgba(166, 123, 91, 0.1);
            --content-header-bg: #F4E6D4;
            --content-header-color: #7A5A3A;
        }

        [data-theme="dark"] {
            --bg-main: #1A202C;
            --bg-panel: #2D3748;
            --bg-panel-secondary: #252E3E;
            --bg-content: #323B4C;
            --text-dark: #E2E8F0;
            --text-light: #A0AEC0;
            --border-color: #4A5568;
            --primary-color: #4A5568;
            --primary-color-hover: #718096;
            --secondary-color: #3B475C;
            --accent-color: #48BB78;
            --accent-color-hover: #3AA967;
            --shadow-color: rgba(0, 0, 0, 0.2);
            --content-header-bg: #3A4558;
            --content-header-color: #CBD5E0;
        }

        /* --- 2. 基础样式 --- */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", "Helvetica Neue", sans-serif;
            background-color: var(--bg-main);
            color: var(--text-dark);
            font-size: var(--font-size-base);
            line-height: 1.7;
            -webkit-font-smoothing: antialiased;
            display: flex;
            height: 100vh;
            overflow: hidden;
            transition: background-color 0.3s, color 0.3s;
        }

        /* --- 左侧导航栏 (可自动隐藏) --- */
        .sidebar-wrapper {
            position: relative;
            width: var(--sidebar-width);
            flex-shrink: 0;
            transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar-wrapper.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar {
            position: absolute;
            left: 0;
            top: 0;
            width: var(--sidebar-width);
            height: 100%;
            background: var(--bg-panel);
            box-shadow: 2px 0 8px var(--shadow-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 0;
            z-index: 100;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        /* 隐藏状态 */
        .sidebar-wrapper.collapsed .sidebar {
            transform: translateX(calc(-1 * var(--sidebar-width) + var(--sidebar-collapsed-width)));
        }

        /* 鼠标感应区域 - 修复：使用更大的热区 */
        .sidebar-trigger {
            position: fixed;
            left: 0;
            top: 0;
            width: 20px; /* 减小宽度，避免干扰内容 */
            height: 100%;
            z-index: 101;
        }

        /* 导航栏展开时的热区扩大 */
        .sidebar-wrapper:not(.collapsed) .sidebar-trigger {
            width: calc(var(--sidebar-width) + 20px);
        }

        /* 导航栏内容淡入淡出 */
        .sidebar-content {
            opacity: 1;
            transition: opacity 0.2s;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100%;
        }
        .sidebar-wrapper.collapsed .sidebar-content {
            opacity: 0;
            pointer-events: none;
        }

        /* 用户头像 - 调整大小 */
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px var(--shadow-color);
            transition: transform 0.2s;
            flex-shrink: 0;
        }
        .user-avatar:hover {
            transform: scale(1.05);
        }
        .user-avatar img { 
            width: 100%; 
            height: 100%; 
            object-fit: cover; 
        }

        /* 导航组 */
        .nav-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            width: 100%;
        }

        /* 导航项 */
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            color: var(--text-light);
            transition: all 0.2s ease;
            padding: 8px;
            border-radius: 10px;
            width: 60px;
        }
        .nav-item:hover {
            color: var(--primary-color);
            background-color: var(--bg-panel-secondary);
        }
        .nav-item.active {
            color: var(--primary-color);
            background-color: var(--bg-panel-secondary);
        }
        .nav-item-icon {
            font-size: 20px;
            width: 24px;
            height: 24px;
            fill: currentColor;
        }
        .nav-item-text {
            font-size: 11px;
            font-weight: 500;
        }
        .sidebar-footer {
            margin-top: auto;
        }

        /* --- 主布局 --- */
        .app-container { 
            display: flex; 
            gap: 0; 
            width: 100%; 
            height: 100vh;
            flex-grow: 1;
        }

        /* --- 面板通用样式 (与专业版统一) --- */
        .panel { 
            background-color: var(--bg-panel); 
            box-shadow: 0 2px 8px var(--shadow-color);
            display: flex; 
            flex-direction: column; 
            overflow: hidden; 
            height: 100%;
            transition: background-color 0.3s;
        }
        
        .panel-header { 
            height: var(--header-height);
            padding: 0 20px; 
            font-weight: 600; 
            font-size: var(--font-size-lg); 
            flex-shrink: 0;
            display: flex;
            align-items: center;
            transition: background-color 0.3s, color 0.3s;
            background-color: var(--primary-color);
            color: var(--text-on-primary);
            border-bottom: 1px solid var(--border-color);
        }
        
        .panel-content { 
            padding: 24px; 
            overflow-y: auto; 
            flex-grow: 1; 
        }

        /* 本章内容专用标题栏 */
        .content-header {
            background-color: var(--content-header-bg);
            color: var(--content-header-color);
            border-bottom: 1px solid var(--border-color);
        }

        /* --- 左侧管理面板 --- */
        .management-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            width: 100%;
        }
        .btn-new { 
            background-color: rgba(255,255,255,0.2); 
            color: var(--text-on-primary); 
            border: none; 
            border-radius: 6px; 
            padding: 6px 12px; 
            font-size: var(--font-size-sm); 
            cursor: pointer; 
            transition: background-color 0.2s;
            margin-left: 8px;
        }
        .btn-new:hover { 
            background-color: rgba(255,255,255,0.3); 
        }
        .item-list { 
            list-style: none; 
            padding: 0; 
            margin: 0; 
        }
        .item-list li { 
            padding: 12px 20px; 
            cursor: pointer; 
            border-bottom: 1px solid var(--border-color); 
            transition: background-color 0.2s; 
            font-size: var(--font-size-base); 
        }
        .item-list li:last-child { 
            border-bottom: none; 
        }
        .item-list li:hover { 
            background-color: var(--bg-panel-secondary); 
        }
        .item-list li.active { 
            background-color: var(--primary-color); 
            font-weight: 600; 
            color: var(--text-on-primary);
        }

        /* --- 中间工作区 --- */
        .center-panel { 
            display: flex; 
            flex-direction: column; 
            background-color: transparent; 
            box-shadow: none; 
            padding: 0; 
            gap: 0;
            flex-grow: 1;
            position: relative;
        }
        
        /* 工具栏样式 (与专业版统一) */
        .toolbar { 
            height: var(--header-height);
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: var(--primary-color);
            color: var(--text-on-primary);
            flex-shrink: 0;
        }
        
        .toolbar .btn-group {
            display: flex;
            gap: 10px;
        }
        
        .toolbar button { 
            background-color: rgba(255,255,255,0.2);
            color: var(--text-on-primary);
            border: none;
            cursor: pointer;
            font-size: var(--font-size-sm);
            padding: 8px 14px;
            border-radius: 8px;
            transition: background-color 0.2s, transform 0.2s;
            font-weight: 500;
        }
        .toolbar button:hover { 
            background-color: rgba(255,255,255,0.35);
            transform: translateY(-1px);
        }
        
        /* 显示/隐藏按钮 */
        #toggle-io-btn {
            background-color: var(--warning-color);
            padding: 10px 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            font-weight: 600;
        }
        #toggle-io-btn:hover {
            background-color: var(--warning-color-hover);
        }
        
        /* 主题切换器 */
        .theme-selector {
            display: flex;
            gap: 10px;
        }
        
        /* 中间内容区域 - 基础层 */
        .center-content-wrapper { 
            flex-grow: 1; 
            display: flex; 
            flex-direction: column; 
            gap: 0; 
            overflow: hidden;
            position: relative;
        }
        
        /* 本章内容区域 */
        #chapter-content-wrapper {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            background: var(--bg-content);
            overflow: hidden;
            transition: background-color 0.3s;
        }
        
        /* 悬浮层：输入输出区域 */
        .floating-input-output-container {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60%;
            display: flex;
            flex-direction: column;
            transform: translateY(0);
            opacity: 1;
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.4s;
            z-index: 10;
            box-shadow: 0 -8px 20px var(--shadow-color-heavy);
        }
        
        .floating-input-output-container.is-hidden {
            transform: translateY(100%);
            opacity: 0;
            pointer-events: none;
        }
        
        .floating-io-split-view {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            gap: 0;
            overflow: hidden;
        }
        
        .text-area-wrapper { 
            display: flex; 
            flex-direction: column; 
            background: var(--bg-panel); 
            overflow: hidden;
            height: 100%;
            transition: background-color 0.3s;
        }
        
        textarea { 
            flex-grow: 1; 
            width: 100%; 
            border: none; 
            padding: 20px; 
            font-size: var(--font-size-base); 
            line-height: 1.7; 
            resize: none; 
            color: var(--text-dark); 
            background-color: transparent;
        }
        textarea:focus { 
            outline: none; 
        }
        
        /* 底部操作按钮 */
        .action-buttons { 
            background-color: var(--bg-panel);
            padding: 12px 20px;
            display: flex; 
            justify-content: flex-end; 
            gap: 12px; 
            border-top: 1px solid var(--border-color);
        }
        .btn-secondary { 
            background-color: var(--bg-panel);
            color: var(--primary-color);
            border: 1px solid var(--border-color);
            cursor: pointer;
            font-size: var(--font-size-sm);
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.2s;
            font-weight: 500;
        }
        .btn-secondary:hover { 
            background-color: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        /* --- 右侧控制面板 --- */
        .content-section-header {
            font-size: var(--font-size-lg);
            font-weight: 600;
            padding-bottom: 12px;
            margin-bottom: 16px;
            border-bottom: 1px solid var(--border-color);
            margin-top: 28px;
        }
        .content-section-header:first-child {
            margin-top: 0;
        }
        
        /* 便签管理区域 */
        .notes-section {
            background-color: var(--bg-panel-secondary);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }
        .notes-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        .notes-header h3 {
            margin: 0;
            font-size: var(--font-size-lg);
        }
        .add-note-btn {
            background-color: var(--accent-color);
            color: var(--text-on-primary);
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .add-note-btn:hover {
            background-color: var(--accent-color-hover);
        }
        
        /* 流程引导区域 */
        .process-title { 
            font-size: var(--font-size-sm); 
            font-weight: 600; 
            color: var(--text-dark); 
            padding-bottom: 16px; 
            border-bottom: 1px solid var(--border-color); 
            margin-bottom: 20px; 
        }
        .process-steps { 
            display: flex; 
            flex-direction: column; 
            gap: 20px; 
        }
        .step-card { 
            background-color: var(--bg-panel-secondary); 
            border: 1px solid var(--border-color); 
            border-radius: 12px; 
            padding: 16px; 
            transition: border-color 0.3s, box-shadow 0.3s; 
        }
        .step-card.active { 
            border-color: var(--primary-color); 
            box-shadow: 0 0 0 3px var(--shadow-color); 
        }
        .step-card h4 { 
            margin: 0 0 10px 0; 
            font-size: var(--font-size-base); 
            color: var(--text-dark); 
        }
        .step-card .instructions { 
            font-size: var(--font-size-sm); 
            color: var(--text-light); 
            line-height: 1.6; 
            margin: 0 0 16px 0; 
            padding: 12px; 
            background-color: var(--bg-content); 
            border-radius: 8px; 
        }
        .step-button { 
            width: 100%; 
            padding: 12px; 
            background-color: var(--accent-color); 
            color: var(--text-on-primary); 
            border: none; 
            border-radius: 10px; 
            font-size: var(--font-size-base); 
            font-weight: 600; 
            cursor: pointer; 
            transition: all 0.2s; 
        }
        .step-button:hover { 
            background-color: var(--accent-color-hover); 
            transform: translateY(-2px);
        }
        .final-note { 
            font-size: var(--font-size-sm); 
            color: var(--text-light); 
            font-style: italic; 
            margin-top: 20px; 
            text-align: center; 
        }

        /* 控制组样式 */
        .control-group { 
            margin-bottom: 20px; 
        }
        .control-group label { 
            display: block; 
            font-size: var(--font-size-sm); 
            font-weight: 500; 
            margin-bottom: 10px; 
            color: var(--text-dark); 
        }
        .control-group input[type="text"], 
        .control-group select { 
            width: 100%; 
            padding: 12px; 
            border: 1px solid var(--border-color); 
            border-radius: 8px; 
            background-color: var(--bg-panel); 
            font-size: var(--font-size-sm); 
            color: var(--text-dark);
            transition: all 0.2s;
        }
        .control-group input[type="text"]:focus, 
        .control-group select:focus { 
            outline: none; 
            border-color: var(--primary-color); 
            box-shadow: 0 0 0 3px var(--shadow-color); 
        }

        /* --- Split.js 拖动条样式 (透明化) --- */
        .gutter { 
            background-color: transparent;
            position: relative;
        }
        .gutter.gutter-horizontal { 
            cursor: col-resize;
            width: 12px !important;
        }
        .gutter.gutter-vertical { 
            cursor: row-resize;
            height: 12px !important;
        }
        /* 悬停时显示拖动提示 */
        .gutter.gutter-horizontal:hover::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 2px;
            height: 40px;
            background-color: var(--primary-color);
            opacity: 0.3;
            border-radius: 1px;
        }
        .gutter.gutter-vertical:hover::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 2px;
            background-color: var(--primary-color);
            opacity: 0.3;
            border-radius: 1px;
        }

        /* 移除左右边框 */
        .left-panel {
            border-right: none !important;
        }
        .right-panel {
            border-left: none !important;
        }

        /* ========== 通知弹窗样式 ========== */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            padding: 16px 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            z-index: 10000;
            transform: translateX(400px);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            max-width: 400px;
            border-left: 4px solid var(--primary-color);
        }
        
        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        .notification.success {
            border-left-color: #10b981;
        }
        
        .notification.error {
            border-left-color: #ef4444;
        }
        
        .notification.info {
            border-left-color: #3b82f6;
        }
        
        .notification-icon {
            width: 20px;
            height: 20px;
            flex-shrink: 0;
        }
        
        .notification-icon.success {
            color: #10b981;
        }
        
        .notification-icon.error {
            color: #ef4444;
        }
        
        .notification-icon.info {
            color: #3b82f6;
        }
        
        .notification-message {
            font-size: 14px;
            color: var(--text-dark);
            line-height: 1.4;
        }

        /* ========== 确认对话框样式 ========== */
        .prompt-detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(4px);
        }
        
        .prompt-detail-modal.show {
            display: flex;
            animation: fadeIn 0.3s ease-out;
        }
        
        .prompt-detail-content {
            background: white;
            border-radius: 16px;
            padding: 24px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            transform: scale(0.9);
            animation: scaleIn 0.3s ease-out forwards;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes scaleIn {
            from { transform: scale(0.9); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }
        
        .prompt-detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .prompt-detail-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
        }
        
        .prompt-detail-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--text-light);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s;
        }
        
        .prompt-detail-close:hover {
            background: var(--bg-hover);
            color: var(--text-dark);
        }
        
        .prompt-detail-body {
            margin-bottom: 24px;
        }
        
        .prompt-detail-section {
            margin-bottom: 16px;
        }
        
        .prompt-detail-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-light);
            margin-bottom: 8px;
            display: block;
        }
        
        .prompt-detail-text {
            font-size: 14px;
            color: var(--text-dark);
            line-height: 1.6;
            background: var(--bg-panel);
            padding: 12px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        /* ========== 通用模态框样式 ========== */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            backdrop-filter: blur(4px);
        }
        
        .modal.show {
            display: flex;
            animation: fadeIn 0.3s ease-out;
        }
        
        .modal-content {
            background: white;
            border-radius: 16px;
            width: 90%;
            max-width: 1000px;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            transform: scale(0.9);
            animation: scaleIn 0.3s ease-out forwards;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-panel);
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--text-light);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s;
        }
        
        .modal-close:hover {
            background: var(--bg-hover);
            color: var(--text-dark);
        }
        
        .modal-body {
            padding: 24px;
            overflow-y: auto;
            max-height: calc(90vh - 140px);
        }
        
        .modal-tabs {
            display: flex;
            gap: 2px;
            margin-bottom: 24px;
            background: var(--bg-panel);
            border-radius: 8px;
            padding: 4px;
        }
        
        .modal-tab {
            flex: 1;
            padding: 12px 16px;
            background: transparent;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-light);
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .modal-tab.active {
            background: white;
            color: var(--text-dark);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .modal-section {
            display: none;
        }
        
        .modal-section.active {
            display: block;
        }

        /* ========== 提示词列表样式 ========== */
        .prompt-list {
            display: grid;
            gap: 12px;
        }
        
        .prompt-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            background: var(--bg-panel);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            transition: all 0.2s;
            cursor: pointer;
            animation: slideInUp 0.3s ease-out;
        }
        
        .prompt-item:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .prompt-info {
            flex: 1;
        }
        
        .prompt-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0 0 4px 0;
        }
        
        .prompt-description {
            font-size: 14px;
            color: var(--text-light);
            margin: 0;
            line-height: 1.4;
        }
        
        .prompt-actions {
            display: flex;
            gap: 8px;
        }

        /* ========== 创建提示词表单样式 ========== */
        .create-prompt-form {
            display: grid;
            gap: 20px;
        }
        
        .form-group {
            display: grid;
            gap: 8px;
        }
        
        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-dark);
        }
        
        .form-input,
        .form-textarea {
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            color: var(--text-dark);
            background: white;
            transition: all 0.2s;
        }
        
        .form-input:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .form-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
        }

        /* ========== 多级菜单样式 ========== */
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
            padding: 12px 16px;
            background: var(--bg-panel);
            border-radius: 8px;
            font-size: 14px;
        }
        
        .breadcrumb-item {
            color: var(--text-light);
            cursor: pointer;
            transition: color 0.2s;
        }
        
        .breadcrumb-item:hover {
            color: var(--primary-color);
        }
        
        .breadcrumb-item.active {
            color: var(--text-dark);
            font-weight: 500;
        }
        
        .breadcrumb-separator {
            color: var(--text-light);
            margin: 0 4px;
        }
        
        .menu-levels {
            display: grid;
            gap: 16px;
        }
        
        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
        }
        
        .menu-item {
            padding: 20px;
            background: var(--bg-panel);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 16px;
            font-weight: 500;
            color: var(--text-dark);
        }
        
        .menu-item:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        /* ========== 搜索容器样式 ========== */
        .search-container {
            margin-bottom: 20px;
        }
        
        .search-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            background: white;
            transition: all 0.2s;
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }

        /* ========== 提示词卡片网格样式 ========== */
        .prompt-card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 16px;
        }
        
        .prompt-card {
            background: var(--bg-panel);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            transition: all 0.2s;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .prompt-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
            transform: translateY(-4px);
        }
        
        .prompt-card.added {
            border-color: #10b981;
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        }
        
        .prompt-card.added::after {
            content: '✓';
            position: absolute;
            top: 12px;
            right: 12px;
            width: 24px;
            height: 24px;
            background: #10b981;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .prompt-card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0 0 8px 0;
            line-height: 1.4;
        }
        
        .prompt-card-description {
            font-size: 14px;
            color: var(--text-light);
            line-height: 1.5;
            margin: 0 0 16px 0;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .prompt-card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: var(--text-light);
        }
        
        .prompt-card-author {
            font-weight: 500;
        }
        
        .prompt-card-stats {
            display: flex;
            gap: 12px;
        }

        /* ========== 级联选择器样式 ========== */
        .cascader-selector {
            margin-bottom: 20px;
        }
        
        .selection-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .sort-info {
            font-size: 14px;
            color: var(--text-light);
        }

        /* ========== 章节/便签选择列表样式 ========== */
        .selection-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: white;
        }
        
        .selection-item {
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .selection-item:last-child {
            border-bottom: none;
        }
        
        .selection-item:hover {
            background: var(--bg-hover);
        }
        
        .selection-item.selected {
            background: var(--shadow-color);
            color: var(--primary-color);
            font-weight: 500;
        }

        /* ========== 统一按钮样式系统 ========== */
        .btn-primary {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-primary:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .btn-secondary {
            background: var(--bg-panel);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-secondary:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .btn-cancel {
            background: transparent;
            color: var(--text-light);
            border: 1px solid var(--border-color);
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-cancel:hover {
            background: var(--bg-hover);
            color: var(--text-dark);
        }
        
        .btn-prompt-detail {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-prompt-detail:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
        }
        
        .btn-follow-up {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-follow-up:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        /* ========== 工具栏按钮间距 ========== */
        .toolbar .btn-group button {
            margin-right: 8px;
        }
        
        .toolbar .btn-group button:last-child {
            margin-right: 0;
        }
    </style>
</head>
<body>
    <!-- 鼠标感应区域 -->
    <div class="sidebar-trigger" id="sidebarTrigger"></div>

    <!-- 左侧导航栏包装器 -->
    <div class="sidebar-wrapper collapsed" id="sidebarWrapper">
        <!-- 左侧导航栏 -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-content">
                <a href="#" class="user-avatar">
                    <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="User Avatar">
                </a>
                <div class="nav-group">
                    <div class="nav-item" title="首页" onclick="window.location.href='首页.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                        </svg>
                        <span class="nav-item-text">首页</span>
                    </div>
				    <div class="nav-item" title="书架" onclick="window.location.href='书架.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/>
                        </svg>
                        <span class="nav-item-text">书架</span>
                    </div>
                    <div class="nav-item active" title="创意">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14.06,9.02l0.91,0.91L5.66,19.24L4.75,18.33L14.06,9.02 M17.66,3c-0.25,0-0.51,0.1-0.7,0.29l-1.83,1.83l3.75,3.75l1.83-1.83c0.39-0.39,0.39-1.02,0-1.41l-2.34-2.34C18.17,3.09,17.92,3,17.66,3L17.66,3z M12.06,6.19L3,15.25V19.24h3.99l9.06-9.06L12.06,6.19z"/>
                        </svg>
                        <span class="nav-item-text">创意</span>
                    </div>
                    <div class="nav-item" title="对话">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                        </svg>
                        <span class="nav-item-text">对话</span>
                    </div>
                    <div class="nav-item" title="模拟">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z M12 4c1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3 1.34 3 3 3z"/>
                        </svg>
                        <span class="nav-item-text">模拟</span>
                    </div>
                </div>
                <div class="sidebar-footer nav-group">
                    <div class="nav-item" title="教程">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/>
                        </svg>
                        <span class="nav-item-text">教程</span>
                    </div>
                    <div class="nav-item" title="邀请">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                        <span class="nav-item-text">邀请</span>
                    </div>
                    <div class="nav-item" title="夜间模式" onclick="toggleNightMode()">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9.37 5.51C9.19 6.15 9.1 6.82 9.1 7.5c0 4.08 3.32 7.4 7.4 7.4.68 0 1.35-.09 1.99-.27C17.45 17.19 14.93 19 12 19c-3.86 0-7-3.14-7-7 0-2.93 1.81-5.45 4.37-6.49z"/>
                        </svg>
                        <span class="nav-item-text">夜间</span>
                    </div>
                </div>
            </div>
        </nav>
    </div>

    <div class="app-container">
        <!-- ========== 左侧面板 (章节管理) ========== -->
        <aside id="left-panel-resizable" class="panel left-panel">
            <div class="panel-header management-header">
                <span>章节管理</span>
                <div>
                    <button class="btn-new" title="切换排序">⇅</button>
                    <button class="btn-new">+ 新建</button>
                </div>
            </div>
            <div class="panel-content">
                <ul class="item-list">
                    <li class="active">第1章...</li>
                    <li>第2章...</li>
                </ul>
            </div>
        </aside>
        
        <!-- ========== 中间工作区 ========== -->
        <div id="center-panel-resizable" class="center-panel">
            <div class="toolbar">
                <div class="btn-group">
                    <button>撤回</button>
                    <button>重做</button>
                    <button>复制</button>
                    <button>搜索</button>
                    <button>替换</button>
                    <button>智能排版</button>
                </div>
                <div class="theme-selector">
                    <button title="显示/隐藏输入输出框" id="toggle-io-btn">显示编辑区</button>
                    <button data-theme="default" title="默认主题">🔵</button>
                    <button data-theme="green-leaf" title="豆沙绿">🟢</button>
                    <button data-theme="sepia" title="羊皮纸">🟠</button>
                    <button data-theme="dark" title="暗夜模式">🌙</button>
                </div>
            </div>
            
            <div class="center-content-wrapper">
                <!-- 底层：本章内容 -->
                <div id="chapter-content-wrapper" class="text-area-wrapper">
                    <div class="panel-header content-header">本章内容</div>
                    <textarea readonly placeholder="这里显示当前所选章节的完整内容..."></textarea>
                </div>
                
                <!-- 悬浮层：输入输出 -->
                <div id="floating-io-container" class="floating-input-output-container is-hidden">
                    <div class="floating-io-split-view">
                        <div id="input-wrapper" class="text-area-wrapper">
                            <div class="panel-header">输入框</div>
                            <textarea placeholder="此处为按照(提示词1、提示词2、提示词n)等步骤每一步需要输入或粘贴进来的内容... (不要清除用户的内容, 除非用户自己清空)"></textarea>
                        </div>
                        <div id="output-wrapper" class="text-area-wrapper">
                            <div class="panel-header">生成框</div>
                            <textarea placeholder="此处为按照(提示词1、提示词2、提示词n)等步骤每一步输出的内容... (不要清除用户的内容, 除非用户自己清空)" readonly></textarea>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="btn-follow-up" onclick="openFollowUpModal()">继续追问</button>
                <button class="btn-secondary" onclick="openPromptManager()">提示词管理</button>
                <button class="btn-secondary">历史记录</button>
            </div>
        </div>

        <!-- ========== 右侧面板 (控制面板) ========== -->
        <aside id="right-panel-resizable" class="panel right-panel">
            <div class="panel-header">控制面板</div>
            <div class="panel-content">
                <!-- 便签管理区域 -->
                <div class="notes-section">
                    <div class="notes-header">
                        <h3>便签管理</h3>
                        <button class="add-note-btn">+ 新建</button>
                    </div>
                    <ul class="item-list">
                        <li>ABB</li>
                        <li>围城...</li>
                    </ul>
                </div>

                <!-- 全流程提示词 -->
                <h3 class="content-section-header">全流程提示词</h3>
                <div class="process-title">具体流程名称：玄幻小说生成</div>
                <div class="process-steps">
                    <div class="step-card active">
                        <h4>①【流程1: 莱莉大纲生成】</h4>
                        <p class="instructions">请确认按照流程1的使用步骤操作后，点确认键</p>
                        <button class="step-button" onclick="confirmStep(1)">确认生成</button>
                    </div>
                    <div class="step-card">
                        <h4>②【流程2: 莱莉细纲生成】</h4>
                        <p class="instructions">(流程2使用说明: ...XXXXX...)</p>
                        <button class="step-button" onclick="confirmStep(2)" disabled>确认生成</button>
                    </div>
                </div>
                <p class="final-note">(以下以此类推, 分为几步就出现几个)</p>

                <!-- 关联附件 -->
                <h3 class="content-section-header">关联附件</h3>
                <div class="control-group">
                    <label for="char-card">角色卡 (可输入、可选择)</label>
                    <input type="text" id="char-card" placeholder="输入或选择角色...">
                </div>
                <div class="control-group">
                    <label for="knowledge-card">知识卡 (可输入、可选择)</label>
                    <input type="text" id="knowledge-card" placeholder="输入或选择知识...">
                </div>
                <div class="control-group">
                    <label for="chapter-select">章节 (可选择)</label>
                    <select id="chapter-select">
                        <option>选择关联章节...</option>
                    </select>
                </div>
                <div class="control-group">
                    <label for="memo-select">便签 (可选择)</label>
                    <select id="memo-select">
                        <option>选择关联便签...</option>
                    </select>
                </div>
            </div>
        </aside>
    </div>

    <!-- ========== 继续追问弹窗 ========== -->
    <div id="follow-up-modal" class="modal">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3 class="modal-title">继续追问</h3>
                <button class="modal-close" onclick="closeFollowUpModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">请输入您的追问内容：</label>
                    <textarea class="form-textarea" id="follow-up-input" placeholder="请描述您想要继续追问的内容..." rows="4"></textarea>
                </div>
                <div class="form-actions">
                    <button class="btn-cancel" onclick="closeFollowUpModal()">取消</button>
                    <button class="btn-primary" onclick="submitFollowUp()">发送追问</button>
                </div>
            </div>
        </div>
    </div>

    <!-- ========== 提示词管理弹窗 ========== -->
    <div id="prompt-manager-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">提示词管理</h3>
                <button class="modal-close" onclick="closePromptManager()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="modal-tabs">
                    <button class="modal-tab active" onclick="switchModalTab('my-prompts')">我的提示词</button>
                    <button class="modal-tab" onclick="switchModalTab('prompt-market')">提示词市场</button>
                    <button class="modal-tab" onclick="switchModalTab('create-prompt')">创建提示词</button>
                </div>
                
                <!-- 我的提示词 -->
                <div id="my-prompts-section" class="modal-section active">
                    <div class="prompt-list" id="my-prompts-list">
                        <!-- 动态生成我的提示词列表 -->
                    </div>
                </div>
                
                <!-- 提示词市场 -->
                <div id="prompt-market-section" class="modal-section">
                    <div class="breadcrumb" id="market-breadcrumb">
                        <span class="breadcrumb-item active" onclick="navigateToMarketLevel('type')">提示词市场</span>
                    </div>
                    
                    <div class="search-container">
                        <input type="text" class="search-input" id="market-search" placeholder="搜索提示词..." oninput="filterMarketPrompts()">
                    </div>
                    
                    <div id="market-content">
                        <!-- 动态生成市场内容 -->
                    </div>
                </div>
                
                <!-- 创建提示词 -->
                <div id="create-prompt-section" class="modal-section">
                    <form class="create-prompt-form" onsubmit="createCustomPrompt(event)">
                        <div class="form-group">
                            <label class="form-label">提示词标题</label>
                            <input type="text" class="form-input" id="prompt-title" placeholder="请输入提示词标题" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">提示词描述</label>
                            <textarea class="form-textarea" id="prompt-description" placeholder="请输入提示词描述" rows="3" required></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">提示词内容</label>
                            <textarea class="form-textarea" id="prompt-content" placeholder="请输入完整的提示词内容" rows="6" required></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn-cancel">重置</button>
                            <button type="submit" class="btn-primary">创建提示词</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- ========== 提示词详情弹窗 ========== -->
    <div id="prompt-detail-modal" class="prompt-detail-modal">
        <div class="prompt-detail-content">
            <div class="prompt-detail-header">
                <h3 class="prompt-detail-title" id="detail-title">提示词详情</h3>
                <button class="prompt-detail-close" onclick="closePromptDetail()">&times;</button>
            </div>
            <div class="prompt-detail-body">
                <div class="prompt-detail-section">
                    <span class="prompt-detail-label">描述</span>
                    <div class="prompt-detail-text" id="detail-description"></div>
                </div>
                <div class="prompt-detail-section">
                    <span class="prompt-detail-label">提示词内容</span>
                    <div class="prompt-detail-text" id="detail-introduction"></div>
                </div>
                <div class="prompt-detail-section">
                    <span class="prompt-detail-label">作者</span>
                    <div class="prompt-detail-text" id="detail-author"></div>
                </div>
            </div>
            <div class="form-actions">
                <button class="btn-cancel" onclick="closePromptDetail()">取消</button>
                <button class="btn-primary" onclick="confirmAddPrompt()">添加到我的提示词</button>
            </div>
        </div>
    </div>

    <!-- 引入 Split.js 库 -->
    <script src="https://unpkg.com/split.js/dist/split.min.js"></script>

    <script>
        // --- 换肤功能（保持和首页一致） ---
        const savedTheme = localStorage.getItem('theme') || 'default';
        document.documentElement.setAttribute('data-theme', savedTheme);

        // 夜间模式快捷切换
        function toggleNightMode() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'default' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        }

        // --- 导航栏自动隐藏/显示逻辑 (重写版) ---
        const sidebarWrapper = document.getElementById('sidebarWrapper');
        const sidebarTrigger = document.getElementById('sidebarTrigger');
        let sidebarTimer;
        
        // 鼠标进入感应区域
        sidebarTrigger.addEventListener('mouseenter', () => {
            clearTimeout(sidebarTimer);
            sidebarWrapper.classList.remove('collapsed');
        });
        
        // 鼠标离开感应区域
        sidebarTrigger.addEventListener('mouseleave', (e) => {
            // 检查鼠标是否移到了导航栏上
            const toElement = e.relatedTarget;
            if (toElement && !sidebarWrapper.contains(toElement)) {
                sidebarTimer = setTimeout(() => {
                    sidebarWrapper.classList.add('collapsed');
                }, 300);
            }
        });
        
        // 鼠标在导航栏区域内移动时保持显示
        sidebarWrapper.addEventListener('mouseenter', () => {
            clearTimeout(sidebarTimer);
        });
        
        // 鼠标离开整个导航栏区域
        sidebarWrapper.addEventListener('mouseleave', () => {
            sidebarTimer = setTimeout(() => {
                sidebarWrapper.classList.add('collapsed');
            }, 300);
        });

        // 初始化水平三栏拖动 (调整为1:7:2比例，与专业版一致)
        Split(['#left-panel-resizable', '#center-panel-resizable', '#right-panel-resizable'], {
            sizes: [10, 70, 20], // 1:7:2 比例
            minSize: [150, 400, 280],
            gutterSize: 12,
            cursor: 'col-resize'
        });
        
        // 悬浮层内部的垂直两栏
        Split(['#input-wrapper', '#output-wrapper'], {
            direction: 'vertical',
            sizes: [50, 50],
            minSize: 100,
            gutterSize: 12,
            cursor: 'row-resize',
            elementStyle: (dimension, size, gutterSize) => ({
                'flex-basis': `calc(${size}% - ${gutterSize}px)`,
            }),
            gutterStyle: (dimension, gutterSize) => ({
                'flex-basis': `${gutterSize}px`,
            }),
        });

        // --- 交互功能 ---
        const floatingIOContainer = document.getElementById('floating-io-container');
        const toggleIOBtn = document.getElementById('toggle-io-btn');

        // 切换输入/输出悬浮层的显示和隐藏
        toggleIOBtn.addEventListener('click', () => {
            floatingIOContainer.classList.toggle('is-hidden');
            // 更新按钮文字
            toggleIOBtn.textContent = floatingIOContainer.classList.contains('is-hidden') ? '显示编辑区' : '隐藏编辑区';
        });

        // 皮肤切换功能
        const themeButtons = document.querySelectorAll('.theme-selector button[data-theme]');
        const body = document.body;

        const savedWritingTheme = localStorage.getItem('writingTheme') || 'default';
        if (savedWritingTheme !== 'default') {
            body.dataset.theme = savedWritingTheme;
        }

        themeButtons.forEach(button => {
            button.addEventListener('click', () => {
                const theme = button.dataset.theme;
                if (theme === 'default') {
                    delete body.dataset.theme;
                    localStorage.removeItem('writingTheme');
                } else {
                    body.dataset.theme = theme;
                    localStorage.setItem('writingTheme', theme);
                }
            });
        });

        // --- 章节管理排序功能 ---
        const sortBtn = document.querySelector('button[title="切换排序"]');
        const chapterList = document.querySelector('.item-list');
        let sortAscending = true;

        sortBtn.addEventListener('click', () => {
            const items = Array.from(chapterList.children);
            items.sort((a, b) => {
                const aText = a.textContent;
                const bText = b.textContent;
                return sortAscending ? 
                    aText.localeCompare(bText, 'zh-CN') : 
                    bText.localeCompare(aText, 'zh-CN');
            });
            
            // 清空并重新添加排序后的元素
            chapterList.innerHTML = '';
            items.forEach(item => chapterList.appendChild(item));
            
            sortAscending = !sortAscending;
        });

        // --- 章节列表交互 ---
        const chapterItems = document.querySelectorAll('.left-panel .item-list li');
        chapterItems.forEach(item => {
            item.addEventListener('click', () => {
                // 移除所有active类
                chapterItems.forEach(i => i.classList.remove('active'));
                // 添加active类到当前项
                item.classList.add('active');
            });
        });

        // --- 便签列表交互 ---
        const noteItems = document.querySelectorAll('.notes-section .item-list li');
        noteItems.forEach(item => {
            item.addEventListener('click', () => {
                // 这里可以添加选中便签的逻辑
                console.log('选中便签:', item.textContent);
            });
        });

        // ========== 通知系统 ==========
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            
            // 创建图标
            const icon = document.createElement('div');
            icon.className = `notification-icon ${type}`;
            
            let iconSVG = '';
            switch(type) {
                case 'success':
                    iconSVG = '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg>';
                    break;
                case 'error':
                    iconSVG = '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>';
                    break;
                case 'info':
                default:
                    iconSVG = '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/></svg>';
                    break;
            }
            
            icon.innerHTML = iconSVG;
            
            // 创建消息文本
            const messageEl = document.createElement('div');
            messageEl.className = 'notification-message';
            messageEl.textContent = message;
            
            // 组装通知
            notification.appendChild(icon);
            notification.appendChild(messageEl);
            
            // 添加到页面
            document.body.appendChild(notification);
            
            // 显示动画
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            // 自动隐藏
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // ========== 提示词管理系统 ==========
        let currentMarketType = '';
        let currentMarketLength = '';
        let currentMarketFunction = '';
        let myPrompts = [];
        let pendingPrompt = null;
        const userName = '用户';

        // 模拟提示词市场数据
        const marketData = {
            writing: {
                长篇: {
                    全局写作: [
                        {
                            id: 'w1',
                            title: '小说全局写作助手',
                            description: '帮助您构建完整的小说框架和情节发展',
                            introduction: '这是一个专业的小说写作提示词，能够帮助您从构思到完成整部作品...',
                            author: '写作专家',
                            likes: 1234,
                            uses: 5678
                        },
                        {
                            id: 'w2',
                            title: '长篇小说结构规划',
                            description: '专业的长篇小说结构设计和章节规划',
                            introduction: '通过科学的结构设计，帮助您创作出引人入胜的长篇小说...',
                            author: '结构大师',
                            likes: 987,
                            uses: 3456
                        }
                    ],
                    扩写: [
                        {
                            id: 'w3',
                            title: '情节扩写专家',
                            description: '将简单的情节扩展为丰富的故事内容',
                            introduction: '专业的情节扩写技巧，让您的故事更加生动有趣...',
                            author: '扩写高手',
                            likes: 756,
                            uses: 2341
                        }
                    ],
                    润色: [
                        {
                            id: 'w4',
                            title: '文字润色大师',
                            description: '提升文字表达的优美度和可读性',
                            introduction: '专业的文字润色服务，让您的作品更加精彩...',
                            author: '润色专家',
                            likes: 654,
                            uses: 1987
                        }
                    ],
                    续写: [
                        {
                            id: 'w5',
                            title: '智能续写助手',
                            description: '根据已有内容智能续写后续情节',
                            introduction: 'AI驱动的续写技术，保持故事连贯性...',
                            author: 'AI助手',
                            likes: 543,
                            uses: 1654
                        }
                    ],
                    拆书: [
                        {
                            id: 'w6',
                            title: '章节拆分专家',
                            description: '合理拆分长篇内容为章节结构',
                            introduction: '科学的章节划分方法，提升阅读体验...',
                            author: '结构师',
                            likes: 432,
                            uses: 1321
                        }
                    ],
                    大纲: [
                        {
                            id: 'w7',
                            title: '故事大纲生成器',
                            description: '快速生成完整的故事大纲框架',
                            introduction: '专业的大纲设计工具，让您的创作更有条理...',
                            author: '大纲专家',
                            likes: 876,
                            uses: 2987
                        }
                    ],
                    人设: [
                        {
                            id: 'w8',
                            title: '角色人设创建器',
                            description: '创建丰富立体的角色人物设定',
                            introduction: '专业的角色设计工具，打造令人印象深刻的角色...',
                            author: '人设大师',
                            likes: 765,
                            uses: 2654
                        }
                    ],
                    世界观: [
                        {
                            id: 'w9',
                            title: '世界观构建师',
                            description: '构建完整的故事世界观设定',
                            introduction: '专业的世界观设计，为您的故事提供坚实基础...',
                            author: '世界观专家',
                            likes: 654,
                            uses: 2321
                        }
                    ]
                }
            },
            brainstorm: {
                长篇: {
                    全局写作: [
                        {
                            id: 'b1',
                            title: '创意头脑风暴',
                            description: '激发无限创意灵感的头脑风暴工具',
                            introduction: '通过系统性的思维训练，激发您的创作潜能...',
                            author: '创意导师',
                            likes: 543,
                            uses: 1876
                        }
                    ]
                }
            }
        };

        // 菜单配置
        const menuConfig = {
            writing: {
                lengths: ['长篇'],
                functions: ['全局写作', '扩写', '润色', '续写', '拆书', '大纲', '人设', '世界观']
            },
            brainstorm: {
                lengths: ['长篇'],
                functions: ['全局写作']
            }
        };

        // ========== 提示词管理弹窗功能 ==========
        function openPromptManager() {
            const modal = document.getElementById('prompt-manager-modal');
            modal.classList.add('show');
            
            // 默认显示提示词市场的类型选择
            switchModalTab('prompt-market');
            navigateToMarketLevel('type');
            
            // 加载我的提示词
            loadMyPrompts();
        }

        function closePromptManager() {
            const modal = document.getElementById('prompt-manager-modal');
            modal.classList.remove('show');
        }

        function switchModalTab(tabName) {
            // 更新标签状态
            document.querySelectorAll('.modal-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 更新内容区域
            document.querySelectorAll('.modal-section').forEach(section => {
                section.classList.remove('active');
            });
            document.getElementById(tabName + '-section').classList.add('active');
        }

        function navigateToMarketLevel(level, type = '', length = '', func = '') {
            const breadcrumb = document.getElementById('market-breadcrumb');
            const content = document.getElementById('market-content');
            
            // 更新当前状态
            if (level === 'type') {
                currentMarketType = '';
                currentMarketLength = '';
                currentMarketFunction = '';
            } else if (level === 'length') {
                currentMarketType = type;
                currentMarketLength = '';
                currentMarketFunction = '';
            } else if (level === 'function') {
                currentMarketType = type;
                currentMarketLength = length;
                currentMarketFunction = '';
            } else if (level === 'prompts') {
                currentMarketType = type;
                currentMarketLength = length;
                currentMarketFunction = func;
            }
            
            // 更新面包屑
            updateBreadcrumb();
            
            // 渲染对应内容
            if (level === 'type') {
                renderTypeMenu();
            } else if (level === 'length') {
                renderLengthMenu();
            } else if (level === 'function') {
                renderFunctionMenu();
            } else if (level === 'prompts') {
                renderPromptsList();
            }
        }

        function updateBreadcrumb() {
            const breadcrumb = document.getElementById('market-breadcrumb');
            let html = '<span class="breadcrumb-item" onclick="navigateToMarketLevel(\'type\')">提示词市场</span>';
            
            if (currentMarketType) {
                html += '<span class="breadcrumb-separator">></span>';
                html += `<span class="breadcrumb-item" onclick="navigateToMarketLevel('length', '${currentMarketType}')">${currentMarketType === 'writing' ? '写作' : '头脑风暴'}</span>`;
            }
            
            if (currentMarketLength) {
                html += '<span class="breadcrumb-separator">></span>';
                html += `<span class="breadcrumb-item" onclick="navigateToMarketLevel('function', '${currentMarketType}', '${currentMarketLength}')">${currentMarketLength}</span>`;
            }
            
            if (currentMarketFunction) {
                html += '<span class="breadcrumb-separator">></span>';
                html += `<span class="breadcrumb-item active">${currentMarketFunction}</span>`;
            }
            
            breadcrumb.innerHTML = html;
        }

        function renderTypeMenu() {
            const content = document.getElementById('market-content');
            content.innerHTML = `
                <div class="menu-grid">
                    <div class="menu-item" onclick="navigateToMarketLevel('length', 'writing')">
                        <h3>写作</h3>
                        <p>专业的写作辅助提示词</p>
                    </div>
                    <div class="menu-item" onclick="navigateToMarketLevel('length', 'brainstorm')">
                        <h3>头脑风暴</h3>
                        <p>激发创意灵感的思维工具</p>
                    </div>
                </div>
            `;
        }

        function renderLengthMenu() {
            const content = document.getElementById('market-content');
            const lengths = menuConfig[currentMarketType]?.lengths || [];
            
            let html = '<div class="menu-grid">';
            lengths.forEach(length => {
                html += `
                    <div class="menu-item" onclick="navigateToMarketLevel('function', '${currentMarketType}', '${length}')">
                        <h3>${length}</h3>
                    </div>
                `;
            });
            html += '</div>';
            
            content.innerHTML = html;
        }

        function renderFunctionMenu() {
            const content = document.getElementById('market-content');
            const functions = menuConfig[currentMarketType]?.functions || [];
            
            let html = '<div class="menu-grid">';
            functions.forEach(func => {
                html += `
                    <div class="menu-item" onclick="navigateToMarketLevel('prompts', '${currentMarketType}', '${currentMarketLength}', '${func}')">
                        <h3>${func}</h3>
                    </div>
                `;
            });
            html += '</div>';
            
            content.innerHTML = html;
        }

        function renderPromptsList() {
            const content = document.getElementById('market-content');
            const prompts = marketData[currentMarketType]?.[currentMarketLength]?.[currentMarketFunction] || [];
            
            let html = '<div class="prompt-card-grid">';
            prompts.forEach(prompt => {
                const isAdded = myPrompts.some(p => p.id === prompt.id);
                html += `
                    <div class="prompt-card ${isAdded ? 'added' : ''}" onclick="showPromptDetail('${prompt.id}')">
                        <h4 class="prompt-card-title">${prompt.title}</h4>
                        <p class="prompt-card-description">${prompt.description}</p>
                        <div class="prompt-card-footer">
                            <span class="prompt-card-author">by ${prompt.author}</span>
                            <div class="prompt-card-stats">
                                <span>❤️ ${prompt.likes}</span>
                                <span>📝 ${prompt.uses}</span>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            content.innerHTML = html;
        }

        function filterMarketPrompts() {
            const searchTerm = document.getElementById('market-search').value.toLowerCase();
            const cards = document.querySelectorAll('.prompt-card');
            
            cards.forEach(card => {
                const title = card.querySelector('.prompt-card-title').textContent.toLowerCase();
                const author = card.querySelector('.prompt-card-author').textContent.toLowerCase();
                
                if (title.includes(searchTerm) || author.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function showPromptDetail(promptId) {
            // 查找提示词数据
            let prompt = null;
            for (const type in marketData) {
                for (const length in marketData[type]) {
                    for (const func in marketData[type][length]) {
                        const found = marketData[type][length][func].find(p => p.id === promptId);
                        if (found) {
                            prompt = found;
                            break;
                        }
                    }
                    if (prompt) break;
                }
                if (prompt) break;
            }
            
            if (!prompt) return;
            
            // 设置待添加的提示词
            pendingPrompt = prompt;
            
            // 显示详情弹窗
            document.getElementById('detail-title').textContent = prompt.title;
            document.getElementById('detail-description').textContent = prompt.description;
            document.getElementById('detail-introduction').textContent = prompt.introduction;
            document.getElementById('detail-author').textContent = prompt.author;
            
            const modal = document.getElementById('prompt-detail-modal');
            modal.classList.add('show');
        }

        function closePromptDetail() {
            const modal = document.getElementById('prompt-detail-modal');
            modal.classList.remove('show');
            pendingPrompt = null;
        }

        function confirmAddPrompt() {
            if (!pendingPrompt) return;
            
            // 检查是否已存在
            if (myPrompts.some(p => p.id === pendingPrompt.id)) {
                showNotification('该提示词已在您的列表中', 'info');
                closePromptDetail();
                return;
            }
            
            // 添加到我的提示词
            myPrompts.push({ ...pendingPrompt });
            
            // 保存到本地存储
            localStorage.setItem('myPrompts', JSON.stringify(myPrompts));
            
            // 重新加载我的提示词列表
            loadMyPrompts();
            
            // 标记卡片为已添加
            const cards = document.querySelectorAll('.prompt-card');
            cards.forEach(card => {
                if (card.querySelector('.prompt-card-title').textContent === pendingPrompt.title) {
                    card.classList.add('added');
                }
            });
            
            // 关闭详情弹窗
            closePromptDetail();
            
            // 显示成功通知
            showNotification('提示词添加成功！', 'success');
        }

        function loadMyPrompts() {
            // 从本地存储加载
            const saved = localStorage.getItem('myPrompts');
            if (saved) {
                myPrompts = JSON.parse(saved);
            }
            
            // 渲染我的提示词列表
            const container = document.getElementById('my-prompts-list');
            if (myPrompts.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: var(--text-light); padding: 40px;">暂无提示词，去市场添加一些吧！</p>';
                return;
            }
            
            let html = '';
            myPrompts.forEach((prompt, index) => {
                html += `
                    <div class="prompt-item">
                        <div class="prompt-info">
                            <h4 class="prompt-title">${prompt.title}</h4>
                            <p class="prompt-description">${prompt.description}</p>
                        </div>
                        <div class="prompt-actions">
                            <button class="btn-prompt-detail" onclick="usePrompt(${index})">使用</button>
                            <button class="btn-cancel" onclick="removePrompt(${index})">删除</button>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        function usePrompt(index) {
            const prompt = myPrompts[index];
            if (!prompt) return;
            
            // 将提示词内容填入输入框
            const inputTextarea = document.querySelector('#input-wrapper textarea');
            if (inputTextarea) {
                inputTextarea.value = prompt.introduction;
            }
            
            // 关闭弹窗
            closePromptManager();
            
            // 显示输入输出区域
            const floatingIOContainer = document.getElementById('floating-io-container');
            const toggleIOBtn = document.getElementById('toggle-io-btn');
            if (floatingIOContainer.classList.contains('is-hidden')) {
                floatingIOContainer.classList.remove('is-hidden');
                toggleIOBtn.textContent = '隐藏编辑区';
            }
            
            showNotification('提示词已应用到输入框', 'success');
        }

        function removePrompt(index) {
            if (confirm('确定要删除这个提示词吗？')) {
                myPrompts.splice(index, 1);
                localStorage.setItem('myPrompts', JSON.stringify(myPrompts));
                loadMyPrompts();
                showNotification('提示词已删除', 'info');
            }
        }

        function createCustomPrompt(event) {
            event.preventDefault();
            
            const title = document.getElementById('prompt-title').value.trim();
            const description = document.getElementById('prompt-description').value.trim();
            const content = document.getElementById('prompt-content').value.trim();
            
            if (!title || !description || !content) {
                showNotification('请填写完整信息', 'error');
                return;
            }
            
            const newPrompt = {
                id: 'custom_' + Date.now(),
                title: title,
                description: description,
                introduction: content,
                author: userName,
                likes: 0,
                uses: 0
            };
            
            myPrompts.push(newPrompt);
            localStorage.setItem('myPrompts', JSON.stringify(myPrompts));
            
            // 清空表单
            document.getElementById('prompt-title').value = '';
            document.getElementById('prompt-description').value = '';
            document.getElementById('prompt-content').value = '';
            
            // 切换到我的提示词标签
            switchModalTab('my-prompts');
            loadMyPrompts();
            
            showNotification('自定义提示词创建成功！', 'success');
        }

        // ========== 继续追问弹窗功能 ==========
        function openFollowUpModal() {
            const modal = document.getElementById('follow-up-modal');
            modal.classList.add('show');
        }

        function closeFollowUpModal() {
            const modal = document.getElementById('follow-up-modal');
            modal.classList.remove('show');
        }

        function submitFollowUp() {
            const input = document.getElementById('follow-up-input');
            const content = input.value.trim();
            
            if (!content) {
                showNotification('请输入追问内容', 'error');
                return;
            }
            
            // 这里可以添加实际的追问处理逻辑
            showNotification('追问已发送', 'success');
            
            // 清空输入框并关闭弹窗
            input.value = '';
            closeFollowUpModal();
        }

        // ========== 流程步骤确认功能 ==========
        function confirmStep(stepNumber) {
            showNotification(`流程${stepNumber}已确认`, 'success');
            
            // 激活下一步
            const currentStep = document.querySelector(`.step-card:nth-child(${stepNumber})`);
            const nextStep = document.querySelector(`.step-card:nth-child(${stepNumber + 1})`);
            
            if (currentStep) {
                currentStep.classList.remove('active');
                const button = currentStep.querySelector('.step-button');
                if (button) {
                    button.textContent = '已完成';
                    button.disabled = true;
                }
            }
            
            if (nextStep) {
                nextStep.classList.add('active');
                const button = nextStep.querySelector('.step-button');
                if (button) {
                    button.disabled = false;
                }
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadMyPrompts();
        });
    </script>

</body>
</html>
