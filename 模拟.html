<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模拟 - 笔尖传奇写作</title>
    <style>
        /* --- 1. 全局与动态配色系统 (与其他页面统一) --- */
        :root {
            /* 默认皮肤 (护眼蓝灰) */
            --bg-main: #F0F3F7;
            --bg-panel: #FAFBFC;
            --bg-panel-secondary: #F3F5F8;
            --bg-content: #F7F9FB;
            --text-dark: #2D3748;
            --text-light: #718096;
            --text-on-primary: #FFFFFF;
            --border-color: #E8EBEF;
            --gutter-color: transparent;
            --shadow-color: rgba(93, 156, 236, 0.08);
            --shadow-color-light: rgba(0, 0, 0, 0.04);
            --shadow-color-heavy: rgba(0, 0, 0, 0.12);
            /* 主题色 */
            --primary-color: #5D9CEC;
            --primary-color-hover: #4A89E2;
            --secondary-color: #8696A7;
            --secondary-color-hover: #7A8A9B;
            --accent-color: #48BB78;
            --accent-color-hover: #3AA967;
            --warning-color: #FF6B6B;
            --warning-color-hover: #FF5252;
            /* 内容区专用色 */
            --content-header-bg: #E8F2FF;
            --content-header-color: #2B5797;
            /* 布局尺寸 */
            --font-size-base: 16px;
            --font-size-sm: 14px;
            --font-size-lg: 18px;
            --header-height: 52px;
            --sidebar-width: 80px;
            --sidebar-collapsed-width: 4px;
            /* 模拟界面专用 */
            --character-bg-user: #E8F4FF;
            --character-bg-ai: #F0F8FF;
            --character-bg-system: #FFF8E8;
            --message-border-radius: 12px;
            --input-height: 120px;
        }

        [data-theme="green-leaf"] {
            --bg-main: #EFF3EF;
            --bg-panel: #F8FAF8;
            --bg-panel-secondary: #F0F3F0;
            --bg-content: #F5F8F5;
            --text-dark: #3E4A3E;
            --text-light: #6B7C6B;
            --border-color: #E0E6E0;
            --primary-color: #6A9C89;
            --primary-color-hover: #5A8C79;
            --secondary-color: #8A9B94;
            --secondary-color-hover: #7A8B84;
            --accent-color: #E99469;
            --accent-color-hover: #D98459;
            --shadow-color: rgba(106, 156, 137, 0.08);
            --content-header-bg: #E5F2E9;
            --content-header-color: #3A6B4F;
            --character-bg-user: #E5F2E9;
            --character-bg-ai: #F0F8F0;
            --character-bg-system: #FFF8E8;
        }

        [data-theme="sepia"] {
            --bg-main: #FBF0D9;
            --bg-panel: #FAF4E8;
            --bg-panel-secondary: #F6ECDA;
            --bg-content: #FAF4E8;
            --text-dark: #5C4B33;
            --text-light: #8B7355;
            --border-color: #EAE0C8;
            --primary-color: #A67B5B;
            --primary-color-hover: #966B4B;
            --secondary-color: #B0A08D;
            --secondary-color-hover: #A0907D;
            --accent-color: #5D9CEC;
            --accent-color-hover: #4A89E2;
            --shadow-color: rgba(166, 123, 91, 0.1);
            --content-header-bg: #F4E6D4;
            --content-header-color: #7A5A3A;
            --character-bg-user: #F4E6D4;
            --character-bg-ai: #F8F0E0;
            --character-bg-system: #FFF8E8;
        }

        [data-theme="dark"] {
            --bg-main: #1A202C;
            --bg-panel: #2D3748;
            --bg-panel-secondary: #252E3E;
            --bg-content: #323B4C;
            --text-dark: #E2E8F0;
            --text-light: #A0AEC0;
            --border-color: #4A5568;
            --primary-color: #4A5568;
            --primary-color-hover: #718096;
            --secondary-color: #3B475C;
            --secondary-color-hover: #4A5568;
            --accent-color: #48BB78;
            --accent-color-hover: #3AA967;
            --shadow-color: rgba(0, 0, 0, 0.2);
            --content-header-bg: #3A4558;
            --content-header-color: #CBD5E0;
            --character-bg-user: #3A4558;
            --character-bg-ai: #404858;
            --character-bg-system: #4A4558;
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", "Helvetica Neue", sans-serif;
            background-color: var(--bg-main);
            margin: 0;
            padding: 0;
            color: var(--text-dark);
            font-size: var(--font-size-base);
            line-height: 1.7;
            -webkit-font-smoothing: antialiased;
            display: flex;
            height: 100vh;
            overflow: hidden;
            transition: background-color 0.3s, color 0.3s;
        }

        /* --- 左侧导航栏 (优化版) --- */
        .sidebar-wrapper {
            position: relative;
            width: var(--sidebar-width);
            flex-shrink: 0;
            transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar-wrapper.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar {
            position: absolute;
            left: 0;
            top: 0;
            width: var(--sidebar-width);
            height: 100%;
            background: var(--bg-panel);
            box-shadow: 2px 0 8px var(--shadow-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 0;
            z-index: 100;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .sidebar-wrapper.collapsed .sidebar {
            transform: translateX(calc(-1 * var(--sidebar-width) + var(--sidebar-collapsed-width)));
        }

        .sidebar-trigger {
            position: fixed;
            left: 0;
            top: 0;
            width: 20px;
            height: 100%;
            z-index: 101;
        }

        .sidebar-wrapper:not(.collapsed) .sidebar-trigger {
            width: calc(var(--sidebar-width) + 20px);
        }

        .sidebar-content {
            opacity: 1;
            transition: opacity 0.2s;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100%;
        }
        .sidebar-wrapper.collapsed .sidebar-content {
            opacity: 0;
            pointer-events: none;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px var(--shadow-color);
            transition: transform 0.2s;
            flex-shrink: 0;
        }
        .user-avatar:hover {
            transform: scale(1.05);
        }
        .user-avatar img { 
            width: 100%; 
            height: 100%; 
            object-fit: cover; 
        }

        .nav-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            width: 100%;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            color: var(--text-light);
            transition: all 0.2s ease;
            padding: 8px;
            border-radius: 10px;
            width: 60px;
        }
        .nav-item:hover {
            color: var(--primary-color);
            background-color: var(--bg-panel-secondary);
        }
        .nav-item.active {
            color: var(--primary-color);
            background-color: var(--bg-panel-secondary);
        }
        .nav-item-icon {
            font-size: 20px;
            width: 24px;
            height: 24px;
            fill: currentColor;
        }
        .nav-item-text {
            font-size: 11px;
            font-weight: 500;
        }
        .sidebar-footer {
            margin-top: auto;
        }

        /* --- 主内容区 --- */
        .main-content {
            flex-grow: 1;
            display: flex;
            height: 100vh;
            overflow: hidden;
        }

        /* --- 模拟界面布局 --- */
        .simulation-container {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            background: var(--bg-main);
            position: relative;
            margin-right: 0; /* 移除右边距，消除留白 */
        }

        /* 顶部工具栏 */
        .simulation-header {
            height: var(--header-height);
            background: var(--bg-panel);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            flex-shrink: 0;
            box-shadow: 0 2px 8px var(--shadow-color-light);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .character-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .character-selector label {
            font-size: var(--font-size-sm);
            color: var(--text-light);
            font-weight: 500;
        }

        .btn-select-character {
            padding: 8px 16px;
            background: var(--bg-content);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-select-character:hover {
            background: var(--bg-panel-secondary);
            border-color: var(--primary-color);
        }

        .btn-create-character {
            padding: 8px 16px;
            background: var(--accent-color);
            color: var(--text-on-primary);
            border: none;
            border-radius: 8px;
            font-size: var(--font-size-sm);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-create-character:hover {
            background: var(--accent-color-hover);
            transform: translateY(-1px);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-right: 320px; /* 按钮组超大幅向左移动 */
        }

        .header-btn {
            padding: 8px 16px;
            background: var(--bg-content);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
        }

        .header-btn:hover {
            background: var(--bg-panel-secondary);
            border-color: var(--primary-color);
        }

        /* 聊天室样式消息区域 */
        .chat-container {
            flex-grow: 1;
            overflow-y: auto;
            padding: 30px 20px;
            scroll-behavior: smooth;
        }

        .chat-wrapper {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* 角色指示器 */
        .character-indicator {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
            padding: 12px 16px;
            background: var(--bg-panel);
            border-radius: 12px;
            box-shadow: 0 2px 8px var(--shadow-color);
        }

        .indicator-label {
            font-size: var(--font-size-sm);
            color: var(--text-light);
            font-weight: 500;
        }

        .active-characters {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .active-characters.empty {
            color: var(--text-light);
            font-size: var(--font-size-sm);
            font-style: italic;
        }

        .character-chip {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            background: var(--bg-content);
            border-radius: 20px;
            font-size: var(--font-size-sm);
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.2s;
        }

        .character-chip:hover {
            background: var(--bg-panel-secondary);
            border-color: var(--primary-color);
        }

        .character-chip-avatar {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary-color);
            color: var(--text-on-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: 600;
        }

        .character-chip-name {
            font-weight: 500;
            color: var(--text-dark);
        }

        .character-chip-model {
            font-size: 11px;
            color: var(--text-light);
            padding: 2px 6px;
            background: var(--bg-panel-secondary);
            border-radius: 10px;
        }

        /* 消息样式 - 聊天室风格 */
        .chat-message {
            display: flex;
            gap: 12px;
            animation: fadeInUp 0.3s ease-out;
            position: relative;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .chat-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: var(--font-size-sm);
            box-shadow: 0 2px 8px var(--shadow-color);
        }

        .chat-content {
            max-width: 70%;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .chat-header {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chat-name {
            font-weight: 600;
            font-size: var(--font-size-sm);
            color: var(--text-dark);
        }

        .chat-role {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            background: var(--bg-panel-secondary);
            color: var(--text-light);
        }

        .chat-time {
            font-size: 12px;
            color: var(--text-light);
            opacity: 0.7;
        }

        .chat-bubble {
            padding: 12px 16px;
            border-radius: var(--message-border-radius);
            box-shadow: 0 2px 8px var(--shadow-color-light);
            transition: all 0.2s;
            position: relative;
        }

        .chat-message.user .chat-bubble {
            background: var(--character-bg-user);
            margin-left: auto;
        }

        .chat-message.ai .chat-bubble {
            background: var(--character-bg-ai);
        }

        .chat-message.system .chat-bubble {
            background: var(--character-bg-system);
            text-align: center;
            font-style: italic;
            color: var(--text-light);
        }

        .chat-text {
            font-size: var(--font-size-base);
            line-height: 1.6;
            color: var(--text-dark);
        }
        
        /* 动作旁白样式 */
        .action-narration {
            color: var(--text-light);
            font-style: italic;
            font-size: 0.9em;
            opacity: 0.9;
            margin: 0 4px;
            background: var(--bg-panel-secondary);
            padding: 3px 8px;
            border-radius: 6px;
            border-left: 3px solid var(--primary-color);
            display: inline-block;
            line-height: 1.3;
            font-weight: 500;
            letter-spacing: 0.2px;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .action-narration:hover {
            background: var(--bg-panel-hover);
            opacity: 1;
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        }
        
        /* 暗色主题下的动作旁白样式增强 */
        [data-theme="dark"] .action-narration {
            color: #e0e0e0;
            background: rgba(255, 255, 255, 0.08);
            border-left-color: var(--primary-color);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }
        
        [data-theme="dark"] .action-narration:hover {
            color: #f0f0f0;
            background: rgba(255, 255, 255, 0.12);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
        }

        /* 系统消息样式 */
        .system-notification {
            text-align: center;
            padding: 8px 16px;
            margin: 10px auto;
            max-width: 600px;
            background: var(--bg-panel-secondary);
            border-radius: 20px;
            font-size: var(--font-size-sm);
            color: var(--text-light);
            animation: fadeIn 0.3s ease-out;
        }

        /* 输入区域 */
        .input-container {
            background: var(--bg-panel);
            border-top: 1px solid var(--border-color);
            padding: 20px;
            padding-right: 0; /* 向右延伸，无留白 */
            flex-shrink: 0;
            box-shadow: 0 -2px 8px var(--shadow-color-light);
        }

        .input-wrapper {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-box {
            flex-grow: 1;
            position: relative;
        }

        .input-textarea {
            width: 100%;
            min-height: 50px;
            max-height: var(--input-height);
            padding: 12px 50px 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            background: var(--bg-content);
            color: var(--text-dark);
            font-size: var(--font-size-base);
            line-height: 1.5;
            resize: none;
            transition: all 0.2s;
            font-family: inherit;
        }

        .input-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }

        .input-actions {
            position: absolute;
            right: 8px;
            bottom: 8px;
            display: flex;
            gap: 8px;
        }

        .input-action-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 6px;
            background: transparent;
            color: var(--text-light);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .input-action-btn:hover {
            background: var(--bg-panel);
            color: var(--primary-color);
        }

        .send-button {
            padding: 12px 24px;
            background: var(--primary-color);
            color: var(--text-on-primary);
            border: none;
            border-radius: 10px;
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .send-button:hover {
            background: var(--primary-color-hover);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px var(--shadow-color-heavy);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .auto-conversation-button {
            padding: 12px 24px;
            background: var(--primary-color);
            color: var(--text-on-primary);
            border: none;
            border-radius: 10px;
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s;
            margin-left: 8px;
        }
        
        .auto-conversation-button:hover {
            background: var(--primary-color-hover);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px var(--shadow-color-heavy);
        }
        
        .auto-conversation-button.disabled {
            opacity: 0.5;
            transform: none;
        }
        
        .auto-restart-button {
            padding: 12px 24px;
            background: var(--primary-color);
            color: var(--text-on-primary);
            border: none;
            border-radius: 10px;
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s;
            margin-left: 8px;
        }
        
        .auto-restart-button:hover {
            background: var(--primary-color-hover);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px var(--shadow-color-heavy);
        }
        
        .auto-restart-button.disabled {
            opacity: 0.5;
            transform: none;
            cursor: default;
        }
        
        .auto-restart-button.disabled:hover {
            background: var(--primary-color);
            transform: none;
            box-shadow: none;
            cursor: default;
        }

        /* 空状态 */
        .empty-state {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px;
            text-align: center;
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .empty-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 10px;
        }

        .empty-desc {
            font-size: var(--font-size-base);
            color: var(--text-light);
            max-width: 500px;
            line-height: 1.6;
        }

        /* 右侧面板 - 角色管理和场景设定 */
        .right-panel {
            width: 320px;
            background: var(--bg-panel);
            border-left: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            box-shadow: -2px 0 8px var(--shadow-color-light);
            overflow: hidden;
            position: fixed;
            right: 0;
            top: 0;
            height: 100vh;
            z-index: 1;
        }

        .panel-header {
            height: var(--header-height);
            padding: 0 20px;
            background: var(--primary-color);
            color: var(--text-on-primary);
            font-size: var(--font-size-lg);
            font-weight: 600;
            display: flex;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
        }

        .panel-content {
            flex-grow: 1;
            overflow-y: auto;
            padding: 20px;
        }

        /* 参与角色列表 */
        .participants-section {
            margin-bottom: 24px;
        }

        .section-title {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .add-participant-btn {
            padding: 4px 12px;
            background: var(--accent-color);
            color: var(--text-on-primary);
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
        }

        .add-participant-btn:hover {
            background: var(--accent-color-hover);
        }

        .participant-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .participant-item {
            padding: 12px;
            background: var(--bg-content);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .participant-item:hover {
            background: var(--bg-panel-secondary);
            border-color: var(--border-color);
        }

        .participant-item.active {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }

        .participant-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--primary-color);
            color: var(--text-on-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-sm);
            font-weight: 600;
        }

        .participant-info {
            flex-grow: 1;
        }

        .participant-name {
            font-size: var(--font-size-sm);
            font-weight: 500;
            color: var(--text-dark);
        }

        .participant-model {
            font-size: 12px;
            color: var(--text-light);
        }

        .participant-actions {
            display: flex;
            gap: 4px;
        }

        .participant-action {
            width: 24px;
            height: 24px;
            background: transparent;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.2s;
        }

        .participant-item:hover .participant-action {
            opacity: 1;
        }

        .participant-action:hover {
            color: var(--primary-color);
        }

        .participant-action.remove:hover {
            color: var(--warning-color);
        }

        /* 场景设定区域 */
        .scenario-editor {
            background: var(--bg-content);
            border-radius: 12px;
            padding: 16px;
            margin-top: 20px;
        }

        .editor-field {
            margin-bottom: 16px;
        }

        .field-label {
            font-size: var(--font-size-sm);
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 6px;
            display: block;
        }

        .field-textarea {
            width: 100%;
            min-height: 120px;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-panel);
            color: var(--text-dark);
            font-size: var(--font-size-sm);
            resize: vertical;
            transition: all 0.2s;
            font-family: inherit;
        }

        .field-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }

        .auto-generate-btn {
            padding: 8px 16px;
            background: var(--bg-panel);
            color: var(--primary-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
            margin-top: 8px;
            width: 100%;
            justify-content: center;
        }

        .auto-generate-btn:hover {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        /* 关联编辑器内容 */
        .editor-link-section {
            margin-top: 24px;
            padding: 20px;
            padding-top: 24px;
            border-top: 1px solid var(--border-color);
        }

        .editor-field {
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.4s ease-out forwards;
        }

        .editor-field:nth-child(2) { animation-delay: 0.1s; }
        .editor-field:nth-child(3) { animation-delay: 0.15s; }
        .editor-field:nth-child(4) { animation-delay: 0.2s; }
        .editor-field:nth-child(5) { animation-delay: 0.25s; }

        .field-label {
            display: block;
            font-size: var(--font-size-sm);
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 8px;
        }

        .link-select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-content);
            color: var(--text-dark);
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
        }

        .link-select:hover {
            border-color: var(--primary-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px var(--shadow-color);
        }

        .link-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }

        .editor-hint {
            font-size: 12px;
            color: var(--text-light);
            margin-top: 4px;
        }

        /* 角色市场弹窗 */
        .modal {
            display: none;
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0);
            z-index: 1000;
            justify-content: center;
            align-items: center;
            transition: background-color 0.3s ease-out;
        }

        .modal.show {
            background: rgba(0, 0, 0, 0.6);
        }

        .modal-content {
            background: var(--bg-panel);
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            max-width: 900px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            transform: scale(0.9) translateY(20px);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .modal.show .modal-content {
            transform: scale(1) translateY(0);
            opacity: 1;
        }

        .modal-header {
            margin-bottom: 24px;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 8px;
        }

        .modal-desc {
            font-size: var(--font-size-sm);
            color: var(--text-light);
        }

        /* 角色分类 */
        .character-categories {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .category-btn {
            padding: 6px 16px;
            background: var(--bg-content);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            font-size: var(--font-size-sm);
            color: var(--text-dark);
            cursor: pointer;
            transition: all 0.2s;
        }

        .category-btn:hover {
            background: var(--bg-panel-secondary);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .category-btn.active {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        /* 角色网格 */
        .character-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .character-card {
            background: var(--bg-content);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            opacity: 0;
            transform: translateY(20px);
            animation: cardFadeIn 0.5s ease-out forwards;
        }

        .character-card:nth-child(n) { animation-delay: calc(0.05s * var(--card-index)); }

        @keyframes cardFadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .character-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 8px 20px var(--shadow-color);
            transform: translateY(-4px) scale(1.02);
        }

        .character-card.selected {
            background: var(--bg-panel-secondary);
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }

        .character-avatar-large {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary-color);
            color: var(--text-on-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0 auto 12px;
        }

        .character-name {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 4px;
            text-align: center;
        }

        .character-desc {
            font-size: var(--font-size-sm);
            color: var(--text-light);
            line-height: 1.5;
            text-align: center;
            margin-bottom: 8px;
        }

        .character-tags {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .character-tag {
            padding: 2px 8px;
            background: var(--bg-panel-secondary);
            border-radius: 4px;
            font-size: 12px;
            color: var(--text-light);
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: var(--font-size-sm);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-cancel {
            background: var(--bg-content);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
        }

        .btn-cancel:hover {
            background: var(--bg-panel-secondary);
        }

        .btn-confirm {
            background: var(--primary-color);
            color: var(--text-on-primary);
        }

        .btn-confirm:hover {
            background: var(--primary-color-hover);
            transform: translateY(-1px);
        }

        /* 创建角色弹窗 - 继承对话页面样式 */
        .form-group {
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(10px);
            animation: formFadeIn 0.4s ease-out forwards;
        }

        .form-group:nth-child(1) { animation-delay: 0.1s; }
        .form-group:nth-child(2) { animation-delay: 0.15s; }
        .form-group:nth-child(3) { animation-delay: 0.2s; }
        .form-group:nth-child(4) { animation-delay: 0.25s; }

        @keyframes formFadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-label {
            display: block;
            font-size: var(--font-size-sm);
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 8px;
        }

        .form-input,
        .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-content);
            color: var(--text-dark);
            font-size: var(--font-size-sm);
            transition: all 0.2s;
            font-family: inherit;
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        .form-input:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }

        .form-hint {
            font-size: 12px;
            color: var(--text-light);
            margin-top: 4px;
        }

        /* 模型选择弹窗 */
        .model-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .model-card {
            background: var(--bg-content);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-align: center;
            opacity: 0;
            transform: translateY(20px);
            animation: cardFadeIn 0.5s ease-out forwards;
        }

        .model-card:nth-child(n) { animation-delay: calc(0.08s * var(--card-index)); }

        .model-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 8px 20px var(--shadow-color);
            transform: translateY(-4px) scale(1.02);
        }

        .model-card.selected {
            background: var(--bg-panel-secondary);
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }

        .model-icon {
            font-size: 2.5rem;
            margin-bottom: 12px;
            opacity: 0.8;
        }

        .model-name {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
        }

        .model-desc {
            font-size: var(--font-size-sm);
            color: var(--text-light);
            line-height: 1.4;
        }

        /* 历史记录弹窗样式 */
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--text-light);
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            background: var(--bg-panel-secondary);
            color: var(--text-dark);
        }

        .history-item {
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
            transition: background-color 0.2s ease;
        }

        .history-item:hover {
            background: var(--bg-panel-secondary);
        }

        .history-item:last-child {
            border-bottom: none;
        }

        .history-title {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
            font-size: 16px;
        }

        .history-content {
            color: var(--text-light);
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 8px;
            display: -webkit-box;
            line-clamp: 2;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .history-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: var(--text-light);
        }

        .history-actions {
            display: flex;
            gap: 8px;
        }

        .history-action-btn {
            padding: 4px 8px;
            border: 1px solid var(--border-color);
            background: var(--bg-content);
            color: var(--text-dark);
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .history-action-btn:hover {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        .btn-cancel {
            padding: 8px 16px;
            border: 1px solid var(--warning-color);
            background: transparent;
            color: var(--warning-color);
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .btn-cancel:hover {
            background: var(--warning-color);
            color: white;
        }

        /* 详情弹窗样式 */
        .detail-field {
            margin-bottom: 16px;
        }

        .field-input {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-content);
            color: var(--text-dark);
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .field-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .field-input[readonly] {
            background: var(--bg-panel-secondary);
            color: var(--text-light);
        }

        .btn-primary {
            padding: 8px 16px;
            border: none;
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
        }

        .btn-secondary {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            background: var(--bg-content);
            color: var(--text-dark);
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        /* 动态总结面板样式 - 悬浮窗样式 */
        .dynamic-summary-panel {
            position: fixed;
            right: 320px; /* 与右侧面板接触 */
            top: var(--header-height); /* 与顶部操作栏底部边缘接触 */
            width: 400px; /* 增加三分之一宽度 */
            height: calc(100vh - var(--header-height) - 100px); /* 顶部接触，底部保持原位置 */
            background: var(--bg-content);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: 0 8px 32px var(--shadow-color-heavy);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            user-select: none;
            backdrop-filter: blur(10px);
        }
        
        /* 移除悬浮窗相关样式 */
        .dynamic-summary-panel:hover {
            /* 移除悬浮效果 */
        }

        .dynamic-summary-panel.dragging {
            /* 移除拖拽效果 */
        }

        .dynamic-summary-panel.collapsed {
            /* 移除收起效果 */
        }

        .summary-panel-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 20px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-bottom: 1px solid var(--border-color);
            flex-shrink: 0;
        }

        .summary-panel-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            font-size: 14px;
        }

        .summary-toggle-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 6px;
            padding: 6px;
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .summary-toggle-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .summary-panel-content {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.6;
        }

        .summary-placeholder {
            text-align: center;
            padding: 40px 20px;
        }

        .placeholder-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.6;
        }

        .placeholder-text {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
            font-size: 16px;
        }

        .placeholder-desc {
            color: var(--text-light);
            font-size: 13px;
            line-height: 1.5;
        }

        .summary-item {
            margin-bottom: 16px;
            padding: 16px;
            background: var(--bg-panel);
            border-radius: 10px;
            border-left: 4px solid var(--primary-color);
            transition: all 0.2s ease;
        }

        .summary-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .summary-category {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 8px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .summary-text {
            color: var(--text-dark);
            line-height: 1.6;
        }

        .summary-timestamp {
            font-size: 12px;
            color: var(--text-light);
            margin-top: 16px;
            padding-top: 12px;
            border-top: 1px solid var(--border-color);
            text-align: center;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .dynamic-summary-panel {
                width: 300px;
            }
        }

        @media (max-width: 768px) {
            .dynamic-summary-panel {
                position: fixed;
                top: 0;
                right: 0;
                width: 100vw;
                height: 100vh;
                max-height: 100vh;
                border-radius: 0;
                transform: translateX(100%);
            }

            .dynamic-summary-panel.collapsed {
                transform: translateX(calc(100% - 50px));
            }

            .summary-panel-header {
                border-radius: 0;
            }
        }

        .btn-secondary:hover {
            background: var(--bg-panel-secondary);
            border-color: var(--primary-color);
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: transparent;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--secondary-color);
            border-radius: 4px;
            transition: background 0.2s;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        /* 自定义提醒弹窗样式 */
        .custom-alert {
            display: none;
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0);
            z-index: 20000;
            justify-content: center;
            align-items: center;
            transition: background-color 0.3s ease-out;
        }

        .custom-alert.show {
            background: rgba(0, 0, 0, 0.5);
        }

        .custom-alert-content {
            background: var(--bg-panel);
            padding: 24px 30px;
            border-radius: 12px;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            max-width: 400px;
            width: 90%;
            text-align: center;
            transform: scale(0.9) translateY(-20px);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .custom-alert.show .custom-alert-content {
            transform: scale(1) translateY(0);
            opacity: 1;
        }

        .custom-alert-icon {
            width: 48px;
            height: 48px;
            margin: 0 auto 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .custom-alert-icon.success {
            background: var(--accent-color);
            color: white;
        }

        .custom-alert-icon.error {
            background: var(--warning-color);
            color: white;
        }

        .custom-alert-icon.info {
            background: var(--primary-color);
            color: white;
        }

        .custom-alert-icon.warning {
            background: var(--primary-color);
            color: white;
        }

        .custom-alert-message {
            font-size: var(--font-size-base);
            color: var(--text-dark);
            line-height: 1.5;
            margin-bottom: 20px;
            white-space: pre-line;
        }

        .custom-alert-button {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border: none;
            padding: 10px 24px;
            border-radius: 8px;
            font-size: var(--font-size-sm);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 80px;
        }

        .custom-alert-button:hover {
            background: var(--primary-color-hover);
            transform: translateY(-1px);
        }

        .custom-alert-button:active {
            transform: translateY(0);
        }

        .custom-alert-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        .custom-alert-button.secondary {
            background: var(--bg-panel-secondary);
            color: var(--text-dark);
        }

        .custom-alert-button.secondary:hover {
            background: var(--secondary-color);
            color: var(--text-on-primary);
        }
    </style>
</head>
<body>

    <!-- 鼠标感应区域 -->
    <div class="sidebar-trigger" id="sidebarTrigger"></div>

    <!-- 左侧导航栏包装器 -->
    <div class="sidebar-wrapper collapsed" id="sidebarWrapper">
        <!-- 左侧导航栏 -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-content">
                <a href="#" class="user-avatar">
                    <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="User Avatar">
                </a>
                <div class="nav-group">
                    <div class="nav-item" title="首页" onclick="window.location.href='首页.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                        </svg>
                        <span class="nav-item-text">首页</span>
                    </div>
                    <div class="nav-item" title="书架" onclick="window.location.href='书架.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/>
                        </svg>
                        <span class="nav-item-text">书架</span>
                    </div>
                    <div class="nav-item" title="创意" onclick="window.location.href='创意.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14.06,9.02l0.91,0.91L5.66,19.24L4.75,18.33L14.06,9.02 M17.66,3c-0.25,0-0.51,0.1-0.7,0.29l-1.83,1.83l3.75,3.75l1.83-1.83c0.39-0.39,0.39-1.02,0-1.41l-2.34-2.34C18.17,3.09,17.92,3,17.66,3L17.66,3z M12.06,6.19L3,15.25V19.24h3.99l9.06-9.06L12.06,6.19z"/>
                        </svg>
                        <span class="nav-item-text">创意</span>
                    </div>
                    <div class="nav-item" title="对话" onclick="window.location.href='对话.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                        </svg>
                        <span class="nav-item-text">对话</span>
                    </div>
                    <div class="nav-item active" title="模拟" onclick="window.location.href='模拟.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z M12 4c1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3 1.34 3 3 3z"/>
                        </svg>
                        <span class="nav-item-text">模拟</span>
                    </div>
                </div>
                <div class="sidebar-footer nav-group">
                    <div class="nav-item" title="教程" onclick="window.location.href='教程.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/>
                        </svg>
                        <span class="nav-item-text">教程</span>
                    </div>
                    <div class="nav-item" title="邀请" onclick="window.location.href='邀请.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                        <span class="nav-item-text">邀请</span>
                    </div>
                    <div class="nav-item" title="夜间模式" onclick="toggleNightMode()">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9.37 5.51C9.19 6.15 9.1 6.82 9.1 7.5c0 4.08 3.32 7.4 7.4 7.4.68 0 1.35-.09 1.99-.27C17.45 17.19 14.93 19 12 19c-3.86 0-7-3.14-7-7 0-2.93 1.81-5.45 4.37-6.49z"/>
                        </svg>
                        <span class="nav-item-text">夜间</span>
                    </div>
                </div>
            </div>
        </nav>
    </div>

    <!-- 主内容区 -->
    <main class="main-content">
        <!-- 模拟界面 -->
        <div class="simulation-container">
            <!-- 顶部工具栏 -->
            <div class="simulation-header">
                <div class="header-left">
                    <div class="character-selector">
                        <label>角色设定:</label>
                        <button class="btn-select-character" onclick="openCharacterMarket()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                            </svg>
                            选择角色
                        </button>
                    </div>
                    <button class="btn-create-character" onclick="openCreateCharacterModal()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                        </svg>
                        创建角色
                    </button>
                </div>
                <div class="header-right">
                    <button class="header-btn" onclick="clearChat()">清空对话</button>
                    <button class="header-btn" onclick="exportChat()">导出记录</button>
                    <button class="header-btn" onclick="saveScenario()">保存场景</button>
                    <button class="header-btn" onclick="openHistoryDialog()">历史记录</button>
                </div>
            </div>

            <!-- 消息显示区域 -->
            <div class="chat-container" id="chatContainer">
                <!-- 空状态 -->
                <div class="empty-state" id="emptyState">
                    <div class="empty-icon">🎭</div>
                    <div class="empty-title">开始一场角色扮演对话</div>
                    <div class="empty-desc">选择或创建角色，设定场景背景，让AI扮演不同角色进行互动对话</div>
                </div>

                <!-- 角色指示器 - 始终显示 -->
                <div class="character-indicator" id="characterIndicator">
                    <span class="indicator-label">当前参与角色:</span>
                    <div class="active-characters empty" id="activeCharacters">
                        暂无角色参与
                    </div>
                </div>
                
                <!-- 消息列表 -->
                <div class="chat-wrapper" id="chatWrapper" style="display: none;">
                    <!-- 消息会动态添加到这里 -->
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="input-container">
                <div class="input-wrapper">
                    <div class="input-box">
                        <textarea 
                            class="input-textarea" 
                            id="messageInput"
                            placeholder="以用户身份加入对话，或输入指令推进剧情..."
                            rows="1"
                        ></textarea>
                        <div class="input-actions">
                            <button class="input-action-btn" title="上传文件" onclick="triggerFileUpload()">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M16.5 6v11.5c0 2.21-1.79 4-4 4s-4-1.79-4-4V5c0-1.38 1.12-2.5 2.5-2.5s2.5 1.12 2.5 2.5v10.5c0 .55-.45 1-1 1s-1-.45-1-1V6H10v9.5c0 1.38 1.12 2.5 2.5 2.5s2.5-1.12 2.5-2.5V5c0-2.21-1.79-4-4-4S7 2.79 7 5v12.5c0 3.04 2.46 5.5 5.5 5.5s5.5-2.46 5.5-5.5V6h-1.5z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <button class="send-button" id="sendButton">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                        发送
                    </button>
                    <button class="auto-conversation-button" id="autoConversationButton" onclick="toggleAutoConversation()">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                        自动对话: 开启
                    </button>
                    <button class="auto-restart-button disabled" id="autoRestartButton" onclick="toggleAutoRestart()" title="开启后将在达到最大轮数时自动重启对话">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
                        </svg>
                        自动重启: 关闭
                    </button>
                </div>
                <!-- 隐藏的文件输入 -->
                <input type="file" class="file-input-hidden" id="fileInput" multiple style="display: none;">
            </div>
        </div>

        <!-- 动态总结面板 -->
        <div class="dynamic-summary-panel" id="dynamicSummaryPanel">
            <div class="summary-panel-header">
                <div class="summary-panel-title">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                    </svg>
                    智能分析
                </div>
            </div>
            <div class="summary-panel-content" id="summaryContent">
                <div class="summary-placeholder">
                    <div class="placeholder-icon">🤖</div>
                    <div class="placeholder-text">AI正在分析对话内容...</div>
                    <div class="placeholder-desc">当对话进行到一定程度时，将自动生成智能分析</div>
                </div>
            </div>
        </div>

        <!-- 右侧面板 - 角色管理和场景设定 -->
        <div class="right-panel">
            <div class="panel-header">角色与场景</div>
            <div class="panel-content">
                <!-- 参与角色列表 -->
                <div class="participants-section">
                    <div class="section-title">
                        <span>参与角色</span>
                        <button class="add-participant-btn" onclick="addParticipant()">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                            </svg>
                            添加
                        </button>
                    </div>
                    <div class="participant-list" id="participantList">
                        <!-- 默认角色 -->
                        <div class="participant-item" data-role-id="user" data-role-type="default">
                            <div class="participant-avatar">我</div>
                            <div class="participant-info">
                                <div class="participant-name">用户</div>
                                <div class="participant-model">玩家角色</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 场景设定区域 -->
                <div class="scenario-editor">
                    <div class="section-title">场景设定</div>
                    <div class="editor-field">
                        <label class="field-label">场景背景</label>
                        <textarea 
                            class="field-textarea" 
                            id="scenarioInput"
                            placeholder="描述故事背景或场景预设，例如：在一个未来科技城市，几位来自不同背景的角色在咖啡厅相遇..."
                        ></textarea>
                    </div>
                    <button class="auto-generate-btn" onclick="autoGenerateScenario()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0l4.6-4.6-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
                        </svg>
                        AI随机生成场景
                    </button>
                </div>



                <!-- 关联编辑器内容 -->
                <div class="editor-link-section">
                    <div class="section-title">关联编辑器内容</div>
                    <div class="editor-field">
                        <label class="field-label">关联角色卡</label>
                        <div style="display: flex; gap: 8px;">
                            <select class="link-select" style="flex: 1;">
                                <option value="">选择已有角色卡...</option>
                                <option>主角 - 李明</option>
                                <option>配角 - 张三</option>
                                <option>反派 - 黑衣人</option>
                            </select>
                            <button class="btn-secondary" onclick="openCharacterManager()" style="padding: 8px 12px; font-size: 12px;">管理</button>
                        </div>
                        <div class="editor-hint">添加角色设定到场景上下文</div>
                    </div>
                    <div class="editor-field">
                        <label class="field-label">关联章节</label>
                        <div style="display: flex; gap: 8px;">
                            <select class="link-select" style="flex: 1;">
                                <option value="">选择相关章节...</option>
                                <option>第1章 开端</option>
                                <option>第2章 发展</option>
                                <option>第3章 高潮</option>
                            </select>
                            <button class="btn-secondary" onclick="openChapterManager()" style="padding: 8px 12px; font-size: 12px;">管理</button>
                        </div>
                        <div class="editor-hint">引用章节内容到场景背景</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 角色市场弹窗 -->
    <div class="modal" id="characterMarketModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">角色市场</h2>
                <p class="modal-desc">选择预设的角色设定，快速开始角色扮演对话</p>
            </div>
            
            <!-- 分类标签 -->
            <div class="character-categories">
                <button class="category-btn active" onclick="filterCharacters('all')">全部</button>
                <button class="category-btn" onclick="filterCharacters('fantasy')">奇幻</button>
                <button class="category-btn" onclick="filterCharacters('scifi')">科幻</button>
                <button class="category-btn" onclick="filterCharacters('modern')">现代</button>
                <button class="category-btn" onclick="filterCharacters('history')">历史</button>
                <button class="category-btn" onclick="filterCharacters('anime')">动漫</button>
            </div>

            <!-- 角色网格 -->
            <div class="character-grid" id="characterGrid">
                <!-- 内容由JS动态生成 -->
            </div>

            <div class="modal-actions">
                <button class="btn btn-cancel" onclick="closeCharacterMarket()">取消</button>
                <button class="btn btn-confirm" onclick="selectCharacters()">确认选择</button>
            </div>
        </div>
    </div>

    <!-- 创建角色弹窗 -->
    <div class="modal" id="createCharacterModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">创建自定义角色</h2>
                <p class="modal-desc">创建您专属的角色设定，让AI更好地扮演角色</p>
            </div>
            <form id="characterForm">
                <div class="form-group">
                    <label class="form-label">角色名称</label>
                    <input type="text" class="form-input" id="newCharName" placeholder="例如：神秘法师" required>
                    <div class="form-hint">给您的角色起一个独特的名字</div>
                </div>
                <div class="form-group">
                    <label class="form-label">性格特点</label>
                    <textarea class="form-textarea" id="newCharPersonality" placeholder="描述角色的性格特征、行为模式等..." required></textarea>
                    <div class="form-hint">详细描述角色的性格，AI会根据此设定进行角色扮演</div>
                </div>
                <div class="form-group">
                    <label class="form-label">背景故事</label>
                    <textarea class="form-textarea" id="newCharBackground" placeholder="角色的身世、经历、目标等背景信息..."></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">说话风格</label>
                    <input type="text" class="form-input" id="newCharStyle" placeholder="例如：文言文、口语化、专业术语等">
                </div>
                <div class="form-group">
                    <label class="form-label">外貌描述</label>
                    <textarea class="form-textarea" id="newCharAppearance" placeholder="描述角色的外貌特征、穿着打扮、气质等..."></textarea>
                    <div class="form-hint">详细描述角色的外貌，有助于其他角色更好地互动</div>
                </div>
                <div class="form-group">
                    <label class="form-label">隐藏信息</label>
                    <textarea class="form-textarea" id="newCharSecret" placeholder="只有该角色知道的秘密信息、内心想法、隐藏动机等..."></textarea>
                    <div class="form-hint">这些信息只有角色自己知道，不会被其他角色看到</div>
                </div>
                <button type="button" class="auto-generate-btn" onclick="autoGenerateCharacter()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0l4.6-4.6-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
                    </svg>
                    AI随机生成设定
                </button>
                <div class="modal-actions">
                    <button type="button" class="btn btn-cancel" onclick="closeCreateCharacterModal()">取消</button>
                    <button type="submit" class="btn btn-confirm">
保存角色</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 章节编辑弹窗 -->
    <div class="modal" id="chapterEditModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">编辑章节</h2>
                <p class="modal-desc">编辑章节内容并提取关键场景信息</p>
            </div>
            <form id="chapterEditForm">
                <div class="form-group">
                    <label class="form-label">章节标题</label>
                    <input type="text" class="form-input" id="editChapterTitle" placeholder="输入章节标题" required>
                </div>
                <div class="form-group">
                    <label class="form-label">章节内容</label>
                    <textarea class="form-textarea" id="editChapterContent" placeholder="输入章节内容..." rows="8" required></textarea>
                    <div class="form-hint">详细描述章节的情节发展、人物对话、场景描述等</div>
                </div>
                <div class="form-group">
                    <label class="form-label">AI提取的场景信息</label>
                    <textarea class="form-textarea" id="extractedSceneInfo" placeholder="点击下方按钮让AI分析章节内容并提取关键场景信息..." rows="6" readonly></textarea>
                    <div class="form-hint">AI会自动分析章节内容，提取关键人物、场景、情节要点等信息</div>
                </div>
                <button type="button" class="auto-generate-btn" onclick="extractSceneInfo()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L16 8l-8 8z"/>
                    </svg>
                    AI提取场景信息
                </button>
                <div class="modal-actions">
                    <button type="button" class="btn btn-cancel" onclick="closeChapterEditModal()">取消</button>
                    <button type="submit" class="btn btn-confirm">保存章节</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 模型选择弹窗 -->
    <div class="modal" id="modelSelectModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">选择AI模型</h2>
                <p class="modal-desc">为角色选择合适的AI模型</p>
            </div>
            
            <!-- 模型网格 -->
            <div class="model-grid" id="modelGrid">
                <!-- 内容由JS动态生成 -->
            </div>

            <div class="modal-actions">
                <button class="btn btn-cancel" onclick="closeModelSelect()">取消</button>
                <button class="btn btn-confirm" onclick="confirmModelSelect()">确认</button>
            </div>
        </div>
    </div>

    <!-- 历史记录弹窗 -->
    <div class="modal" id="historyModal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h2 class="modal-title">场景历史记录</h2>
                <button class="modal-close" onclick="closeHistoryDialog()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="history-stats" style="display: flex; gap: 20px; margin-bottom: 20px; padding: 16px; background: var(--bg-panel-secondary); border-radius: 8px;">
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: 600; color: var(--primary-color);" id="totalScenarios">0</div>
                        <div style="font-size: 12px; color: var(--text-light);">保存场景数</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: 600; color: var(--accent-color);" id="totalConversations">0</div>
                        <div style="font-size: 12px; color: var(--text-light);">对话轮数</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 24px; font-weight: 600; color: var(--secondary-color);" id="activeCharacters">0</div>
                        <div style="font-size: 12px; color: var(--text-light);">活跃角色</div>
                    </div>
                </div>

                <div class="history-list" id="historyList" style="max-height: 400px; overflow-y: auto;">
                    <!-- 动态生成历史记录 -->
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn-cancel" onclick="clearScenarioHistory()">清空历史记录</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 场景详情弹窗 -->
    <div class="modal" id="scenarioDetailModal">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h2 class="modal-title" id="detailModalTitle">场景详情</h2>
                <button class="modal-close" onclick="closeScenarioDetail()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="detail-field">
                    <label class="field-label">场景标题</label>
                    <input type="text" class="field-input" id="detailTitle" placeholder="输入场景标题">
                </div>
                <div class="detail-field">
                    <label class="field-label">场景内容</label>
                    <textarea class="field-textarea" id="detailContent" rows="8" readonly></textarea>
                </div>
                <div class="detail-field">
                    <label class="field-label">相关角色</label>
                    <input type="text" class="field-input" id="detailCharacters" readonly>
                </div>
                <div class="detail-field">
                    <label class="field-label">创建时间</label>
                    <input type="text" class="field-input" id="detailTimestamp" readonly>
                </div>
                <div class="detail-field">
                    <label class="field-label">对话轮数</label>
                    <input type="text" class="field-input" id="detailConversationCount" readonly>
                </div>
                <div class="detail-field">
                    <label class="field-label">对话记录</label>
                    <div id="detailChatHistory" style="max-height: 300px; overflow-y: auto; border: 1px solid var(--border-color); padding: 10px; border-radius: 5px; background: var(--bg-primary); margin-top: 5px; font-size: 14px; line-height: 1.4;">
                        <!-- 对话记录将在这里显示 -->
                    </div>
                </div>
                <div class="modal-actions" style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end; flex-wrap: wrap;">
                    <button class="btn-secondary" onclick="loadScenarioFromDetail()">仅加载场景</button>
                    <button class="btn-primary" onclick="loadScenarioWithChat()">加载场景+对话</button>
                    <button class="btn-secondary" onclick="exportScenarioDetail()">导出场景</button>
                    <button class="btn-primary" onclick="updateScenarioTitle()">保存标题</button>
                    <button class="btn-cancel" onclick="deleteScenarioFromDetail()">删除</button>
                    <button class="btn-secondary" onclick="closeScenarioDetail()">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 自定义提醒弹窗 -->
    <div class="custom-alert" id="customAlert">
        <div class="custom-alert-content">
            <div class="custom-alert-icon" id="customAlertIcon">
                ✓
            </div>
            <div class="custom-alert-message" id="customAlertMessage">
                这是一条提醒消息
            </div>
            <button class="custom-alert-button" id="customAlertButton" onclick="closeCustomAlert()">
                确定
            </button>
        </div>
    </div>

    <script>
        // --- 自定义提醒弹窗功能 ---
        function showCustomAlert(message, type = 'info') {
            const alertElement = document.getElementById('customAlert');
            const iconElement = document.getElementById('customAlertIcon');
            const messageElement = document.getElementById('customAlertMessage');
            
            // 设置消息内容
            messageElement.textContent = message;
            
            // 设置图标和样式
            iconElement.className = 'custom-alert-icon ' + type;
            switch(type) {
                case 'success':
                    iconElement.textContent = '✓';
                    break;
                case 'error':
                    iconElement.textContent = '✕';
                    break;
                case 'info':
                default:
                    iconElement.textContent = 'ℹ';
                    break;
            }
            
            // 显示弹窗
            alertElement.style.display = 'flex';
            setTimeout(() => {
                alertElement.classList.add('show');
            }, 10);
        }
        
        function closeCustomAlert() {
            const alertElement = document.getElementById('customAlert');
            alertElement.classList.remove('show');
            setTimeout(() => {
                alertElement.style.display = 'none';
            }, 300);
        }
        
        // 重写alert函数
        function alert(message) {
            showCustomAlert(message, 'info');
        }
        
        // 添加成功和错误提示的便捷函数
        function showSuccess(message) {
            showCustomAlert(message, 'success');
        }
        
        function showError(message) {
            showCustomAlert(message, 'error');
        }
        
        // 点击弹窗外部关闭
        document.getElementById('customAlert').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCustomAlert();
            }
        });
        
        // ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const alertElement = document.getElementById('customAlert');
                if (alertElement.classList.contains('show')) {
                    closeCustomAlert();
                }
            }
        });

        // 自定义确认对话框
        function showCustomConfirm(message, title = '确认操作') {
            return new Promise((resolve) => {
                // 创建确认弹窗HTML
                const confirmHTML = `
                    <div class="custom-alert" id="customConfirm">
                        <div class="custom-alert-content">
                            <div class="custom-alert-icon warning">⚠</div>
                            <div class="custom-alert-message">${message}</div>
                            <div class="custom-alert-buttons">
                                <button class="custom-alert-button secondary" onclick="closeCustomConfirm(false)">取消</button>
                                <button class="custom-alert-button" onclick="closeCustomConfirm(true)">确定</button>
                            </div>
                        </div>
                    </div>
                `;
                
                // 添加到页面
                document.body.insertAdjacentHTML('beforeend', confirmHTML);
                
                const confirmElement = document.getElementById('customConfirm');
                
                // 显示动画
                confirmElement.style.display = 'flex';
                setTimeout(() => {
                    confirmElement.classList.add('show');
                }, 10);
                
                // 关闭函数
                window.closeCustomConfirm = function(result) {
                    confirmElement.classList.remove('show');
                    setTimeout(() => {
                        if (confirmElement.parentNode) {
                            confirmElement.parentNode.removeChild(confirmElement);
                        }
                    }, 300);
                    resolve(result);
                };
            });
        }

        // 重写confirm函数
        function confirm(message) {
            return showCustomConfirm(message);
        }

        // --- 主题同步功能 ---
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'default';
            document.documentElement.setAttribute('data-theme', savedTheme);
        }

        // 初始化主题
        initTheme();

        // 监听localStorage变化，实现跨页面主题同步
        window.addEventListener('storage', (e) => {
            if (e.key === 'theme') {
                document.documentElement.setAttribute('data-theme', e.newValue);
            }
        });

        // 夜间模式快捷切换
        function toggleNightMode() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'default' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        }

        // --- 侧边栏自动隐藏功能 (优化版) ---
        const sidebarWrapper = document.getElementById('sidebarWrapper');
        const sidebarTrigger = document.getElementById('sidebarTrigger');
        let sidebarTimer;

        function showSidebar() {
            clearTimeout(sidebarTimer);
            sidebarWrapper.classList.remove('collapsed');
        }

        function hideSidebar() {
            clearTimeout(sidebarTimer);
            sidebarTimer = setTimeout(() => {
                sidebarWrapper.classList.add('collapsed');
            }, 300);
        }

        sidebarTrigger.addEventListener('mouseenter', showSidebar);
        sidebarWrapper.addEventListener('mouseenter', showSidebar);
        sidebarWrapper.addEventListener('mouseleave', hideSidebar);

        // --- 模拟功能核心逻辑 ---
        let currentCharacters = [];
        let selectedCharacterIds = new Set();
        let chatHistory = [];
        let selectedModelForCharacter = {};
        let currentEditingCharacterId = null;
        let pendingCharactersForModelSelect = []; // 待选择模型的角色队列

        // API密钥轮询机制
        const apiKeys = [
            'sk-pOpzkJmrv0OqgpldF61f6f0b1f734e01B4B7Be74Dc0fCfD6',
            'sk-Dx7IEtZDPoQg3hyt5d20B7F363A6474785C87bC4Dd949114',
            'sk-rUsfjQ2IKuWoo4cP188bEfCfC1C04e6fBeA3240937B4E4Af',
            'sk-dmDVnaLRs00AZTbw0b7dBa5b9b694d919753Fb22EeE2C229',
            'sk-iHjR6RtNfH7za1W076715fEaBf4f4fA18657255fB8EaE0Ca'
        ];
        let currentKeyIndex = 0;

        // 模型映射关系
        const modelMapping = {
            '细腻贴合': 'doubao-1-5-thinking-pro-250415',
            '逻辑精准': 'gemini-2.5-pro',
            '灵活创意': 'claude-3-7-sonnet-20250219',
            '稳定版': 'gemini-2.5-flash',
            '综合版': 'claude-sonnet-4-20250514',
            '测试版': 'doubao-seed-1-6-250615'
        };

        // 角色上下文隔离机制
        const roleContexts = {};

        // 获取下一个API密钥
        function getNextApiKey() {
            const key = apiKeys[currentKeyIndex];
            currentKeyIndex = (currentKeyIndex + 1) % apiKeys.length;
            return key;
        }
        
        // 通用AI API调用函数
        async function callAIAPI(prompt, modelName, temperature = 0.7, maxTokens = 2000) {
            const apiKey = getNextApiKey();
            
            try {
                const response = await fetch('http://bjcqapi.cn/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`,
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        model: modelName,
                        messages: [{
                            role: 'user',
                            content: prompt
                        }],
                        temperature: temperature,
                        max_tokens: maxTokens
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                }
                
                const data = await response.json();
                const content = data.choices?.[0]?.message?.content;
                
                if (!content) {
                    throw new Error('AI返回内容为空');
                }
                
                return content.trim();
            } catch (error) {
                console.error('AI API调用失败:', error);
                throw error;
            }
        }

        // 初始化角色上下文
        function initRoleContext(roleId, roleName) {
            if (!roleContexts[roleId]) {
                roleContexts[roleId] = {
                    history: [],
                    lastActive: new Date(),
                    memoryLimit: Infinity, // 取消上下文长度限制，让模型拥有更长的记忆
                    name: roleName
                };
            }
            return roleContexts[roleId];
        }

        // 添加消息到角色上下文
        function addMessageToContext(roleId, message) {
            const context = roleContexts[roleId];
            if (context) {
                context.history.push(message);
                context.lastActive = new Date();
                
                // 限制历史记录长度
                if (context.history.length > context.memoryLimit) {
                    context.history.shift();
                }
            }
        }

        // 获取角色的对话历史
        function getRoleHistory(roleId) {
            return roleContexts[roleId]?.history || [];
        }

        // 可用的AI模型列表
        const availableModels = [
            { id: 'delicate', name: '细腻贴合', desc: '注重情感细节和角色深度', icon: '🎭' },
            { id: 'precise', name: '逻辑精准', desc: '擅长逻辑推理和分析', icon: '🧠' },
            { id: 'stable', name: '稳定版', desc: '平衡稳定的通用模型', icon: '⚖️' },
            { id: 'creative', name: '灵活创意', desc: '富有创造力和想象力', icon: '✨' },
            { id: 'comprehensive', name: '综合版', desc: '多功能综合型模型', icon: '🌟' },
            { id: 'test', name: '测试版', desc: '最新实验性功能', icon: '🔬' }
        ];

        // 生成唯一ID
        function generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        // 自动调整输入框高度
        const messageInput = document.getElementById('messageInput');
        const scenarioInput = document.getElementById('scenarioInput');
        
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });

        scenarioInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 200) + 'px';
        });

        // 发送消息
        async function sendMessage() {
            const text = messageInput.value.trim();
            const scenario = scenarioInput.value.trim();
            
            if (!text && !scenario) return;

            // 禁用发送按钮，显示加载状态
            const sendButton = document.getElementById('sendButton');
            sendButton.disabled = true;
            sendButton.innerHTML = '<span class="loading-dots"></span>';

            try {
                // 隐藏空状态
                const emptyState = document.getElementById('emptyState');
                const chatWrapper = document.getElementById('chatWrapper');
                if (emptyState.style.display !== 'none') {
                    emptyState.style.display = 'none';
                    chatWrapper.style.display = 'flex';
                }

                // 如果是场景设定，添加系统消息
                if (scenario && !chatHistory.some(msg => msg.type === 'scenario')) {
                    addSystemMessage(`场景设定：${scenario}`);
                    chatHistory.push({ type: 'scenario', content: scenario });
                    
                    // 将场景设定添加到所有角色的上下文中
                    currentCharacters.forEach(char => {
                        if (char.id !== 'user') {
                            const context = initRoleContext(char.id, char.name);
                            context.history.push({
                                role: 'system',
                                content: `当前场景：${scenario}`
                            });
                        }
                    });
                }

                // 如果有用户输入，添加用户消息
                if (text) {
                    addChatMessage('user', '用户', text, '玩家角色');
                    messageInput.value = '';
                    messageInput.style.height = 'auto';
                    
                    // 将用户消息添加到所有角色的上下文中
                    currentCharacters.forEach(char => {
                        if (char.id !== 'user') {
                            addMessageToContext(char.id, {
                                role: 'user',
                                content: text
                            });
                        }
                    });
                    
                    // 调用API获取角色回复
                    await generateCharacterResponses(text);
                    
                    // 触发智能分析（当对话达到一定数量时）
                    const chatHistory = getChatHistory();
                    if (chatHistory.length >= 5 && chatHistory.length % 3 === 0) {
                        setTimeout(async () => {
                            await generateAndDisplaySummary();
                        }, 1000); // 延迟1秒执行，避免与角色回复冲突
                    }
                }

                // 更新角色指示器
                updateCharacterIndicator();
            } catch (error) {
                // 发送消息出错
                addSystemMessage(`发送消息出错: ${error.message || '未知错误'}`);
            } finally {
                // 恢复发送按钮状态
                sendButton.disabled = false;
                sendButton.innerHTML = `
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                    发送
                `;
            }
        }

        // 处理AI响应 - 简化版本，移除复杂的内容提取
        async function processAIResponse(response, roleId) {
            // 处理AI响应
            
            // 直接使用响应内容，不进行复杂的提取和过滤
            let content = String(response || '').trim();
            
            // 基本的空值检查
            if (!content) {
                // 响应内容为空
                const character = currentCharacters.find(c => c.id === roleId);
                if (character) {
                    return `*${character.name}思考了一下* 抱歉，我刚才走神了。`;
                }
                return '模型响应为空，请重试';
            }
            
            // 移除可能的角色名标注（如果AI违规添加了）
            const character = currentCharacters.find(c => c.id === roleId);
            if (character) {
                // 移除开头的角色名标注
                const namePattern = new RegExp(`^${character.name}\s*[:：]\s*`, 'i');
                content = content.replace(namePattern, '');
                
                // 移除引号包围的角色名标注
                const quotedNamePattern = new RegExp(`^[""'']${character.name}[""'']\s*[:：]\s*`, 'i');
                content = content.replace(quotedNamePattern, '');
            }
            
            // 格式化对话和动作
            const formattedContent = formatDialogueAndActions(content);
            
            // 处理完成
            return formattedContent;
        }

        // 移除系统标记
        function removeSystemMarkers(content) {
            if (!content || typeof content !== 'string') {
                // 输入无效
                return content;
            }
            
            // 处理系统标记
            
            // 非常保守的过滤，只移除最明确的系统标记
            let filtered = content;
            
            // 只移除明确的保密协议标记（如果存在）
            if (content.includes('##保密协议')) {
                filtered = filtered.replace(/##保密协议[\s\S]*?##执行优先级[\s\S]*?(?=\n\n|$)/g, '');
                // 移除保密协议
            }
            
            // 只移除明确的系统指令（如果存在）
            if (content.includes('记住：你就是')) {
                filtered = filtered.replace(/^记住：你就是.*?，直接以该身份说话，开始对话。$/gm, '');
                // 移除系统指令
            }
            
            // 清理多余空行，但保留内容
            filtered = filtered.replace(/\n{3,}/g, '\n\n').trim();
            
            // 过滤完成
            
            return filtered;
        }

        // 检测角色混淆
        function detectRoleConfusion(content, roleId) {
            const roleName = roleContexts[roleId]?.name;
            if (!roleName) return false;
            
            // 检测角色混淆
            
            // 检查是否有角色混淆的迹象 - 只检测明确的AI身份标识
            const confusionPatterns = [
                // 中文AI助手标识
                /我是AI助手/i,
                /作为AI助手/i,
                /我是人工智能/i,
                /我只是一个语言模型/i,
                /我是Claude/i,
                /我是ChatGPT/i,
                // 英文AI助手标识
                /I'm Claude/i,
                /I am Claude/i,
                /I'm an AI assistant/i,
                /I am an AI assistant/i,
                /I'm ChatGPT/i,
                /I am ChatGPT/i,
                /created by Anthropic/i,
                /created by OpenAI/i,
                /I'm a language model/i,
                /I am a language model/i,
                /I'm an artificial intelligence/i,
                /I am an artificial intelligence/i
            ];
            
            const hasConfusion = confusionPatterns.some(pattern => {
                const match = pattern.test(content);
                if (match) {
                    // 检测到角色混淆
                }
                return match;
            });
            
            // 角色混淆检测完成
            return hasConfusion;
        }

        // 重新生成响应
        async function regenerateResponse(roleId) {
            // 重新生成响应
            
            const character = currentCharacters.find(c => c.id === roleId);
            if (!character) {
                // 未找到角色
                return "角色响应出错";
            }
            
            // 重新生成角色响应
            
            // 强化角色提示，避免AI助手身份泄露
            const strongRolePrompt = `请严格按照${character.name}的角色设定进行回应，不要提及任何AI助手、语言模型或技术相关内容。直接以${character.name}的身份和语气回应。`;
            
            try {
                const response = await generateAIResponse(character, strongRolePrompt);
                
                // 再次检查是否仍有角色混淆
                if (detectRoleConfusion(response, roleId)) {
                    // 重新生成后仍有角色混淆
                    return `*${character.name}正在思考如何回应...*`;
                }
                
                return response;
            } catch (error) {
                // 重新生成响应失败
                return `*${character.name}暂时无法回应，请稍后再试*`;
            }
        }

        // 更新角色指示器 - 修复版本
        function updateCharacterIndicator() {
            const indicator = document.getElementById('characterIndicator');
            const activeCharactersDiv = document.getElementById('activeCharacters');
            
            // 获取除用户外的所有角色
            const aiCharacters = currentCharacters.filter(char => char.id !== 'user');
            
            // 清空现有内容
            activeCharactersDiv.innerHTML = '';
            
            if (aiCharacters.length > 0) {
                // 移除空状态样式
                activeCharactersDiv.classList.remove('empty');
                
                // 为每个AI角色创建芯片
                aiCharacters.forEach(char => {
                    const chip = document.createElement('div');
                    chip.className = 'character-chip';
                    chip.innerHTML = `
                        <div class="character-chip-avatar">${char.name.charAt(0)}</div>
                        <div class="character-chip-name">${char.name}</div>
                        <div class="character-chip-model">${selectedModelForCharacter[char.id] || '细腻贴合'}</div>
                    `;
                    chip.addEventListener('click', () => {
                        currentEditingCharacterId = char.id;
                        openModelSelect();
                    });
                    activeCharactersDiv.appendChild(chip);
                });
            } else {
                // 显示"暂无角色参与"
                activeCharactersDiv.classList.add('empty');
                activeCharactersDiv.textContent = '暂无角色参与';
            }
            
            // 角色指示器始终显示
            indicator.style.display = 'flex';
        }

        // 生成消息唯一ID
        function generateMessageId() {
            return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        
        // 统一的聊天历史添加函数（带重复检查）
        function addToChatHistory(messageData) {
            // 检查是否已存在相同内容的消息
            const isDuplicate = chatHistory.some(msg => {
                // 检查内容相同且时间相近（5秒内）
                const timeDiff = Math.abs(new Date(msg.timestamp) - new Date(messageData.timestamp));
                return msg.content === messageData.content && 
                       (msg.sender === messageData.sender || msg.name === messageData.name) &&
                       timeDiff < 5000;
            });
            
            if (!isDuplicate) {
                chatHistory.push(messageData);
                lastMessageId = messageData.id;
                // 添加新消息到历史
            } else {
                // 检测到重复消息
            }
        }
        
        // 统一的自动对话调度函数
        function scheduleNextConversation(delay = 400) {
            // 清除之前的定时器
            if (autoConversationTimer) {
                clearTimeout(autoConversationTimer);
                autoConversationTimer = null;
            }
            
            // 动态调整延迟时间，让对话更自然且更快速
            const dynamicDelay = Math.max(300, delay + Math.random() * 200 - 100); // 300-600ms随机范围
            
            // 设置新的定时器
            autoConversationTimer = setTimeout(() => {
                if (autoConversationEnabled && conversationRounds < maxAutoRounds) {
                    checkAndContinueConversation();
                }
            }, dynamicDelay);
            
            // 已调度下一轮自动对话
        }
        
        // 添加聊天消息
        function addChatMessage(roleId, name, text, roleType, avatar = '我') {
            const chatWrapper = document.getElementById('chatWrapper');
            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-message ${roleId === 'user' ? 'user' : 'ai'}`;
            
            const time = new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
            
            // 格式化动作和对话内容
            const formattedText = formatDialogueAndActions(text);
            
            messageDiv.innerHTML = `
                <div class="chat-avatar" style="${getAvatarStyle(roleId)}">${avatar}</div>
                <div class="chat-content">
                    <div class="chat-header">
                        <span class="chat-name">${name}</span>
                        <span class="chat-role">${roleType}</span>
                        <span class="chat-time">${time}</span>
                    </div>
                    <div class="chat-bubble">
                        <div class="chat-text">${formattedText}</div>
                    </div>
                </div>
            `;
            
            chatWrapper.appendChild(messageDiv);
            document.getElementById('chatContainer').scrollTop = document.getElementById('chatContainer').scrollHeight;
        }

        // 获取角色头像样式
        function getAvatarStyle(roleId) {
            const colors = {
                'user': 'background: var(--accent-color);',
                'narrator': 'background: var(--secondary-color);'
            };
            return colors[roleId] || 'background: var(--primary-color);';
        }

        // 添加系统消息
        function addSystemMessage(text) {
            const chatWrapper = document.getElementById('chatWrapper');
            const systemMsg = document.createElement('div');
            systemMsg.className = 'system-notification';
            systemMsg.textContent = text;
            chatWrapper.appendChild(systemMsg);
            document.getElementById('chatContainer').scrollTop = document.getElementById('chatContainer').scrollHeight;
        }


        // 检查并继续自动对话
         let autoConversationEnabled = true; // 控制自动对话的开关
         let conversationRounds = 0; // 记录自动对话轮数
         const maxAutoRounds = 50; // 增加最大自动对话轮数到50轮
         let autoConversationTimer = null; // 自动对话定时器
         let scenarioUpdateTimer = null; // 独立的场景更新定时器
         let lastMessageId = null; // 最后一条消息的ID
         let autoRestartEnabled = false; // 自动重启功能开关
         let errorRetryCount = 0; // 错误重试计数器
         const maxErrorRetries = 5; // 最大错误重试次数
         
         // 切换自动对话状态
         function toggleAutoConversation() {
             autoConversationEnabled = !autoConversationEnabled;
             conversationRounds = 0; // 重置轮数计数
             
             const button = document.getElementById('autoConversationButton');
              if (autoConversationEnabled) {
                  button.innerHTML = `
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                      </svg>
                      自动对话: 开启
                  `;
                  button.classList.remove('disabled');
                  addSystemMessage('🚀 自动对话已开启，AI角色将自动进行对话，场景将定期更新');
                  
                  // 重置计数器
                  errorRetryCount = 0;
                  
                  // 添加初始触发逻辑
                  scheduleNextConversation(600); // 进一步减少初始延迟
                  
                  // 启动独立的场景更新定时器（每2分钟更新一次）
                  startScenarioUpdateTimer();
                  
              } else {
                  button.innerHTML = `
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm7-7H5v14h14V5z"/>
                      </svg>
                      自动对话: 关闭
                  `;
                  button.classList.add('disabled');
                  addSystemMessage('自动对话已关闭');
                  
                  // 停止场景更新定时器
                  stopScenarioUpdateTimer();
              }
             
             // console.log('自动对话状态:', autoConversationEnabled ? '开启' : '关闭');
         }
         
         // 切换自动重启状态
         function toggleAutoRestart() {
             autoRestartEnabled = !autoRestartEnabled;
             
             const button = document.getElementById('autoRestartButton');
             if (autoRestartEnabled) {
                 button.innerHTML = `
                     <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                         <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
                     </svg>
                     自动重启: 开启
                 `;
                 button.classList.remove('disabled');
                 addSystemMessage('🔄 自动重启已开启，达到最大轮数时将自动重启对话');
             } else {
                 button.innerHTML = `
                     <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                         <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
                     </svg>
                     自动重启: 关闭
                 `;
                 button.classList.add('disabled');
                 addSystemMessage('⏹️ 自动重启已关闭，达到最大轮数时将停止对话');
             }
             
             // 自动重启状态更新
         }
        
        function checkAndContinueConversation() {
            // 检查自动对话状态
            
            // 如果自动对话被禁用或达到最大轮数，则停止
            if (!autoConversationEnabled) {
                // 自动对话已被禁用
                return;
            }
            
            if (conversationRounds >= maxAutoRounds) {
                if (autoRestartEnabled) {
                    // 已达到最大轮数，自动重启对话
                    conversationRounds = 0; // 重置轮数计数器
                    errorRetryCount = 0; // 重置错误计数器
                    addSystemMessage(`🔄 自动对话已达到${maxAutoRounds}轮，自动重启继续对话`);
                    // 继续执行，不返回
                } else {
                    // 已达到最大轮数，停止自动对话
                    autoConversationEnabled = false;
                    const button = document.getElementById('autoConversationButton');
                    button.innerHTML = `
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm7-7H5v14h14V5z"/>
                        </svg>
                        自动对话: 关闭
                    `;
                    button.classList.add('disabled');
                    addSystemMessage('自动对话已达到最大轮数，自动关闭');
                    return;
                }
            }
            
            // 获取AI角色
            const aiCharacters = currentCharacters.filter(char => char.id !== 'user');
            // 检查AI角色数量
            
            if (aiCharacters.length < 1) {
                // 没有AI角色，无法进行自动对话
                addSystemMessage('没有AI角色参与，无法进行自动对话');
                return;
            }
            
            // 随机选择一个角色发起对话
            const randomCharacter = aiCharacters[Math.floor(Math.random() * aiCharacters.length)];
            
            conversationRounds++;
            // 开始自动对话
            
            // 生成自动对话内容，并添加错误处理和重试机制
            try {
                generateAutoMessage(randomCharacter);
            } catch (error) {
                // 自动对话生成失败
                // 如果生成失败，延迟后重试
                if (autoConversationEnabled && conversationRounds < maxAutoRounds) {
                    // 对话生成异常，2秒后重试
                    scheduleNextConversation(800); // 进一步减少异常重试延迟
                }
            }
            
            // 备用检查机制：仅在generateAutoMessage可能失败时作为保险
            setTimeout(() => {
                if (autoConversationEnabled && conversationRounds < maxAutoRounds) {
                    // 检查是否有新的对话产生
                    const lastMessageTime = chatHistory.length > 0 ? new Date(chatHistory[chatHistory.length - 1].timestamp) : new Date(0);
                    const timeSinceLastMessage = Date.now() - lastMessageTime.getTime();
                    
                    // 如果超过8秒没有新消息，且没有定时器在运行，重新触发对话
                    if (timeSinceLastMessage > 8000 && !autoConversationTimer) {
                        // 检测到对话可能停止，重新触发自动对话
                        scheduleNextConversation(0);
                    }
                }
            }, 10000); // 10秒后进行检查，给足够的时间让正常流程完成
        }
        
        // 生成自动消息
        async function generateAutoMessage(character) {
            try {
                // 构建自动对话的提示
                const autoPrompt = `请以${character.name}的身份，根据当前场景和对话历史，主动发起一段对话。重点要求：

【剧情推进】优先选择以下类型的对话：
1. 揭露新的线索或信息
2. 提出关键问题或质疑
3. 做出重要决定或行动
4. 引发冲突或紧张局面
5. 推动故事向前发展

【次要选择】如果剧情暂时平缓，可以：
- 深化角色关系
- 探讨当前事件的影响
- 为后续发展埋下伏笔

【严格要求】：
- 避免无意义的闲聊和客套话
- 每句话都要有明确目的
- 保持角色性格特点
- 30-100字，内容要有分量
- 可以使用*动作*描述增强表现力

请直接输出对话内容，不要包含其他说明。`;
                
                // 获取角色的完整上下文
                const characterContext = roleContexts[character.id]?.history || [];
                
                // 构建消息数组，保持与正常对话一致的逻辑
                let messages = [
                    { role: 'system', content: `你是${character.name}，${character.personality}。${character.background || ''}

【核心使命】：推动剧情发展，避免无意义对话

【对话原则】：
1. 每次发言都要有明确目的：揭露信息、提出质疑、做出决定、引发冲突等
2. 优先关注当前场景的关键问题和矛盾点
3. 避免纯粹的寒暄、客套话和无关紧要的闲聊
4. 根据角色性格，选择合适的推进方式（直接质疑、暗示、观察、行动等）

【输出规则】：
1. 直接输出对话内容，不要在回复前加上"${character.name}:"或任何角色名标注
2. 不要重复之前已经说过的内容
3. 保持角色一致性，但要让角色更主动、更有目的性` }
                ];
                
                // 添加历史消息（排除之前的系统消息，但保留场景更新信息）
                const filteredHistory = characterContext.filter(msg => 
                    msg.role !== 'system' || msg.content.includes('【场景更新】') || msg.content.includes('当前场景：')
                );
                messages.push(...filteredHistory);
                
                // 添加自动对话提示
                messages.push({
                    role: 'user',
                    content: autoPrompt
                });
                
                const apiKey = getNextApiKey();
                const selectedModel = selectedModelForCharacter[character.id] || character.model || '细腻贴合';
                const modelId = modelMapping[selectedModel] || 'doubao-seed-1-6-250615';
                
                const response = await fetch('http://bjcqapi.cn/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`,
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        model: modelId,
                        messages: messages,
                        temperature: 0.8, // 较高的创造性
                        max_tokens: 200
                    })
                });
                
                if (!response.ok) {
                    // 自动对话生成失败
                    // API请求失败时，也要继续调度下一轮对话，避免停止
                    if (autoConversationEnabled && conversationRounds < maxAutoRounds) {
                        // API请求失败，5秒后重试下一轮对话
                        scheduleNextConversation(1500); // 进一步减少API失败重试延迟
                    }
                    return;
                }
                
                const data = await response.json();
                const autoMessage = data.choices?.[0]?.message?.content?.trim();
                
                if (autoMessage) {
                    // 添加到聊天界面，显示为该角色的消息
                    addChatMessage(character.id, character.name, autoMessage, selectedModelForCharacter[character.id] || character.model || '细腻贴合', character.avatar || character.name.charAt(0));
                    
                    // 添加到聊天历史（带重复检查）
                    const messageData = {
                        role: character.id,
                        name: character.name,
                        content: autoMessage,
                        timestamp: new Date().toISOString(),
                        id: generateMessageId()
                    };
                    addToChatHistory(messageData);
                    
                    // 将消息添加到所有角色的上下文中（不重复添加角色名）
                    for (const char of currentCharacters) {
                        if (char.id !== 'user') {
                            addMessageToContext(char.id, {
                                role: char.id === character.id ? 'assistant' : 'user',
                                content: char.id === character.id ? autoMessage : `${character.name}: ${autoMessage}`
                            });
                        }
                    }
                    
                    // 成功生成消息后，调度下一轮自动对话
                    // 自动对话生成成功
                    
                    // 每5轮自动对话后进行场景更新（保留原有机制作为备用）
                    if (conversationRounds % 5 === 0) {
                        // 触发基于轮数的场景更新
                        try {
                            await updateCurrentSituation();
                        } catch (error) {
                            // 场景更新失败
                        }
                    }
                    
                    // 重置错误计数器（成功生成消息后）
                    errorRetryCount = 0;
                    
                    // 调度下一轮自动对话，给适当的延迟让用户看清楚
                    if (autoConversationEnabled && conversationRounds < maxAutoRounds) {
                        scheduleNextConversation(400); // 进一步缩短延迟，提高对话流畅度
                    }
                } else {
                    // 如果没有生成有效消息，也要继续调度下一轮对话
                    // 未生成有效消息，3秒后重试下一轮对话
                    if (autoConversationEnabled && conversationRounds < maxAutoRounds) {
                        scheduleNextConversation(1000); // 进一步减少重试延迟
                    }
                }
                
            } catch (error) {
                // 生成自动对话失败
                errorRetryCount++;
                
                // 改进的错误处理机制
                if (errorRetryCount <= maxErrorRetries && autoConversationEnabled && conversationRounds < maxAutoRounds) {
                    const retryDelay = Math.min(5000 + (errorRetryCount * 2000), 15000); // 递增延迟，最大15秒
                    // 生成对话异常，进行重试
                    addSystemMessage(`⚠️ 对话生成失败，正在进行第${errorRetryCount}次重试...`);
                    scheduleNextConversation(retryDelay);
                } else if (errorRetryCount > maxErrorRetries) {
                    // 错误重试次数超限，暂停自动对话
                    addSystemMessage(`❌ 连续${maxErrorRetries}次生成失败，自动对话已暂停。请检查网络连接或稍后手动重启。`);
                    autoConversationEnabled = false;
                    const button = document.getElementById('autoConversationButton');
                    button.innerHTML = `
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm7-7H5v14h14V5z"/>
                        </svg>
                        自动对话: 关闭
                    `;
                    button.classList.add('disabled');
                }
            }
        }
        
        // 启动独立的场景更新定时器
        function startScenarioUpdateTimer() {
            // 清除之前的定时器
            stopScenarioUpdateTimer();
            
            // 设置新的定时器，每2分钟（120000ms）更新一次场景
            scenarioUpdateTimer = setInterval(async () => {
                if (autoConversationEnabled && chatHistory.length > 5) {
                    // 定时触发场景自动更新
                    try {
                        await updateCurrentSituation();
                        addSystemMessage('🔄 场景已定时自动更新');
                    } catch (error) {
                        // 定时场景更新失败
                        addSystemMessage('⚠️ 场景定时更新失败，将在下次定时时重试');
                    }
                }
            }, 120000); // 2分钟
            
            // 场景自动更新定时器已启动
        }
        
        // 停止场景更新定时器
        function stopScenarioUpdateTimer() {
            if (scenarioUpdateTimer) {
                clearInterval(scenarioUpdateTimer);
                scenarioUpdateTimer = null;
                // 场景自动更新定时器已停止
            }
        }
        
        // 自动归纳当前情况并更新场景
        async function updateCurrentSituation() {
            try {
                // 获取最近的对话历史（最多10条消息）
                const recentHistory = chatHistory.slice(-10);
                if (recentHistory.length < 3) return; // 对话太少时不进行归纳
                
                // 构建归纳请求
                const historyText = recentHistory
                    .filter(msg => msg.type === 'message')
                    .map(msg => `${msg.sender}: ${msg.content}`)
                    .join('\n');
                
                const currentScenario = document.getElementById('scenarioInput').value.trim();
                
                const maxWords = Math.min(50 + (conversationRounds * 20), 200); // 基础50字，每轮增加20字，最大200字
                const summaryPrompt = `请根据以下对话内容，简洁地归纳当前的情况和故事发展，重点关注：
1. 当前发生了什么事件
2. 各角色的状态和关系变化
3. 是否有人被怀疑或指控
4. 当前的紧张点或冲突
5. 故事的发展方向

原始场景：${currentScenario}

最近对话：
${historyText}

请用${maxWords}字左右归纳当前情况，随着对话深入可以包含更多细节，格式："当前情况：[归纳内容]"，不要包含其他内容。`;
                
                // 调用AI进行归纳
                const summary = await callAIAPI(summaryPrompt, 'claude-sonnet-4-20250514');
                
                if (summary && summary.includes('当前情况：')) {
                     // 提取归纳内容
                     const situationSummary = summary.replace('当前情况：', '').trim();
                     
                     // 更新场景输入框
                     const scenarioInput = document.getElementById('scenarioInput');
                     let originalScenario = currentScenario || '一个轻松愉快的日常环境';
                     
                     // 移除之前的【当前情况】部分，避免重复
                     originalScenario = originalScenario.replace(/\n\n【当前情况】[\s\S]*$/, '');
                     
                     const updatedScenario = `${originalScenario}\n\n【当前情况】${situationSummary}`;
                     
                     scenarioInput.value = updatedScenario;
                     
                     // 更新所有角色的场景上下文
                     currentCharacters.forEach(character => {
                         if (character.id !== 'user') {
                             // 为每个角色添加场景更新信息到上下文
                             addMessageToContext(character.id, {
                                 role: 'system',
                                 content: `【场景更新】${situationSummary}`
                             });
                         }
                     });
                     
                     // 添加系统提示
                     addSystemMessage(`📋 场景已自动更新：${situationSummary}`);
                     
                     // 生成并显示总结内容
                     await generateAndDisplaySummary();
                     
                     // 场景自动归纳完成
                     
                     // 场景更新后，增加适当延迟再触发下一轮对话，避免对话过于频繁
                     if (autoConversationEnabled && conversationRounds < maxAutoRounds) {
                         // 场景更新后将继续自动对话
                         setTimeout(() => {
                             if (autoConversationEnabled && conversationRounds < maxAutoRounds) {
                                 checkAndContinueConversation();
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 }
                         }, 800); // 进一步减少延迟，让对话节奏更流畅
                     }
                 }
                
            } catch (error) {
                // 自动归纳当前情况失败
            }
        }
        
        // 生成角色回复
        async function generateCharacterResponses(userMessage) {
            // 获取所有AI角色
            const aiCharacters = currentCharacters.filter(char => char.id !== 'user');
            
            // 如果没有角色，添加系统消息
            if (aiCharacters.length === 0) {
                addSystemMessage('没有角色参与对话，请添加角色');
                return;
            }
            
            // 智能选择需要回复的角色
            function getTriggeredCharacters(characters, userMessage) {
                const triggeredCharacters = [];
                
                // 智能触发开始
                
                // 检查是否有明确提到的角色
                characters.forEach(character => {
                    let isTriggered = false;
                    
                    if (userMessage) {
                        const message = userMessage.toLowerCase();
                        const charName = character.name.toLowerCase();
                        
                        // 检查是否直接提到角色名
                        if (message.includes(charName) || message.includes(character.id)) {
                            isTriggered = true;
                            // 角色被直接提及
                        }
                        
                        // 检查是否有指向性词汇（你、您等）且只有一个角色时
                        if (!isTriggered && characters.length === 1 && 
                            (message.includes('你') || message.includes('您'))) {
                            isTriggered = true;
                            // 单角色指向性触发
                        }
                        
                        // 检查角色相关关键词 - 通用语义匹配算法
                        if (!isTriggered) {
                            // 构建角色的完整文本描述
                            const characterTexts = [
                                character.name || '',
                                character.personality || '',
                                character.background || '',
                                character.description || '',
                                character.style || ''
                            ].filter(text => text.trim().length > 0);
                            
                            // 计算语义相关性得分
                            function calculateRelevanceScore(userMessage, characterTexts) {
                                const messageLower = userMessage.toLowerCase();
                                let totalScore = 0;
                                let matchedTerms = [];
                                
                                characterTexts.forEach(text => {
                                    const textLower = text.toLowerCase();
                                    
                                    // 1. 直接词汇匹配（高权重）
                                    const words = textLower.split(/[，,、\s\u4e00-\u9fff]+/).filter(w => w.length > 0);
                                    words.forEach(word => {
                                        if (word.length >= 2 && messageLower.includes(word)) {
                                            totalScore += word.length * 2; // 词长度越长权重越高
                                            matchedTerms.push(word);
                                        }
                                    });
                                    
                                    // 2. 字符级别匹配（中权重）
                                    for (let i = 0; i < textLower.length - 1; i++) {
                                        const char2 = textLower.substr(i, 2);
                                        if (messageLower.includes(char2)) {
                                            totalScore += 1;
                                        }
                                    }
                                    
                                    // 3. 单字匹配（低权重）
                                    for (let char of textLower) {
                                        if (messageLower.includes(char) && /[\u4e00-\u9fff]/.test(char)) {
                                            totalScore += 0.3;
                                        }
                                    }
                                });
                                
                                return { score: totalScore, matchedTerms };
                            }
                            
                            const relevance = calculateRelevanceScore(message, characterTexts);
                            
                            // 动态阈值：根据消息长度和角色信息丰富度调整
                            const messageLength = message.length;
                            const characterInfoLength = characterTexts.join('').length;
                            const baseThreshold = Math.max(3, Math.min(messageLength * 0.3, characterInfoLength * 0.1));
                            
                            if (relevance.score >= baseThreshold && relevance.matchedTerms.length > 0) {
                                isTriggered = true;
                                // 语义匹配触发
                            }
                        }
                    }
                    
                    if (isTriggered) {
                        triggeredCharacters.push(character);
                    }
                });
                
                // 如果没有明确指向的角色，让多个角色参与对话
                if (triggeredCharacters.length === 0) {
                    // 按角色加入场景的时间排序（最早加入的优先）
                    const sortedByTime = [...characters].sort((a, b) => {
                        const timeA = roleContexts[a.id]?.lastActive || new Date(0);
                        const timeB = roleContexts[b.id]?.lastActive || new Date(0);
                        return timeA - timeB;
                    });
                    
                    // 根据角色数量决定回复的角色数
                    let responseCount;
                    if (sortedByTime.length <= 2) {
                        responseCount = sortedByTime.length; // 2个或以下全部回复
                    } else if (sortedByTime.length <= 4) {
                        responseCount = Math.floor(Math.random() * 2) + 2; // 2-3个角色回复
                    } else {
                        responseCount = Math.floor(Math.random() * 3) + 2; // 2-4个角色回复
                    }
                    
                    // 随机选择角色，但优先选择较早加入的角色
                    const selectedCharacters = [];
                    const availableCharacters = [...sortedByTime];
                    
                    for (let i = 0; i < Math.min(responseCount, availableCharacters.length); i++) {
                        let randomIndex;
                        if (i < 2) {
                            // 前两个优先从前半部分选择
                            randomIndex = Math.floor(Math.random() * Math.min(3, availableCharacters.length));
                        } else {
                            // 后续随机选择
                            randomIndex = Math.floor(Math.random() * availableCharacters.length);
                        }
                        
                        const selectedCharacter = availableCharacters.splice(randomIndex, 1)[0];
                        selectedCharacters.push(selectedCharacter);
                    }
                    
                    triggeredCharacters.push(...selectedCharacters);
                    // 群聊模式触发角色
                }
                
                // 最终触发的角色
                return triggeredCharacters;
            }
            
            // 获取需要回复的角色
            const triggeredCharacters = getTriggeredCharacters(aiCharacters, userMessage);
            
            // 存储所有角色的回复，用于批量更新上下文
            const allResponses = [];
            
            // 为触发的角色生成回复
            for (const character of triggeredCharacters) {
                try {
                        // 添加思考中的消息
                        const messageId = `thinking-${character.id}-${Date.now()}`;
                        const thinkingHtml = `
                            <div id="${messageId}" class="chat-message ai-message thinking">
                                <div class="chat-avatar" style="${getAvatarStyle(`ai-${character.id}`)}">${character.name.charAt(0)}</div>
                                <div class="chat-content">
                                    <div class="chat-header">
                                        <span class="chat-name">${character.name}</span>
                                        <span class="chat-role">${selectedModelForCharacter[character.id] || character.model || '细腻贴合'}</span>
                                        <span class="chat-time">${new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}</span>
                                    </div>
                                    <div class="chat-bubble">
                                        <div class="chat-text">
                                            <span class="thinking-indicator">思考中<span class="dot-1">.</span><span class="dot-2">.</span><span class="dot-3">.</span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        document.getElementById('chatWrapper').insertAdjacentHTML('beforeend', thinkingHtml);
                        document.getElementById('chatContainer').scrollTop = document.getElementById('chatContainer').scrollHeight;
                        
                        // 调用API获取回复
                        const response = await generateAIResponse(character, userMessage);
                        
                        // 处理响应
                        const processedResponse = await processAIResponse(response, character.id);
                        
                        // 移除思考中的消息
                        const thinkingElement = document.getElementById(messageId);
                        if (thinkingElement) {
                            thinkingElement.remove();
                        }
                        
                        // 添加正式回复
                        addChatMessage(
                            `ai-${character.id}`,
                            character.name,
                            processedResponse,
                            selectedModelForCharacter[character.id] || character.model || '细腻贴合',
                            character.name.charAt(0)
                        );
                        
                        // 将AI回复添加到当前角色的上下文
                        addMessageToContext(character.id, {
                            role: 'assistant',
                            content: processedResponse
                        });
                        
                        // 添加到聊天历史（带重复检查）
                        const messageData = {
                            type: 'message',
                            sender: character.name,
                            content: processedResponse,
                            timestamp: new Date().toISOString(),
                            id: generateMessageId()
                        };
                        addToChatHistory(messageData);
                        
                        // 存储回复信息，稍后批量更新其他角色的上下文
                        allResponses.push({
                            characterId: character.id,
                            characterName: character.name,
                            response: processedResponse
                        });
                    
                } catch (error) {
                    // 获取角色回复出错
                    addSystemMessage(`获取${character.name}的回复出错: ${error.message || '未知错误'}`);
                }
            }
            
            // 批量更新所有角色的上下文，避免角色混淆和重复
            // 只有在所有角色都回复完成后，才将其他角色的回复添加到各自的上下文中
            allResponses.forEach(responseInfo => {
                aiCharacters.forEach(otherCharacter => {
                    if (otherCharacter.id !== responseInfo.characterId) {
                        // 检查是否已经添加过这条消息，避免重复
                        const context = roleContexts[otherCharacter.id];
                        const messageContent = `${responseInfo.characterName}: ${responseInfo.response}`;
                        const isDuplicate = context && context.history.some(msg => 
                            msg.content === messageContent || 
                            msg.content.includes(responseInfo.response)
                        );
                        
                        if (!isDuplicate) {
                            // 添加其他角色的回复到当前角色的上下文中
                            addMessageToContext(otherCharacter.id, {
                                role: 'user', // 使用user角色，表示这是对话中的其他声音
                                content: messageContent
                            });
                        }
                    }
                });
            });
            
            // 在所有角色回复完成后，自动归纳当前情况
            await updateCurrentSituation();
            
            // 检查是否需要继续自动对话
            // 使用统一的调度机制
            scheduleNextConversation(300); // 进一步减少延迟，提高对话连贯性
        }
        
        // 格式化对话和动作，突出台词对话
        function formatDialogueAndActions(content) {
            if (!content || typeof content !== 'string') {
                return content;
            }
            
            // 统一处理所有动作标记格式，确保一致性（去掉圆括号，统一样式）
            // 先处理已有的动作标记（**动作**和*动作*）
            content = content.replace(/\*\*([^*]+?)\*\*/g, '<span class="action-narration">$1</span>');
            content = content.replace(/(?<!\*)\*([^*]+?)\*(?!\*)/g, '<span class="action-narration">$1</span>');
            
            // 处理方括号内的动作描述，统一格式
            content = content.replace(/\[([^\]]+?)\]/g, '<span class="action-narration">$1</span>');
            
            // 改进的圆括号处理逻辑
            // 识别并处理圆括号内的动作描述，但要避免处理数学表达式、引用等
            content = content.replace(/\(([^)]+)\)/g, (match, innerContent) => {
                // 扩展的动作关键词列表
                const actionKeywords = [
                    // 基础动作
                    '走', '跑', '坐', '站', '躺', '蹲', '跪', '弯', '伸', '挥', '拍', '握', '摊', '耸', '抱',
                    '看', '望', '凝视', '瞪', '瞥', '注视', '观察', '打量', '扫视', '环顾', '低头', '抬头',
                    '拿', '放', '抓', '松', '推', '拉', '触摸', '抚摸', '轻抚', '拍打', '敲击',
                    '笑', '哭', '皱眉', '蹙眉', '挑眉', '扬眉', '眯眼', '瞪眼', '翻白眼', '眨眼', '闭眼',
                    '脸红', '面红', '脸色', '面色', '神色', '表情', '面带',
                    '激动', '兴奋', '紧张', '焦虑', '担心', '害怕', '恐惧', '愤怒', '生气', '开心', '高兴', '难过', '伤心',
                    '思考', '沉思', '考虑', '犹豫', '迟疑', '停顿', '暂停', '等待', '观望',
                    '叹气', '深呼吸', '咳嗽', '打哈欠', '清嗓子', '哼', '嘟囔', '嘀咕',
                    '点头', '摇头', '摆手', '跺脚', '伸懒腰', '揉眼睛', '挠头', '整理', '理',
                    // 扩展动作词汇
                    '转', '移', '靠', '贴', '倚', '撑', '托', '捧', '举', '放下', '拿起', '拾起', '捡起',
                    '摸', '碰', '按', '压', '捏', '掐', '搓', '揉', '拧', '扭', '甩', '抖', '颤', '震',
                    '吸', '呼', '喘', '叹', '哼', '嗯', '啊', '哦', '唉', '嘘', '咦', '呀',
                    '红', '白', '青', '黑', '亮', '暗', '湿', '干', '热', '冷', '温', '凉',
                    '快', '慢', '急', '缓', '轻', '重', '大', '小', '高', '低', '深', '浅',
                    '紧', '松', '硬', '软', '粗', '细', '厚', '薄', '长', '短', '宽', '窄',
                    '动', '静', '停', '顿', '歇', '休', '睡', '醒', '起', '倒', '翻', '滚',
                    '说', '讲', '谈', '聊', '问', '答', '回', '应', '叫', '喊', '呼', '唤',
                    '听', '闻', '嗅', '尝', '感', '觉', '知', '晓', '明', '懂', '解', '悟'
                ];
                
                // 检查是否包含动作关键词
                const hasActionKeyword = actionKeywords.some(keyword => innerContent.includes(keyword));
                
                // 更宽松的非动作内容检查
                const isNonAction = /^[\d\s+\-*/=.,，。！？()（）]+$/.test(innerContent) || // 纯数字和符号
                                   /^[a-zA-Z\s]+$/.test(innerContent) || // 纯英文
                                   /^\s*$/.test(innerContent) || // 空白
                                   innerContent.length < 1; // 空内容
                
                // 检查是否是常见的动作描述模式
                const actionPatterns = [
                    /.*地.*/, // "轻轻地", "慢慢地"
                    /.*着.*/, // "看着", "想着"
                    /.*了.*/, // "笑了", "走了"
                    /.*起.*/, // "站起", "拿起"
                    /.*下.*/, // "坐下", "放下"
                    /.*过.*/, // "走过", "看过"
                    /.*向.*/, // "走向", "看向"
                    /.*中.*/, // "思考中", "等待中"
                    /.*ing$/, // 英文进行时
                    /.*ed$/, // 英文过去时
                ];
                
                const hasActionPattern = actionPatterns.some(pattern => pattern.test(innerContent));
                
                // 如果包含动作关键词、动作模式，或者内容看起来像动作描述且不是非动作内容，则标记为动作
                // 统一格式：去掉圆括号，保持一致的视觉效果
                if ((hasActionKeyword || hasActionPattern || 
                     (innerContent.length >= 2 && innerContent.length <= 20 && 
                      !/^[\d\s+\-*/=.,，。！？()（）]+$/.test(innerContent) && 
                      !/^[a-zA-Z\s]+$/.test(innerContent))) && 
                    !isNonAction) {
                    return `<span class="action-narration">${innerContent}</span>`;
                }
                
                // 否则保持原样
                return match;
            });
            
            // 处理完整的动作句子，避免误标注台词
            const actionSentencePatterns = [
                // 移动动作
                /([^。！？""'']*(?:走向|走到|走过去|走近|走开|离开|转身|回头|靠近|远离|后退|前进|跑向|奔向|冲向|踱步|徘徊)[^。！？""'']*[。！？])/g,
                // 身体姿态
                /([^。！？""'']*(?:坐下|站起|躺下|蹲下|跪下|弯腰|伸手|挥手|拍手|握拳|摊手|耸肩|抱臂|交叉双臂|双手抱胸|托腮|撑头)[^。！？""'']*[。！？])/g,
                // 视觉动作
                /([^。！？""'']*(?:看向|望着|凝视|瞪着|瞥了一眼|注视|观察|打量|扫视|环顾|低头|抬头|回望|侧目|斜视|直视|俯视|仰视)[^。！？""'']*[。！？])/g,
                // 手部动作
                /([^。！？""'']*(?:拿起|放下|抓住|松开|推开|拉近|触摸|抚摸|轻抚|拍打|敲击|握住|捏住|按住|摸索|抚摸|轻拍|重击)[^。！？""'']*[。！？])/g,
                // 面部表情
                /([^。！？""'']*(?:笑了笑|苦笑|冷笑|轻笑|大笑|皱眉|蹙眉|挑眉|扬眉|眯眼|瞪眼|翻白眼|眨眼|闭眼|瞪大眼睛|眼神闪烁|目光游移)[^。！？""'']*[。！？])/g,
                // 面色变化
                /([^。！？""'']*(?:脸红|面红|红着脸|涨红了脸|脸色苍白|面色凝重|脸色发青|面无表情|神色复杂|表情严肃|面带微笑)[^。！？""'']*[。！？])/g,
                // 情绪状态
                /([^。！？""'']*(?:激动|兴奋|紧张|焦虑|担心|害怕|恐惧|愤怒|生气|开心|高兴|难过|伤心|惊讶|震惊|困惑|疑惑|无奈|失望)地[^。！？""'']*[。！？])/g,
                // 思维动作
                /([^。！？""'']*(?:思考|沉思|考虑|犹豫|迟疑|停顿|暂停|等待|观望|琢磨|寻思|回忆|回想|凝神|专注)着[^。！？""'']*[。！？])/g,
                // 声音动作
                /([^。！？""'']*(?:叹气|深呼吸|咳嗽|打哈欠|清嗓子|哼了一声|轻哼|嘟囔|嘀咕|窃窃私语|低声说道|大声喊道)[^。！？""'']*[。！？])/g,
                // 其他常见动作
                /([^。！？""'']*(?:点点头|摇摇头|摆摆手|拍拍手|跺跺脚|伸了个懒腰|揉了揉眼睛|挠了挠头|整理衣服|理了理头发)[^。！？""'']*[。！？])/g
            ];
            
            // 避免重复处理已经被标记的内容
            const tempMarker = '___TEMP_ACTION_MARKER___';
            content = content.replace(/<span class="action-narration">/g, tempMarker + '<span class="action-narration">');
            
            actionSentencePatterns.forEach(pattern => {
                content = content.replace(pattern, (match) => {
                    // 检查是否已经在action-narration标签内或引号内
                    if (content.indexOf(tempMarker) !== -1 && 
                        content.indexOf(match) > content.lastIndexOf(tempMarker) && 
                        content.indexOf(match) < content.indexOf('</span>', content.lastIndexOf(tempMarker))) {
                        return match; // 已经在标签内，不再处理
                    }
                    // 检查是否在引号内（避免标注台词）
                    const beforeMatch = content.substring(0, content.indexOf(match));
                    const quoteCount = (beforeMatch.match(/[""'']/g) || []).length;
                    if (quoteCount % 2 === 1) {
                        return match; // 在引号内，不处理
                    }
                    const punctuation = match.match(/[。！？]$/)?.[0] || '';
                    const actionText = match.replace(/[。！？]$/, '');
                    return `<span class="action-narration">${actionText}</span>${punctuation}`;
                });
            });
            
            // 恢复临时标记
            content = content.replace(new RegExp(tempMarker, 'g'), '');
            
            return content;
        }
        
        // 调用API获取AI回复
        async function generateAIResponse(character, userMessage) {
            // 获取下一个API密钥
            const apiKey = getNextApiKey();
            
            // 获取角色上下文
            const context = roleContexts[character.id] || initRoleContext(character.id, character.name);
            
            // 构建完整的系统提示词
            // 角色信息调试
            
            // 确保所有角色字段都有默认值
            const characterName = character.name || '未命名角色';
            const characterPersonality = character.personality || '独特的个性';
            const characterBackground = character.background || character.description || '一个独特的角色，有自己的个性和特点';
            const characterStyle = character.style || '自然流畅';
            // 优先使用当前场景输入框的值，如果为空则使用角色存储的场景
            const currentScenario = document.getElementById('scenarioInput').value.trim();
            const characterScenario = currentScenario || character.scenario || '一个轻松愉快的日常环境中，你可以自由地与他人交流互动，展现你的个性和特点';
            
            // 获取其他参与对话的角色信息
            const otherCharacters = currentCharacters.filter(char => char.id !== 'user' && char.id !== character.id);
            let otherCharactersInfo = '';
            if (otherCharacters.length > 0) {
                otherCharactersInfo = `\n\n【其他参与角色】\n`;
                otherCharacters.forEach(otherChar => {
                    let charInfo = `- ${otherChar.name}：${otherChar.personality || '独特的个性'}，${otherChar.background || otherChar.description || '一个独特的角色'}`;
                    if (otherChar.appearance) {
                        charInfo += `，外貌：${otherChar.appearance}`;
                    }
                    otherCharactersInfo += charInfo + '\n';
                });
                otherCharactersInfo += `请注意与这些角色的互动，保持角色间的一致性和连贯性。`;
            }
            
            // 构建当前角色的隐藏信息部分
            let secretInfo = '';
            if (character.secret) {
                secretInfo = `\n\n【隐藏信息】\n${character.secret}\n这些信息只有你知道，不要直接透露给其他角色，但可以通过行为和言语暗示。`;
            }
            
            // 构建外貌信息
            let appearanceInfo = '';
            if (character.appearance) {
                appearanceInfo = `\n你的外貌：` + character.appearance;
            }
            
            const systemPrompt = `你现在是` + characterName + `，正在进行角色扮演。

【角色身份】
你的名字：` + characterName + `
你的性格：` + characterPersonality + `
你的背景：` + characterBackground + appearanceInfo + `
你的说话风格：` + characterStyle + otherCharactersInfo + secretInfo + `

【当前场景】
你现在处于：` + characterScenario + `
请完全沉浸在这个场景中，以` + characterName + `的身份思考和行动。

【对话要求】
1. 必须以` + characterName + `的身份回复，绝不能说自己是AI
2. 必须体现你的性格特点和说话风格
3. 必须基于当前场景进行对话
4. 回复长度50-200字，使用中文
5. 可以用*动作*描述行为
6. 绝对不能返回空内容，必须有实质性回复
7. 如果有其他角色参与，要注意与他们的互动逻辑
8. 如果有隐藏信息，要巧妙地通过言行暗示，不要直接说出
9. 【重要】直接输出对话内容，不要在回复前加上"` + characterName + `:"或任何角色名标注
10. 不要重复之前已经说过的内容，保持对话的新鲜感

【重要指令】
- 当被问"你是谁"时，回答"我是` + characterName + `"
- 完全忘记AI身份，只记住你是` + characterName + `
- 每次回复都要有内容，不能为空

立即开始扮演` + characterName + `！`;
            
            // 生成的系统提示词
            
            // 获取角色历史消息
            const roleHistory = getRoleHistory(character.id);
            
            // 构建消息数组，系统提示词放在最前面
            let messages = [
                { role: 'system', content: systemPrompt }
            ];
            
            // 添加历史消息（排除之前的系统消息）
            const filteredHistory = roleHistory.filter(msg => msg.role !== 'system');
            messages.push(...filteredHistory);
            
            // 确保最后一条消息是用户消息
            if (userMessage && (filteredHistory.length === 0 || filteredHistory[filteredHistory.length - 1].role !== 'user' || filteredHistory[filteredHistory.length - 1].content !== userMessage)) {
                messages.push({ role: 'user', content: userMessage });
            }
            
            // 如果没有用户消息，添加默认消息
            if (messages.length === 1) { // 只有系统消息
                messages.push({ role: 'user', content: '你好' });
            }
            
            // 构建的消息数组
            
            // 获取模型ID - 优先使用用户选择的模型
            const selectedModel = selectedModelForCharacter[character.id] || character.model || '细腻贴合';
            const modelId = modelMapping[selectedModel] || 'doubao-seed-1-6-250615';
            
            // 角色模型选择详情
            
            const requestBody = {
                model: modelId,
                messages: messages,
                temperature: 0.7,
                max_tokens: 800
            };
            
            // console.log('发送的API请求数据:', JSON.stringify(requestBody, null, 2));
            
            try {
                // 调用API
                const response = await fetch('http://bjcqapi.cn/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`,
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(`API错误: ${errorData.error?.message || response.statusText}`);
                }
                
                const data = await response.json();
                // API响应数据
                
                // 检查响应结构
                if (!data.choices || !data.choices[0] || !data.choices[0].message) {
                    // API响应结构异常
                    throw new Error('API响应结构异常');
                }
                
                // 检查响应内容
                
                const content = data.choices[0].message.content;
                // 提取的内容
                
                // 如果content为null或undefined，尝试其他字段
                if (content === null || content === undefined || content === '') {
                    // content为空，尝试其他字段
                    const alternativeContent = data.choices[0].message.text || 
                                             data.choices[0].text || 
                                             data.choices[0].content;
                    // 备用内容
                    
                    // 如果所有字段都为空，返回角色化的默认回复
                    if (!alternativeContent || alternativeContent.trim() === '') {
                        // 所有内容字段都为空，使用角色化默认回复
                        return `*${character.name}微笑着看向你* 你好！我是${character.name}，很高兴见到你。有什么想聊的吗？`;
                    }
                    
                    return alternativeContent;
                }
                
                return content;
            } catch (error) {
                // API调用失败
                // 根据错误类型返回不同的错误信息
                if (error.message.includes('API响应结构异常')) {
                    return 'API响应格式异常，请稍后重试';
                } else if (error.message.includes('API错误')) {
                    return `API调用错误：${error.message}`;
                } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    return '网络连接失败，请检查网络后重试';
                } else {
                    return `*${character.name}似乎遇到了一些问题，无法回应...*`;
                }
            }
        }
        
        // 获取角色的对话历史
        function getRoleHistory(roleId) {
            return roleContexts[roleId]?.history || [];
        }

        // 绑定发送事件
        const sendButton = document.getElementById('sendButton');
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // --- 角色管理功能 ---
        function addParticipant() {
            openCharacterMarket();
        }

        // 参与者列表交互 - 修复版本
        document.getElementById('participantList').addEventListener('click', (e) => {
            const item = e.target.closest('.participant-item');
            if (!item) return;
            
            const roleId = item.dataset.roleId;
            const roleType = item.dataset.roleType;
            
            // 点击模型切换按钮
            if (e.target.closest('.participant-action.model')) {
                currentEditingCharacterId = roleId;
                openModelSelect();
                return;
            }
            
            // 移除按钮点击 - 只能删除非默认角色
            if (e.target.closest('.participant-action.remove')) {
                if (roleType !== 'default') {
                    // 从当前角色列表中移除
                    currentCharacters = currentCharacters.filter(char => char.id !== roleId);
                    // 从DOM中移除
                    item.remove();
                    // 立即更新角色指示器
                    updateCharacterIndicator();
                }
                return;
            }
            
            // 点击角色项目时的行为（用户角色不响应点击）
            if (roleId !== 'user') {
                // 只为非用户角色添加高亮
                document.querySelectorAll('.participant-item').forEach(el => el.classList.remove('active'));
                item.classList.add('active');
                
                // 确保聊天界面可见
                const emptyState = document.getElementById('emptyState');
                const chatWrapper = document.getElementById('chatWrapper');
                if (emptyState.style.display !== 'none') {
                    emptyState.style.display = 'none';
                    chatWrapper.style.display = 'flex';
                }
                
                // 更新角色指示器
                updateCharacterIndicator();
            }
        });

        // --- 角色市场功能 ---
        let currentCategory = 'all';

        function openCharacterMarket() {
            const modal = document.getElementById('characterMarketModal');
            modal.style.display = 'flex';
            
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
            
            renderCharacters();
        }

        function closeCharacterMarket() {
            const modal = document.getElementById('characterMarketModal');
            modal.classList.remove('show');
            
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        function filterCharacters(category) {
            currentCategory = category;
            
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            renderCharacters();
        }

        function renderCharacters() {
            const grid = document.getElementById('characterGrid');
            grid.innerHTML = '';
            
            const filteredCharacters = currentCategory === 'all' 
                ? presetCharacters 
                : presetCharacters.filter(c => c.category === currentCategory);
            
            filteredCharacters.forEach((character, index) => {
                const card = document.createElement('div');
                card.className = 'character-card';
                card.style.setProperty('--card-index', index);
                if (selectedCharacterIds.has(character.id)) {
                    card.classList.add('selected');
                }
                
                card.innerHTML = `
                    <div class="character-avatar-large">${character.name.charAt(0)}</div>
                    <div class="character-name">${character.name}</div>
                    <div class="character-desc">${character.desc}</div>
                    <div class="character-tags">
                        <span class="character-tag">${character.category}</span>
                    </div>
                `;
                
                card.addEventListener('click', () => {
                    card.classList.toggle('selected');
                    if (selectedCharacterIds.has(character.id)) {
                        selectedCharacterIds.delete(character.id);
                    } else {
                        selectedCharacterIds.add(character.id);
                    }
                });
                
                grid.appendChild(card);
            });
        }

        function selectCharacters() {
            if (selectedCharacterIds.size === 0) {
                alert('请至少选择一个角色');
                return;
            }
            
            // 将选中的角色加入待处理队列
            pendingCharactersForModelSelect = [];
            selectedCharacterIds.forEach(id => {
                const character = presetCharacters.find(c => c.id === id);
                if (character && !currentCharacters.find(c => c.id === character.id)) {
                    pendingCharactersForModelSelect.push(character);
                }
            });
            
            selectedCharacterIds.clear();
            closeCharacterMarket();
            
            // 开始为第一个角色选择模型
            if (pendingCharactersForModelSelect.length > 0) {
                selectModelForNextCharacter();
            }
        }

        // 为下一个待处理的角色选择模型
        function selectModelForNextCharacter() {
            if (pendingCharactersForModelSelect.length === 0) {
                // 所有角色都处理完毕，立即更新指示器
                updateCharacterIndicator();
                addSystemMessage(`已添加 ${currentCharacters.filter(c => c.id !== 'user').length} 个角色到对话中`);
                return;
            }
            
            const character = pendingCharactersForModelSelect.shift();
            currentEditingCharacterId = character.id;
            
            // 先添加角色到列表，使用默认模型
            addCharacterToList(character);
            
            // 打开模型选择窗口
            openModelSelectForCharacter(character);
        }

        // 添加角色到参与列表
        function addCharacterToList(character) {
            // 确保角色ID是字符串类型
            const charId = String(character.id);
            
            // 检查角色是否已存在
            if (currentCharacters.find(c => String(c.id) === charId)) {
                return;
            }
            
            const participantList = document.getElementById('participantList');
            const item = document.createElement('div');
            item.className = 'participant-item';
            item.dataset.roleId = charId;
            item.dataset.roleType = 'custom';
            
            item.innerHTML = `
                <div class="participant-avatar">${character.name.charAt(0)}</div>
                <div class="participant-info">
                    <div class="participant-name">${character.name}</div>
                    <div class="participant-model">${character.model || '细腻贴合'}</div>
                </div>
                <div class="participant-actions">
                    <button class="participant-action model" title="切换模型">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </button>
                    <button class="participant-action remove" title="移除">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                        </svg>
                    </button>
                </div>
            `;
            
            participantList.appendChild(item);
            
            // 创建角色对象副本，确保ID是字符串
            const charCopy = {...character, id: charId};
            currentCharacters.push(charCopy);
            selectedModelForCharacter[charId] = character.model || '细腻贴合';
            
            // 立即更新角色指示器
            updateCharacterIndicator();
        }

        // --- 创建角色功能 ---
        function openCreateCharacterModal() {
            const modal = document.getElementById('createCharacterModal');
            modal.style.display = 'flex';
            
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
        }

        function closeCreateCharacterModal() {
            const modal = document.getElementById('createCharacterModal');
            modal.classList.remove('show');
            
            setTimeout(() => {
                modal.style.display = 'none';
                document.getElementById('characterForm').reset();
                // 确保新添加的字段也被清空
                document.getElementById('newCharAppearance').value = '';
                document.getElementById('newCharSecret').value = '';
            }, 300);
        }

        // 角色表单提交
        document.getElementById('characterForm').addEventListener('submit', (e) => {
            e.preventDefault();
            
            const character = {
                id: generateId(),
                name: document.getElementById('newCharName').value,
                personality: document.getElementById('newCharPersonality').value,
                background: document.getElementById('newCharBackground').value,
                description: document.getElementById('newCharBackground').value, // 使用背景作为描述
                style: document.getElementById('newCharStyle').value,
                appearance: document.getElementById('newCharAppearance').value, // 外貌描述
                secret: document.getElementById('newCharSecret').value, // 隐藏信息
                scenario: document.getElementById('scenarioInput').value || '一个轻松愉快的日常环境中，你可以自由地与他人交流互动，展现你的个性和特点', // 使用用户设置的场景
                role: '自定义角色',
                model: '细腻贴合'
            };
            
            closeCreateCharacterModal();
            
            // 添加角色到列表
            addCharacterToList(character);
            
            // 打开模型选择窗口
            currentEditingCharacterId = character.id;
            openModelSelectForCharacter(character);
        });

        // AI随机生成角色设定
        async function autoGenerateCharacter() {
            // 显示加载状态
            const nameField = document.getElementById('newCharName');
            const personalityField = document.getElementById('newCharPersonality');
            const backgroundField = document.getElementById('newCharBackground');
            const styleField = document.getElementById('newCharStyle');
            const appearanceField = document.getElementById('newCharAppearance');
            const secretField = document.getElementById('newCharSecret');
            
            nameField.value = '正在生成角色...';
            personalityField.value = '正在生成性格特征...';
            backgroundField.value = '正在生成背景故事...';
            styleField.value = '正在生成对话风格...';
            appearanceField.value = '正在生成外貌描述...';
            secretField.value = '正在生成秘密信息...';
            
            try {
                const characterData = await generateCharacterWithAI();
                
                nameField.value = characterData.name;
                personalityField.value = characterData.personality;
                backgroundField.value = characterData.background;
                styleField.value = characterData.style;
                appearanceField.value = characterData.appearance;
                secretField.value = characterData.secret;
            } catch (error) {
                console.error('AI生成角色失败:', error);
                showCustomAlert('AI生成角色失败，请稍后重试', 'error');
                
                // 回退到默认值
                nameField.value = '';
                personalityField.value = '';
                backgroundField.value = '';
                styleField.value = '';
                appearanceField.value = '';
                secretField.value = '';
            }
        }
        
        // 调用AI生成角色
        async function generateCharacterWithAI() {
            const prompt = `你是一个专业的剧本杀游戏设计师，请为剧本杀游戏创建一个独特且有趣的角色。剧本杀是一种推理解谜类角色扮演游戏，每个角色都有自己的身份、动机和秘密。

要求：
1. 角色名称：符合剧本杀风格，可以是真名或代号
2. 性格特征：详细描述角色的性格特点，要有利于推理和互动
3. 背景故事：包含角色的职业、经历，以及与案件相关的背景
4. 对话风格：角色说话的特点，要体现其身份和性格
5. 外貌描述：生动的外观特征，有助于其他玩家识别
6. 秘密信息：角色隐藏的关键秘密，可能与案件真相相关，包括动机、线索或不在场证明

角色设定要求：
- 每个角色都应该有明确的动机和目标
- 秘密信息要有推理价值，不能过于简单
- 角色之间要有潜在的关系和冲突
- 适合悬疑推理类剧情发展

请按照以下JSON格式返回：
{
  "name": "角色名称",
  "personality": "性格特征描述",
  "background": "背景故事（包含职业和相关经历）",
  "style": "对话风格描述",
  "appearance": "外貌描述",
  "secret": "关键秘密信息（与案件或推理相关）"
}`;
            
            const response = await callAIAPI(prompt, 'doubao-seed-1-6-flash-250615');
            
            try {
                // 尝试解析JSON响应
                const jsonMatch = response.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    return JSON.parse(jsonMatch[0]);
                } else {
                    throw new Error('AI返回格式不正确');
                }
            } catch (parseError) {
                // 如果JSON解析失败，尝试从文本中提取信息
                return parseCharacterFromText(response);
            }
        }
        
        // 从文本中解析角色信息
        function parseCharacterFromText(text) {
            const lines = text.split('\n');
            const character = {
                name: '神秘角色',
                personality: '性格复杂多变',
                background: '来历不明',
                style: '说话神秘',
                appearance: '外貌普通',
                secret: '隐藏着不为人知的秘密'
            };
            
            // 简单的文本解析逻辑
            for (let line of lines) {
                if (line.includes('名称') || line.includes('姓名')) {
                    const match = line.match(/[:：]\s*(.+)/);
                    if (match) character.name = match[1].trim();
                }
                if (line.includes('性格') || line.includes('特征')) {
                    const match = line.match(/[:：]\s*(.+)/);
                    if (match) character.personality = match[1].trim();
                }
                if (line.includes('背景') || line.includes('故事')) {
                    const match = line.match(/[:：]\s*(.+)/);
                    if (match) character.background = match[1].trim();
                }
                if (line.includes('风格') || line.includes('说话')) {
                    const match = line.match(/[:：]\s*(.+)/);
                    if (match) character.style = match[1].trim();
                }
                if (line.includes('外貌') || line.includes('外观')) {
                    const match = line.match(/[:：]\s*(.+)/);
                    if (match) character.appearance = match[1].trim();
                }
                if (line.includes('秘密') || line.includes('隐藏')) {
                    const match = line.match(/[:：]\s*(.+)/);
                    if (match) character.secret = match[1].trim();
                }
            }
            
            return character;
        }

        // AI随机生成场景
        async function autoGenerateScenario() {
            const scenarioInput = document.getElementById('scenarioInput');
            scenarioInput.value = '正在生成场景背景...';
            
            try {
                const scenario = await generateScenarioWithAI();
                scenarioInput.value = scenario;
            } catch (error) {
                console.error('AI生成场景失败:', error);
                showCustomAlert('AI生成场景失败，请稍后重试', 'error');
                scenarioInput.value = '';
            }
        }
        
        // 调用AI生成场景
        async function generateScenarioWithAI() {
            const prompt = `你是一个专业的剧本杀游戏设计师，请为剧本杀游戏创建一个引人入胜的场景背景。剧本杀是一种推理解谜类游戏，场景设定要为推理和互动提供充分的空间。

要求：
1. 场景要有悬疑推理元素，适合发生案件或谜题
2. 包含明确的环境描述（时间、地点、氛围、重要物品）
3. 设置一个核心事件或谜题作为推理焦点
4. 为角色之间的秘密交流和线索搜集提供机会
5. 长度控制在150-250字之间
6. 营造紧张悬疑的氛围

场景特点：
- 要有封闭或半封闭的环境（便于推理）
- 包含可能的案发现场或关键地点
- 有利于角色隐藏秘密和交换信息
- 适合进行证据搜集和推理讨论

请直接返回场景描述，不需要额外的格式或标题。

示例风格：
- 豪华别墅：暴风雨夜的山顶别墅里，几位受邀参加生日宴会的客人发现主人神秘失踪，别墅的电话线被切断，所有人都被困在这里...
- 古董拍卖会：高档拍卖行的贵宾室内，一件价值连城的古董在众目睽睽下消失，在场的收藏家、专家和工作人员都有嫌疑...
- 豪华游轮：航行在大海中的豪华游轮上，船长的贵重物品失窃，嫌疑人就在船上的乘客和船员中...

请创造一个全新的、适合剧本杀的悬疑场景：`;
            
            const response = await callAIAPI(prompt, 'doubao-seed-1-6-flash-250615');
            return response.trim();
        }

        // --- 模型选择功能 ---
        function openModelSelect() {
            const modal = document.getElementById('modelSelectModal');
            modal.style.display = 'flex';
            
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
            
            renderModels();
        }

        function openModelSelectForCharacter(character) {
            const modal = document.getElementById('modelSelectModal');
            modal.style.display = 'flex';
            
            // 更新标题显示当前角色名
            const modalTitle = modal.querySelector('.modal-title');
            modalTitle.textContent = `为 ${character.name} 选择AI模型`;
            
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
            
            renderModels();
        }

        function closeModelSelect() {
            const modal = document.getElementById('modelSelectModal');
            modal.classList.remove('show');
            
            setTimeout(() => {
                modal.style.display = 'none';
                // 恢复默认标题
                const modalTitle = modal.querySelector('.modal-title');
                modalTitle.textContent = '选择AI模型';
                
                // 处理下一个待选择模型的角色
                selectModelForNextCharacter();
            }, 300);
        }

        function renderModels() {
            const grid = document.getElementById('modelGrid');
            grid.innerHTML = '';
            
            availableModels.forEach((model, index) => {
                const card = document.createElement('div');
                card.className = 'model-card';
                card.style.setProperty('--card-index', index);
                
                // 检查是否是当前选中的模型
                const currentModel = selectedModelForCharacter[currentEditingCharacterId] || '细腻贴合';
                if (model.name === currentModel) {
                    card.classList.add('selected');
                }
                
                card.innerHTML = `
                    <div class="model-icon">${model.icon}</div>
                    <div class="model-name">${model.name}</div>
                    <div class="model-desc">${model.desc}</div>
                `;
                
                card.addEventListener('click', () => {
                    document.querySelectorAll('.model-card').forEach(c => c.classList.remove('selected'));
                    card.classList.add('selected');
                    card.dataset.modelId = model.id;
                    card.dataset.modelName = model.name;
                });
                
                grid.appendChild(card);
            });
        }

        function confirmModelSelect() {
            const selectedCard = document.querySelector('.model-card.selected');
            if (!selectedCard) {
                alert('请选择一个模型');
                return;
            }
            
            const modelName = selectedCard.dataset.modelName;
            
            // 更新角色的模型
            selectedModelForCharacter[currentEditingCharacterId] = modelName;
            
            // 同时更新角色对象的model属性
            const character = currentCharacters.find(char => char.id === currentEditingCharacterId);
            if (character) {
                character.model = modelName;
            }
            
            // 更新显示
            const participantItem = document.querySelector(`[data-role-id="${currentEditingCharacterId}"]`);
            if (participantItem) {
                const modelDiv = participantItem.querySelector('.participant-model');
                if (modelDiv) {
                    modelDiv.textContent = modelName;
                }
            }
            
            // 立即更新角色指示器
            updateCharacterIndicator();
            closeModelSelect();
        }

        // --- 其他功能 ---
        async function clearChat() {
            const confirmed = await confirm('确定要清空所有对话记录吗？');
            if (confirmed) {
                // 清空聊天内容
                const chatWrapper = document.getElementById('chatWrapper');
                chatWrapper.innerHTML = '';
                
                // 恢复空状态（如果没有角色的话）
                if (currentCharacters.filter(c => c.id !== 'user').length === 0) {
                    document.getElementById('emptyState').style.display = 'flex';
                    document.getElementById('chatWrapper').style.display = 'none';
                }
                
                // 清空所有相关数据
                chatHistory = [];
                
                // 清空所有角色的上下文历史
                Object.keys(roleContexts).forEach(roleId => {
                    if (roleContexts[roleId]) {
                        roleContexts[roleId].history = [];
                    }
                });
                
                // 停止自动对话
                autoConversationEnabled = false;
                conversationRounds = 0;
                
                // 更新角色指示器
                updateCharacterIndicator();
                
                // 添加系统提示
                addSystemMessage('对话记录已清空');
            }
        }

        function exportChat() {
            if (chatHistory.length === 0) {
                alert('当前没有对话内容可导出');
                return;
            }
            
            // 生成txt格式的对话记录
            let chatText = '=== 对话记录导出 ===\n\n';
            chatText += `导出时间：${new Date().toLocaleString('zh-CN')}\n`;
            chatText += `对话轮数：${chatHistory.length}\n`;
            chatText += `参与角色：${currentCharacters.filter(c => c.id !== 'user').map(c => c.name).join(', ') || '无'}\n\n`;
            chatText += '=== 对话内容 ===\n\n';
            
            chatHistory.forEach((msg, index) => {
                const roleName = msg.role === 'user' ? '用户' : (msg.character || 'AI');
                chatText += `【${roleName}】\n${msg.content}\n\n`;
            });
            
            chatText += '=== 导出结束 ===';
            
            // 创建并下载txt文件
            const dataBlob = new Blob([chatText], {type: 'text/plain;charset=utf-8'});
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `对话记录_${new Date().toISOString().slice(0, 10)}_${new Date().toTimeString().slice(0, 8).replace(/:/g, '-')}.txt`;
            link.click();
            
            showSuccess('对话记录导出成功！');
        }

        function saveScenario() {
            const scenarioElement = document.getElementById('scenarioInput');
            if (!scenarioElement) {
                alert('场景输入框未找到，请刷新页面重试');
                return;
            }
            
            const scenario = scenarioElement.value.trim();
            if (!scenario) {
                alert('请先在"场景背景"中输入场景内容');
                // 聚焦到场景输入框
                scenarioElement.focus();
                return;
            }
            
            try {
                // 保存场景到localStorage
                const scenarioData = {
                    id: Date.now(),
                    title: scenario.substring(0, 50) + (scenario.length > 50 ? '...' : ''),
                    content: scenario,
                    characters: currentCharacters.map(char => char.name).join(', '),
                    timestamp: new Date().toISOString(),
                    conversationCount: chatHistory.length,
                    chatHistory: JSON.parse(JSON.stringify(chatHistory)), // 深拷贝聊天记录
                    savedCharacters: JSON.parse(JSON.stringify(currentCharacters)) // 保存角色信息
                };
                
                let savedScenarios = JSON.parse(localStorage.getItem('savedScenarios') || '[]');
                savedScenarios.unshift(scenarioData);
                localStorage.setItem('savedScenarios', JSON.stringify(savedScenarios));
                
                showSuccess('场景保存成功！\n\n场景标题：' + scenarioData.title + '\n保存时间：' + new Date().toLocaleString('zh-CN'));
            } catch (error) {
                // 保存场景时出错
                showError('保存场景失败，请重试');
            }
        }

        function triggerFileUpload() {
            document.getElementById('fileInput').click();
        }

        // 点击弹窗外部关闭
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    if (modal.id === 'characterMarketModal') {
                        closeCharacterMarket();
                    } else if (modal.id === 'createCharacterModal') {
                        closeCreateCharacterModal();
                    } else if (modal.id === 'modelSelectModal') {
                        // 如果还有待处理的角色，继续处理
                        if (pendingCharactersForModelSelect.length > 0) {
                            confirmModelSelect();
                        } else {
                            closeModelSelect();
                        }
                    }
                }
            });
        });

        // 初始化
        window.addEventListener('load', () => {
            // 添加默认的用户到当前角色列表
            currentCharacters.push(
                { id: 'user', name: '用户', role: '玩家角色' }
            );
            
            // 初始化时也要确保空状态或角色指示器显示正确
            const emptyState = document.getElementById('emptyState');
            const chatWrapper = document.getElementById('chatWrapper');
            
            // 总是显示chatWrapper（包含角色指示器）
            emptyState.style.display = 'none';
            chatWrapper.style.display = 'flex';
            
            // 更新角色指示器
            updateCharacterIndicator();
        });
        // 历史记录弹窗相关函数
        function openHistoryDialog() {
            const modal = document.getElementById('historyModal');
            modal.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
            loadScenarioHistory();
        }

        function closeHistoryDialog() {
            const modal = document.getElementById('historyModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        function loadScenarioHistory() {
            const savedScenarios = JSON.parse(localStorage.getItem('savedScenarios') || '[]');
            const historyList = document.getElementById('historyList');
            const totalScenarios = document.getElementById('totalScenarios');
            const totalConversations = document.getElementById('totalConversations');
            const activeCharacters = document.getElementById('activeCharacters');

            // 更新统计数据
            totalScenarios.textContent = savedScenarios.length;
            const totalConvCount = savedScenarios.reduce((sum, scenario) => sum + scenario.conversationCount, 0);
            totalConversations.textContent = totalConvCount;
            const uniqueCharacters = new Set();
            savedScenarios.forEach(scenario => {
                if (scenario.characters) {
                    scenario.characters.split(', ').forEach(char => uniqueCharacters.add(char.trim()));
                }
            });
            activeCharacters.textContent = uniqueCharacters.size;

            // 生成历史记录列表
            if (savedScenarios.length === 0) {
                historyList.innerHTML = '<div style="text-align: center; padding: 40px; color: var(--text-light);">暂无保存的场景</div>';
                return;
            }

            historyList.innerHTML = savedScenarios.map(scenario => {
                const date = new Date(scenario.timestamp);
                const formattedDate = date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
                
                const hasChatHistory = scenario.chatHistory && scenario.chatHistory.length > 0;
                const chatIndicator = hasChatHistory ? 
                    '<span style="color: #28a745; font-weight: bold; font-size: 12px;">📝 含对话</span>' : 
                    '<span style="color: #6c757d; font-size: 12px;">📄 仅场景</span>';
                
                return `
                    <div class="history-item" onclick="viewScenarioDetail('${scenario.id}')" style="cursor: pointer;">
                        <div class="history-title">${scenario.title}</div>
                        <div class="history-content">${scenario.content}</div>
                        <div class="history-meta">
                            <span>角色: ${scenario.characters || '无'} | 对话数: ${scenario.conversationCount}</span>
                            <span style="margin-left: 10px;">${chatIndicator}</span>
                            <div class="history-actions" onclick="event.stopPropagation();">
                                <button class="history-action-btn" onclick="loadScenario('${scenario.id}')">快速加载</button>
                                <button class="history-action-btn" onclick="deleteScenario('${scenario.id}')">删除</button>
                            </div>
                        </div>
                        <div style="font-size: 11px; color: var(--text-light); margin-top: 4px;">${formattedDate}</div>
                        <div style="font-size: 11px; color: var(--primary-color); margin-top: 2px;">点击查看详情和对话记录</div>
                    </div>
                `;
            }).join('');
        }

        function loadScenario(scenarioId) {
            const savedScenarios = JSON.parse(localStorage.getItem('savedScenarios') || '[]');
            const scenario = savedScenarios.find(s => s.id == scenarioId);
            if (scenario) {
                document.getElementById('scenarioInput').value = scenario.content;
                closeHistoryDialog();
                alert('场景已加载！');
            }
        }

        async function deleteScenario(scenarioId) {
            const confirmed = await confirm('确定要删除这个场景吗？');
            if (confirmed) {
                let savedScenarios = JSON.parse(localStorage.getItem('savedScenarios') || '[]');
                savedScenarios = savedScenarios.filter(s => s.id != scenarioId);
                localStorage.setItem('savedScenarios', JSON.stringify(savedScenarios));
                loadScenarioHistory();
            }
        }

        async function clearScenarioHistory() {
            const confirmed = await confirm('确定要清空所有历史记录吗？此操作不可恢复！');
            if (confirmed) {
                localStorage.removeItem('savedScenarios');
                loadScenarioHistory();
            }
        }

        // 当前查看的场景ID
        let currentDetailScenarioId = null;

        // 查看场景详情
        function viewScenarioDetail(scenarioId) {
            const savedScenarios = JSON.parse(localStorage.getItem('savedScenarios') || '[]');
            const scenario = savedScenarios.find(s => s.id == scenarioId);
            if (!scenario) {
                alert('场景不存在');
                return;
            }

            currentDetailScenarioId = scenarioId;
            
            // 填充详情数据
            document.getElementById('detailTitle').value = scenario.title;
            document.getElementById('detailContent').value = scenario.content;
            document.getElementById('detailCharacters').value = scenario.characters || '无';
            document.getElementById('detailTimestamp').value = new Date(scenario.timestamp).toLocaleString('zh-CN');
            document.getElementById('detailConversationCount').value = scenario.conversationCount;
            
            // 显示对话记录
            const chatHistoryDiv = document.getElementById('detailChatHistory');
            if (scenario.chatHistory && scenario.chatHistory.length > 0) {
                let chatHtml = '';
                scenario.chatHistory.forEach((msg, index) => {
                    const roleClass = msg.role === 'user' ? 'user-message' : 'ai-message';
                    const roleName = msg.role === 'user' ? '用户' : (msg.character || 'AI');
                    chatHtml += `
                        <div style="margin-bottom: 10px; padding: 8px; border-left: 3px solid ${msg.role === 'user' ? 'var(--primary-color)' : 'var(--accent-color)'}; background: var(--bg-secondary); border-radius: 4px;">
                            <div style="font-weight: bold; color: ${msg.role === 'user' ? 'var(--primary-color)' : 'var(--accent-color)'}; margin-bottom: 3px;">${roleName}</div>
                            <div style="color: var(--text-primary);">${msg.content}</div>
                        </div>
                    `;
                });
                chatHistoryDiv.innerHTML = chatHtml;
            } else {
                chatHistoryDiv.innerHTML = '<div style="color: var(--text-light); text-align: center; padding: 20px;">暂无对话记录</div>';
            }
            
            // 显示详情弹窗
            const modal = document.getElementById('scenarioDetailModal');
            modal.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
        }

        // 关闭场景详情
        function closeScenarioDetail() {
            const modal = document.getElementById('scenarioDetailModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
                currentDetailScenarioId = null;
            }, 300);
        }

        // 从详情页加载场景
        function loadScenarioFromDetail() {
            if (!currentDetailScenarioId) return;
            
            const savedScenarios = JSON.parse(localStorage.getItem('savedScenarios') || '[]');
            const scenario = savedScenarios.find(s => s.id == currentDetailScenarioId);
            if (scenario) {
                document.getElementById('scenarioInput').value = scenario.content;
                closeScenarioDetail();
                closeHistoryDialog();
                alert('场景已加载到编辑器！');
            }
        }

        // 从详情页加载场景和对话
        function loadScenarioWithChat() {
            if (!currentDetailScenarioId) return;
            
            const savedScenarios = JSON.parse(localStorage.getItem('savedScenarios') || '[]');
            const scenario = savedScenarios.find(s => s.id == currentDetailScenarioId);
            if (scenario) {
                // 加载场景内容
                document.getElementById('scenarioInput').value = scenario.content;
                
                // 恢复对话记录
                if (scenario.chatHistory && scenario.chatHistory.length > 0) {
                    chatHistory = JSON.parse(JSON.stringify(scenario.chatHistory));
                    updateChatDisplay();
                }
                
                // 恢复角色信息
                if (scenario.savedCharacters && scenario.savedCharacters.length > 0) {
                    currentCharacters = JSON.parse(JSON.stringify(scenario.savedCharacters));
                }
                
                closeScenarioDetail();
                closeHistoryDialog();
                alert('场景和对话记录已完整加载！');
            }
        }

        // 更新场景标题
        function updateScenarioTitle() {
            if (!currentDetailScenarioId) return;
            
            const newTitle = document.getElementById('detailTitle').value.trim();
            if (!newTitle) {
                alert('请输入场景标题');
                return;
            }
            
            let savedScenarios = JSON.parse(localStorage.getItem('savedScenarios') || '[]');
            const scenarioIndex = savedScenarios.findIndex(s => s.id == currentDetailScenarioId);
            if (scenarioIndex !== -1) {
                savedScenarios[scenarioIndex].title = newTitle;
                localStorage.setItem('savedScenarios', JSON.stringify(savedScenarios));
                showSuccess('标题更新成功！');
                loadScenarioHistory(); // 刷新历史记录列表
            }
        }

        // 导出场景详情
        function exportScenarioDetail() {
            if (!currentDetailScenarioId) return;
            
            const savedScenarios = JSON.parse(localStorage.getItem('savedScenarios') || '[]');
            const scenario = savedScenarios.find(s => s.id == currentDetailScenarioId);
            if (!scenario) return;
            
            // 生成txt格式的场景记录
            let scenarioText = '=== 场景记录导出 ===\n\n';
            scenarioText += `场景标题：${scenario.title}\n`;
            scenarioText += `创建时间：${scenario.timestamp}\n`;
            scenarioText += `对话轮数：${scenario.conversationCount || 0}\n`;
            scenarioText += `参与角色：${scenario.characters || '无'}\n\n`;
            scenarioText += '=== 场景内容 ===\n\n';
            scenarioText += scenario.content + '\n\n';
            
            // 如果有保存的对话记录，也一并导出
            if (scenario.chatHistory && scenario.chatHistory.length > 0) {
                scenarioText += '=== 对话记录 ===\n\n';
                scenario.chatHistory.forEach((msg, index) => {
                    const roleName = msg.role === 'user' ? '用户' : (msg.character || 'AI');
                    scenarioText += `【${roleName}】\n${msg.content}\n\n`;
                });
            }
            
            scenarioText += '=== 导出结束 ===';
            
            const dataBlob = new Blob([scenarioText], {type: 'text/plain;charset=utf-8'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `场景_${scenario.title}_${new Date().toISOString().slice(0, 10)}.txt`;
            link.click();
            
            showSuccess('场景导出成功！');
        }

        // 从详情页删除场景
        async function deleteScenarioFromDetail() {
            if (!currentDetailScenarioId) return;
            
            const confirmed = await confirm('确定要删除这个场景吗？');
            if (confirmed) {
                let savedScenarios = JSON.parse(localStorage.getItem('savedScenarios') || '[]');
                savedScenarios = savedScenarios.filter(s => s.id != currentDetailScenarioId);
                localStorage.setItem('savedScenarios', JSON.stringify(savedScenarios));
                
                closeScenarioDetail();
                loadScenarioHistory(); // 刷新历史记录列表
                alert('场景已删除！');
            }
        }

        // --- 附件管理功能 ---
        
        // 人设卡管理
        function openCharacterManager() {
            showAttachmentManager('人设卡', 'character', [
                { id: 1, name: '李明', content: '男主角，25岁，程序员，性格内向但善良' },
                { id: 2, name: '张小雨', content: '女主角，23岁，设计师，活泼开朗，喜欢旅行' },
                { id: 3, name: '王老师', content: '配角，50岁，大学教授，智慧长者' }
            ]);
        }
        
        // 章节管理
        function openChapterManager() {
            showAttachmentManager('章节', 'chapter', [
                { id: 1, name: '第一章：相遇', content: '男女主角在咖啡厅的偶然相遇' },
                { id: 2, name: '第二章：误会', content: '因为一个小误会产生的矛盾' },
                { id: 3, name: '第三章：和解', content: '通过朋友的帮助重新认识彼此' }
            ]);
        }
        
        // 通用附件管理器
        function showAttachmentManager(title, type, items) {
            // 创建弹窗HTML
            const modalHTML = `
                <div class="attachment-manager-overlay" id="attachmentManagerOverlay">
                    <div class="attachment-manager-modal" id="attachmentManagerModal">
                        <div class="attachment-manager-header">
                            <h3 class="attachment-manager-title">${title}管理</h3>
                            <button class="attachment-manager-close" onclick="closeAttachmentManager()">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                                </svg>
                            </button>
                        </div>
                        <div class="attachment-manager-body">
                            <div class="attachment-manager-toolbar">
                                <button class="btn-primary" onclick="addNewAttachment('${type}')">
                                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px; margin-right: 6px;">
                                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                                    </svg>
                                    新建${title}
                                </button>
                                ${type !== 'chapter' ? `<button class="btn-secondary" onclick="toggleAttachmentMultiSelect('${type}')" id="multiSelectBtn">
                                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px; margin-right: 6px;">
                                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14l-5-5 1.41-1.41L14 14.17l7.59-7.59L23 8l-9 9z"/>
                                    </svg>
                                    多选
                                </button>` : ''}
                                <button class="btn-success" onclick="useSelectedAttachments('${type}')" id="useSelectedBtn" style="display: none;">
                                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px; margin-right: 6px;">
                                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                                    </svg>
                                    使用选中项
                                </button>
                            </div>
                            <div class="attachment-list" id="attachmentList">
                                ${items.map((item, index) => `
                                    <div class="attachment-item" style="--item-index: ${index}" data-attachment-id="${item.id}">
                                        ${type !== 'chapter' ? `<div class="attachment-checkbox" onclick="toggleAttachmentSelection(${item.id})">
                                            <svg viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                                            </svg>
                                        </div>` : ''}
                                        <div class="attachment-info">
                                            <div class="attachment-name">${item.name}</div>
                                            <div class="attachment-content">${item.content}</div>
                                        </div>
                                        <div class="attachment-actions">
                                            <button class="attachment-action-btn edit" onclick="editAttachment(${item.id}, '${type}')" title="编辑">
                                                <svg viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                                                </svg>
                                            </button>
                                            <button class="attachment-action-btn use" onclick="useAttachment(${item.id}, '${type}')" title="使用">
                                                <svg viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                                                </svg>
                                            </button>
                                            <button class="attachment-action-btn delete" onclick="deleteAttachment(${item.id}, '${type}')" title="删除">
                                                <svg viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 添加样式
            if (!document.getElementById('attachmentManagerStyles')) {
                const styles = document.createElement('style');
                styles.id = 'attachmentManagerStyles';
                styles.textContent = `
                    .attachment-manager-overlay {
                        position: fixed;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: rgba(0, 0, 0, 0.5);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 15000;
                        opacity: 0;
                        transition: opacity 0.3s ease;
                    }
                    
                    .attachment-manager-overlay.show {
                        opacity: 1;
                    }
                    
                    .attachment-manager-modal {
                        background: var(--bg-panel);
                        border-radius: 12px;
                        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
                        transform: scale(0.9);
                        transition: transform 0.3s ease;
                        max-width: 800px;
                        width: 90%;
                        max-height: 80vh;
                        display: flex;
                        flex-direction: column;
                    }
                    
                    .attachment-manager-overlay.show .attachment-manager-modal {
                        transform: scale(1);
                    }
                    
                    .attachment-manager-header {
                        padding: 20px 24px;
                        border-bottom: 1px solid var(--border-color);
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                    
                    .attachment-manager-title {
                        font-size: 18px;
                        font-weight: 600;
                        color: var(--text-dark);
                        margin: 0;
                    }
                    
                    .attachment-manager-close {
                        width: 32px;
                        height: 32px;
                        border: none;
                        background: transparent;
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: var(--text-light);
                        border-radius: 6px;
                        transition: all 0.2s;
                    }
                    
                    .attachment-manager-close:hover {
                        background: var(--bg-panel-secondary);
                        color: var(--text-dark);
                    }
                    
                    .attachment-manager-close svg {
                        width: 20px;
                        height: 20px;
                    }
                    
                    .attachment-manager-body {
                        padding: 24px;
                        flex: 1;
                        overflow: hidden;
                        display: flex;
                        flex-direction: column;
                    }
                    
                    .attachment-manager-toolbar {
                        margin-bottom: 20px;
                        display: flex;
                        gap: 12px;
                        align-items: center;
                    }
                    
                    .btn-secondary {
                        background: var(--bg-panel-secondary);
                        color: var(--text-primary);
                        border: 1px solid var(--border-color);
                        padding: 8px 16px;
                        border-radius: 6px;
                        cursor: pointer;
                        display: inline-flex;
                        align-items: center;
                        font-size: 14px;
                        transition: all 0.2s;
                    }
                    
                    .btn-secondary:hover {
                        background: var(--bg-panel);
                        border-color: var(--primary-color);
                    }
                    
                    .btn-success {
                        background: #4CAF50;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 6px;
                        cursor: pointer;
                        display: inline-flex;
                        align-items: center;
                        font-size: 14px;
                        transition: all 0.2s;
                    }
                    
                    .btn-success:hover {
                        background: #45a049;
                    }
                    
                    .attachment-list {
                        flex: 1;
                        overflow-y: auto;
                    }
                    
                    .attachment-item {
                        position: relative;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 16px;
                        background: var(--bg-panel-secondary);
                        border: 1px solid var(--border-color);
                        border-radius: 8px;
                        margin-bottom: 12px;
                        opacity: 0;
                        transform: translateX(-20px);
                        animation: itemSlideIn 0.3s ease-out forwards;
                        animation-delay: calc(0.05s * var(--item-index));
                        transition: all 0.2s;
                    }
                    
                    .attachment-item:hover {
                        border-color: var(--primary-color);
                        transform: translateX(0) translateY(-2px);
                    }
                    
                    .attachment-item.multi-select-mode {
                        padding-left: 60px;
                    }
                    
                    .attachment-checkbox {
                        position: absolute;
                        left: 16px;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 20px;
                        height: 20px;
                        border: 2px solid var(--border-color);
                        border-radius: 4px;
                        background: var(--bg-content);
                        cursor: pointer;
                        transition: all 0.2s;
                        display: none;
                        align-items: center;
                        justify-content: center;
                    }
                    
                    .attachment-checkbox.show {
                        display: flex;
                    }
                    
                    .attachment-checkbox.checked {
                        background: var(--primary-color);
                        border-color: var(--primary-color);
                    }
                    
                    .attachment-checkbox svg {
                        width: 12px;
                        height: 12px;
                        color: white;
                        opacity: 0;
                        transition: opacity 0.2s;
                    }
                    
                    .attachment-checkbox.checked svg {
                        opacity: 1;
                    }
                    
                    @keyframes itemSlideIn {
                        to {
                            opacity: 1;
                            transform: translateX(0);
                        }
                    }
                    
                    .attachment-info {
                        flex: 1;
                    }
                    
                    .attachment-name {
                        font-weight: 500;
                        color: var(--text-dark);
                        margin-bottom: 6px;
                        font-size: 16px;
                    }
                    
                    .attachment-content {
                        font-size: 14px;
                        color: var(--text-light);
                        line-height: 1.4;
                    }
                    
                    .attachment-actions {
                        display: flex;
                        gap: 8px;
                    }
                    
                    .attachment-action-btn {
                        width: 36px;
                        height: 36px;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: all 0.2s;
                    }
                    
                    .attachment-action-btn svg {
                        width: 16px;
                        height: 16px;
                    }
                    
                    .attachment-action-btn.edit {
                        background: var(--primary-color);
                        color: white;
                    }
                    
                    .attachment-action-btn.edit:hover {
                        background: var(--primary-color-hover);
                    }
                    
                    .attachment-action-btn.use {
                        background: #4CAF50;
                        color: white;
                    }
                    
                    .attachment-action-btn.use:hover {
                        background: #45a049;
                    }
                    
                    .attachment-action-btn.delete {
                        background: #f44336;
                        color: white;
                    }
                    
                    .attachment-action-btn.delete:hover {
                        background: #da190b;
                    }
                `;
                document.head.appendChild(styles);
            }
            
            // 添加到页面
            document.body.insertAdjacentHTML('beforeend', modalHTML);
            
            // 显示动画
            const overlay = document.getElementById('attachmentManagerOverlay');
            setTimeout(() => {
                overlay.classList.add('show');
            }, 10);
        }
        
        // 关闭附件管理器
        function closeAttachmentManager() {
            const overlay = document.getElementById('attachmentManagerOverlay');
            if (overlay) {
                overlay.classList.remove('show');
                setTimeout(() => {
                    overlay.remove();
                }, 300);
            }
        }
        
        // 多选相关变量
        let attachmentMultiSelectMode = false;
        let selectedAttachments = new Set();
        
        // 附件操作函数
        function addNewAttachment(type) {
            showCustomAlert(`新建${type}功能开发中...`);
        }
        
        function editAttachment(id, type) {
            if (type === 'character') {
                // 人设卡编辑：将信息填充到角色创建表单进行编辑
                const characterData = getCharacterDataById(id);
                if (characterData) {
                    fillCharacterForm(characterData);
                    closeAttachmentManager();
                    openCreateCharacterModal();
                    // 不显示提醒，直接进入编辑模式
                } else {
                    showCustomAlert('未找到对应的人设卡信息', 'error');
                }
            } else if (type === 'chapter') {
                // 章节编辑：打开章节编辑弹窗
                const chapterData = getChapterDataById(id);
                if (chapterData) {
                    openChapterEditModal(chapterData);
                    closeAttachmentManager();
                } else {
                    showCustomAlert('未找到对应的章节信息', 'error');
                }
            } else {
                showCustomAlert(`编辑${type} ID:${id} 功能开发中...`);
            }
        }
        
        // 切换多选模式
        function toggleAttachmentMultiSelect(type) {
            attachmentMultiSelectMode = !attachmentMultiSelectMode;
            const multiSelectBtn = document.getElementById('multiSelectBtn');
            const useSelectedBtn = document.getElementById('useSelectedBtn');
            const attachmentItems = document.querySelectorAll('.attachment-item');
            const checkboxes = document.querySelectorAll('.attachment-checkbox');
            
            if (attachmentMultiSelectMode) {
                multiSelectBtn.textContent = '取消多选';
                multiSelectBtn.innerHTML = `
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px; margin-right: 6px;">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                    取消多选
                `;
                useSelectedBtn.style.display = 'inline-flex';
                attachmentItems.forEach(item => item.classList.add('multi-select-mode'));
                checkboxes.forEach(checkbox => checkbox.classList.add('show'));
            } else {
                multiSelectBtn.innerHTML = `
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px; margin-right: 6px;">
                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14l-5-5 1.41-1.41L14 14.17l7.59-7.59L23 8l-9 9z"/>
                    </svg>
                    多选
                `;
                useSelectedBtn.style.display = 'none';
                attachmentItems.forEach(item => item.classList.remove('multi-select-mode'));
                checkboxes.forEach(checkbox => {
                    checkbox.classList.remove('show', 'checked');
                });
                selectedAttachments.clear();
            }
        }
        
        // 切换附件选择状态
        function toggleAttachmentSelection(id) {
            const checkbox = document.querySelector(`[data-attachment-id="${id}"] .attachment-checkbox`);
            
            if (selectedAttachments.has(id)) {
                selectedAttachments.delete(id);
                checkbox.classList.remove('checked');
            } else {
                selectedAttachments.add(id);
                checkbox.classList.add('checked');
            }
            
            // 更新使用选中项按钮状态
            const useSelectedBtn = document.getElementById('useSelectedBtn');
            if (selectedAttachments.size > 0) {
                useSelectedBtn.textContent = `使用选中项 (${selectedAttachments.size})`;
            } else {
                useSelectedBtn.innerHTML = `
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px; margin-right: 6px;">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                    </svg>
                    使用选中项
                `;
            }
        }
        
        // 使用选中的附件
        function useSelectedAttachments(type) {
            if (selectedAttachments.size === 0) {
                showCustomAlert('请先选择要使用的附件', 'warning');
                return;
            }
            
            if (type === 'character') {
                // 人设卡特殊处理：将多个角色添加到参与角色列表
                const selectedIds = Array.from(selectedAttachments);
                const characterDataList = selectedIds.map(id => getCharacterDataById(id)).filter(data => data);
                
                if (characterDataList.length > 0) {
                    let addedCount = 0;
                    let skippedCount = 0;
                    
                    characterDataList.forEach(characterData => {
                        // 检查角色是否已存在
                        const charId = String(characterData.id);
                        if (currentCharacters.find(c => String(c.id) === charId)) {
                            skippedCount++;
                        } else {
                            // 添加角色到参与列表
                            addCharacterToList(characterData);
                            addedCount++;
                        }
                    });
                    
                    // 关闭弹窗
                    closeAttachmentManager();
                    
                    // 显示成功提示
                    let message = '';
                    if (addedCount > 0) {
                        message += `已添加 ${addedCount} 个角色到参与列表`;
                    }
                    if (skippedCount > 0) {
                        if (message) message += '，';
                        message += `${skippedCount} 个角色已存在`;
                    }
                    showCustomAlert(message || '没有新角色被添加');
                } else {
                    showCustomAlert('未找到对应的人设卡信息', 'error');
                }
            } else {
                // 其他类型附件添加到场景背景输入框
                const input = document.getElementById('scenarioInput');
                const currentText = input.value.trim();
                const selectedIds = Array.from(selectedAttachments);
                const attachmentTexts = selectedIds.map(id => `[使用${type} ID:${id}]`);
                const combinedText = attachmentTexts.join('\n');
                
                if (currentText) {
                    input.value = currentText + '\n\n' + combinedText;
                } else {
                    input.value = combinedText;
                }
                
                // 关闭弹窗
                closeAttachmentManager();
                
                // 聚焦输入框
                input.focus();
                
                // 显示成功提示
                showCustomAlert(`已添加 ${selectedAttachments.size} 个${type}到场景背景`);
            }
        }
        
        // 根据ID获取人设卡数据
        function getCharacterDataById(id) {
            const characterCards = [
                { id: 1, name: '李明', content: '男主角，25岁，程序员，性格内向但善良', personality: '内向但善良，喜欢思考，对技术有热情', background: '25岁的程序员，毕业于计算机专业，在一家互联网公司工作', style: '说话比较直接，偶尔会用一些技术术语', appearance: '中等身材，戴着黑框眼镜，穿着简单的T恤和牛仔裤', secret: '其实内心很渴望找到真爱，但不知道如何表达自己的感情' },
                { id: 2, name: '张小雨', content: '女主角，23岁，设计师，活泼开朗，喜欢旅行', personality: '活泼开朗，充满创意，喜欢尝试新事物，对生活充满热情', background: '23岁的UI设计师，热爱旅行和摄影，梦想是环游世界', style: '说话轻快活泼，经常用一些网络流行语和表情符号', appearance: '身材娇小，长发飘逸，喜欢穿色彩鲜艳的衣服，总是带着甜美的笑容', secret: '虽然表面乐观，但内心对未来的职业发展有些迷茫和不安' },
                { id: 3, name: '王老师', content: '配角，50岁，大学教授，智慧长者', personality: '温和睿智，博学多才，善于倾听和给予建议，有着长者的智慧', background: '50岁的大学教授，教授文学课程，已婚有子女，人生阅历丰富', style: '说话温和有条理，经常引用古诗词和名人名言，语言富有哲理', appearance: '中等身材，头发花白，戴着金丝眼镜，总是穿着整洁的衬衫和西装', secret: '年轻时也曾有过文学梦想，但为了家庭选择了稳定的教师职业，偶尔会怀念那段追梦的时光' }
            ];
            return characterCards.find(card => card.id === id);
        }
        
        // 合并多个人设卡数据
        function mergeCharacterData(characterDataList) {
            if (characterDataList.length === 1) {
                return characterDataList[0];
            }
            
            const merged = {
                name: characterDataList.map(data => data.name).join(' & '),
                personality: characterDataList.map(data => data.personality || data.content).filter(p => p).join('；'),
                background: characterDataList.map(data => data.background).filter(b => b).join('；'),
                style: characterDataList.map(data => data.style).filter(s => s).join('；'),
                appearance: characterDataList.map(data => data.appearance).filter(a => a).join('；'),
                secret: characterDataList.map(data => data.secret).filter(s => s).join('；')
            };
            
            return merged;
        }
        
        // 将人设卡数据填充到角色创建表单
        function fillCharacterForm(characterData) {
            document.getElementById('newCharName').value = characterData.name || '';
            document.getElementById('newCharPersonality').value = characterData.personality || characterData.content || '';
            document.getElementById('newCharBackground').value = characterData.background || '';
            document.getElementById('newCharStyle').value = characterData.style || '';
            document.getElementById('newCharAppearance').value = characterData.appearance || '';
            document.getElementById('newCharSecret').value = characterData.secret || '';
        }

        async function useAttachment(id, type) {
            if (type === 'character') {
                // 人设卡特殊处理：将角色添加到参与角色列表
                const characterData = getCharacterDataById(id);
                if (characterData) {
                    // 检查角色是否已存在
                    const charId = String(characterData.id);
                    if (currentCharacters.find(c => String(c.id) === charId)) {
                        showCustomAlert('该角色已在参与列表中', 'warning');
                        closeAttachmentManager();
                        return;
                    }
                    
                    // 添加角色到参与列表
                    addCharacterToList(characterData);
                    
                    // 关闭弹窗
                    closeAttachmentManager();
                    
                    // 显示成功提示
                    showCustomAlert(`${characterData.name} 已添加到参与角色`);
                } else {
                    showCustomAlert('未找到对应的人设卡信息', 'error');
                }
            } else if (type === 'chapter') {
                // 章节特殊处理：提取AI场景信息并填充到场景背景
                const chapterData = getChapterDataById(id);
                if (chapterData) {
                    const input = document.getElementById('scenarioInput');
                    const currentText = input.value.trim();
                    
                    // 显示加载提示
                    showCustomAlert('正在提取章节场景信息...', 'info');
                    
                    try {
                        // 调用AI提取场景信息
                        const sceneInfo = await analyzeChapterContentWithAI(chapterData.content);
                        
                        // 将AI提取的场景信息填充到场景背景
                        if (currentText) {
                            input.value = currentText + '\n\n' + sceneInfo;
                        } else {
                            input.value = sceneInfo;
                        }
                        
                        // 关闭弹窗
                        closeAttachmentManager();
                        
                        // 聚焦输入框
                        input.focus();
                        
                        // 显示成功提示
                        showCustomAlert(`已将"${chapterData.name}"的场景信息添加到场景背景`);
                    } catch (error) {
                        console.error('AI分析章节失败:', error);
                        // AI分析失败时，使用基础分析作为备用
                        const basicInfo = analyzeChapterContent(chapterData.content);
                        
                        if (currentText) {
                            input.value = currentText + '\n\n' + basicInfo;
                        } else {
                            input.value = basicInfo;
                        }
                        
                        closeAttachmentManager();
                        input.focus();
                        showCustomAlert(`已添加"${chapterData.name}"的基础场景信息（AI分析失败，使用备用方案）`, 'warning');
                    }
                } else {
                    showCustomAlert('未找到对应的章节信息', 'error');
                }
            } else {
                // 其他类型附件添加到场景背景输入框
                const input = document.getElementById('scenarioInput');
                const currentText = input.value.trim();
                const attachmentText = `[使用${type} ID:${id}]`;
                
                if (currentText) {
                    input.value = currentText + '\n\n' + attachmentText;
                } else {
                    input.value = attachmentText;
                }
                
                // 关闭弹窗
                closeAttachmentManager();
                
                // 聚焦输入框
                input.focus();
                
                // 显示成功提示
                showCustomAlert(`${type}已添加到场景背景`);
            }
        }
        
        async function deleteAttachment(id, type) {
             const confirmed = await confirm(`确定要删除这个${type}吗？`);
             if (confirmed) {
                 showCustomAlert(`删除${type} ID:${id} 功能开发中...`);
             }
         }
        
        // 点击弹窗外部关闭附件管理器
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('attachment-manager-overlay')) {
                closeAttachmentManager();
            }
        });

        // 点击弹窗外部关闭
        document.getElementById('historyModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeHistoryDialog();
            }
        });
        
        document.getElementById('scenarioDetailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeScenarioDetail();
            }
        });
        
        // --- 章节编辑功能 ---
        
        // 获取章节数据
        function getChapterDataById(id) {
            const chapters = [
                { id: 1, name: '第一章：相遇', content: '男女主角在咖啡厅的偶然相遇，因为一杯意外洒落的咖啡而开始了他们的故事。阳光透过落地窗洒在桌案上，她正专心阅读着一本小说，他匆忙赶路时不小心撞到了她的桌子...' },
                { id: 2, name: '第二章：误会', content: '因为一个小误会产生的矛盾，让两人的关系变得复杂起来。她以为他是故意的，而他却不知道如何解释。朋友们试图从中调解，但似乎让事情变得更加复杂...' },
                { id: 3, name: '第三章：和解', content: '通过朋友的帮助重新认识彼此，两人终于敞开心扉，真诚地交流。在一个安静的公园里，他们坐在长椅上，看着夕阳西下，慢慢地理解了对方的想法...' }
            ];
            return chapters.find(chapter => chapter.id === id);
        }
        
        // 打开章节编辑弹窗
        function openChapterEditModal(chapterData) {
            const modal = document.getElementById('chapterEditModal');
            
            // 填充表单数据
            if (chapterData) {
                document.getElementById('editChapterTitle').value = chapterData.name;
                document.getElementById('editChapterContent').value = chapterData.content;
                document.getElementById('extractedSceneInfo').value = '';
            } else {
                // 新建章节
                document.getElementById('editChapterTitle').value = '';
                document.getElementById('editChapterContent').value = '';
                document.getElementById('extractedSceneInfo').value = '';
            }
            
            modal.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
        }
        
        // 关闭章节编辑弹窗
        function closeChapterEditModal() {
            const modal = document.getElementById('chapterEditModal');
            modal.classList.remove('show');
            
            setTimeout(() => {
                modal.style.display = 'none';
                document.getElementById('chapterEditForm').reset();
                document.getElementById('extractedSceneInfo').value = '';
            }, 300);
        }
        
        // AI提取场景信息
        async function extractSceneInfo() {
            const content = document.getElementById('editChapterContent').value.trim();
            if (!content) {
                showCustomAlert('请先输入章节内容', 'error');
                return;
            }
            
            const extractedInfo = document.getElementById('extractedSceneInfo');
            extractedInfo.value = '正在分析章节内容...';
            
            try {
                const sceneInfo = await analyzeChapterContentWithAI(content);
                extractedInfo.value = sceneInfo;
            } catch (error) {
                console.error('AI分析章节失败:', error);
                showCustomAlert('AI分析章节失败，请稍后重试', 'error');
                extractedInfo.value = '';
            }
        }
        
        // 调用AI分析章节内容
        async function analyzeChapterContentWithAI(content) {
            const prompt = `你是一个专业的文本分析师，请仔细分析以下章节内容，客观地提取关键的场景信息。请忠于原文内容，不要过度解读或添加原文中没有的信息。

章节内容：
${content}

请从以下几个维度进行分析：
1. 主要人物：识别文中出现的重要角色及其特征
2. 场景地点：分析故事发生的具体地点和环境
3. 情感氛围：识别文中的情感基调和氛围变化
4. 关键事件：提取推动情节发展的重要事件
5. 主要主题：分析文本传达的核心主题和意义
6. 时间背景：识别故事发生的时间设定
7. 冲突要素：分析文中的矛盾和冲突点
8. 重要细节：提取文中的关键描述和细节信息

请按照以下格式输出分析结果：

=== AI场景分析报告 ===

📝 主要人物：
• [人物1及其特征]
• [人物2及其特征]

🏠 场景地点：
• [地点1及其描述]
• [地点2及其描述]

💭 情感氛围：
• [情感基调1]
• [氛围变化2]

⭐ 关键事件：
• [事件1]
• [事件2]

🎯 主要主题：
• [主题1]
• [主题2]

⏰ 时间背景：
• [时间设定]

⚡ 冲突要素：
• [冲突1]
• [冲突2]

🔍 重要细节：
• [细节1]
• [细节2]

💡 角色扮演建议：
• [基于文本内容的建议1]
• [基于文本内容的建议2]
• [基于文本内容的建议3]

请确保分析准确、客观，忠实反映原文内容，为后续的角色扮演提供有价值的参考信息。`;
            
            const response = await callAIAPI(prompt, 'doubao-seed-1-6-flash-250615');
            return response.trim();
        }
        
        // 保留原有的简单分析函数作为备用
        function analyzeChapterContent(content) {
            // 这里是简化的分析结果，作为AI分析失败时的备用方案
            return '=== 基础场景分析 ===\n\n📝 检测到文本内容，建议使用AI提取功能获取详细分析。\n\n💡 基础建议：\n• 根据文本内容调整角色对话风格\n• 参考文中的环境描述设置场景\n• 注意文本中体现的情感基调';
        }
        
        // 章节编辑表单提交
        document.getElementById('chapterEditForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const title = document.getElementById('editChapterTitle').value.trim();
            const content = document.getElementById('editChapterContent').value.trim();
            const sceneInfo = document.getElementById('extractedSceneInfo').value.trim();
            
            if (!title || !content) {
                showCustomAlert('请填写章节标题和内容', 'error');
                return;
            }
            
            // 如果有AI提取的场景信息，将其应用到场景背景
            if (sceneInfo && sceneInfo !== '正在分析章节内容...') {
                const scenarioInput = document.getElementById('scenarioInput');
                const currentScenario = scenarioInput.value.trim();
                
                if (currentScenario) {
                    scenarioInput.value = currentScenario + '\n\n' + sceneInfo;
                } else {
                    scenarioInput.value = sceneInfo;
                }
                
                showCustomAlert('章节保存成功！场景信息已应用到场景背景');
            } else {
                showCustomAlert('章节保存成功！');
            }
            
            closeChapterEditModal();
        });
        
        // 点击弹窗外部关闭
        document.getElementById('chapterEditModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeChapterEditModal();
            }
        });

        // 获取聊天历史记录
        function getChatHistory() {
            return chatHistory.filter(msg => msg.type !== 'scenario' && msg.content && msg.content.trim());
        }

        // 生成并显示总结内容
        async function generateAndDisplaySummary() {
            try {
                const chatHistoryData = getChatHistory();
                if (chatHistoryData.length < 5) return; // 对话太少时不生成总结

                const summaryPrompt = `你是一个专业的对话分析师，请分析以下对话内容，提取关键信息和线索。

对话记录：
${chatHistoryData.map(msg => `${msg.character || '用户'}: ${msg.content}`).join('\n')}

请按照以下格式分析并总结：

🔍 关键信息：
• [重要的背景信息和设定]

🎭 角色动态：
• [角色行为和关系变化]

📍 场景要素：
• [环境、地点、时间等要素]

🔗 情节线索：
• [推动故事发展的关键线索]

⚡ 最新进展：
• [当前状况和新发现]

只显示页面信息，不要包含任何代码内容或技术细节。`;

                const summary = await callAIAPI(summaryPrompt, 'claude-sonnet-4-20250514');
                displaySummary(summary);
            } catch (error) {
                console.error('生成总结失败:', error);
            }
        }

        // 显示总结内容
        function displaySummary(summaryText) {
            const summaryContent = document.getElementById('summaryContent');
            if (!summaryContent) return;

            // 清除占位符
            summaryContent.innerHTML = '';

            // 解析总结内容并格式化显示
            const lines = summaryText.split('\n').filter(line => line.trim());
            let currentCategory = '';
            let currentItems = [];

            lines.forEach(line => {
                line = line.trim();
                if (line.startsWith('🔍') || line.startsWith('🎭') || line.startsWith('📍') || 
                    line.startsWith('🔗') || line.startsWith('⚡')) {
                    // 保存上一个分类的内容
                    if (currentCategory && currentItems.length > 0) {
                        addSummaryCategory(summaryContent, currentCategory, currentItems);
                    }
                    // 开始新分类
                    currentCategory = line;
                    currentItems = [];
                } else if (line.startsWith('•')) {
                    currentItems.push(line.substring(1).trim());
                }
            });

            // 添加最后一个分类
            if (currentCategory && currentItems.length > 0) {
                addSummaryCategory(summaryContent, currentCategory, currentItems);
            }

            // 添加时间戳
            const timestamp = document.createElement('div');
            timestamp.className = 'summary-timestamp';
            timestamp.textContent = `更新时间: ${new Date().toLocaleString('zh-CN')}`;
            summaryContent.appendChild(timestamp);
        }

        // 添加总结分类
        function addSummaryCategory(container, category, items) {
            const summaryItem = document.createElement('div');
            summaryItem.className = 'summary-item';

            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'summary-category';
            categoryDiv.textContent = category;

            const textDiv = document.createElement('div');
            textDiv.className = 'summary-text';
            textDiv.innerHTML = items.map(item => `• ${item}`).join('<br>');

            summaryItem.appendChild(categoryDiv);
            summaryItem.appendChild(textDiv);
            container.appendChild(summaryItem);
        }

        // 智能分析面板现在固定显示，不再需要切换功能
        function toggleSummaryPanel() {
            // 面板现在固定显示，此函数保留以避免错误，但不执行任何操作
            return;
        }

        // 页面加载时初始化总结面板
        document.addEventListener('DOMContentLoaded', function() {
            // 智能分析面板现在固定显示，无需初始化拖拽功能
            const panel = document.getElementById('dynamicSummaryPanel');
            if (panel) {
                // 确保面板始终显示
                panel.classList.remove('collapsed');
            }
        });

    </script>
</body>
</html>
