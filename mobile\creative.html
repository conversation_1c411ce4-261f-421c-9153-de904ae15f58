<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <title>创意 - 笔尖传奇</title>
    <style>
        /* --- 移动端创意界面CSS --- */
        :root {
            /* 基础颜色 */
            --bg-main: #F0F3F7;
            --bg-panel: #FAFBFC;
            --bg-content: #F7F9FB;
            --text-dark: #2D3748;
            --text-light: #718096;
            --text-on-primary: #FFFFFF;
            --border-color: #E8EBEF;
            --shadow-color: rgba(93, 156, 236, 0.08);
            --shadow-color-light: rgba(0, 0, 0, 0.04);
            --primary-color: #5D9CEC;
            --primary-color-hover: #4A89E2;
            --accent-color: #48BB78;
            --warning-color: #FF6B6B;
            
            /* 创意专用颜色 */
            --prompt-bg-golden: #FFF8E1;
            --prompt-bg-novel: #E8F5E9;
            --prompt-bg-outline: #E3F2FD;
            --prompt-bg-world: #F3E5F5;
            --prompt-bg-character: #FFE0B2;
            --prompt-bg-script: #E0F2F1;
            --prompt-bg-title: #FFEFD5;
            --prompt-bg-intro: #F0E6FF;
            
            /* 移动端尺寸 */
            --mobile-header-height: 56px;
            --mobile-padding: 16px;
            --mobile-border-radius: 12px;
            --mobile-font-size: 16px;
            --mobile-font-size-sm: 14px;
            --mobile-font-size-lg: 18px;
        }

        [data-theme="dark"] {
            --bg-main: #1A202C;
            --bg-panel: #2D3748;
            --bg-content: #323B4C;
            --text-dark: #E2E8F0;
            --text-light: #A0AEC0;
            --border-color: #4A5568;
            --primary-color: #4A5568;
            --primary-color-hover: #718096;
            --accent-color: #48BB78;
            --shadow-color: rgba(0, 0, 0, 0.2);
            --prompt-bg-golden: #4A4A3A;
            --prompt-bg-novel: #3A4A3A;
            --prompt-bg-outline: #3A3A4A;
            --prompt-bg-world: #4A3A4A;
            --prompt-bg-character: #4A4A3A;
            --prompt-bg-script: #3A4A4A;
            --prompt-bg-title: #4A453A;
            --prompt-bg-intro: #453A4A;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", "Helvetica Neue", sans-serif;
            background-color: var(--bg-main);
            color: var(--text-dark);
            font-size: var(--mobile-font-size);
            line-height: 1.6;
            height: 100vh;
            overflow-x: hidden;
            transition: background-color 0.3s, color 0.3s;
        }

        /* --- 创意容器 --- */
        .creative-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* --- 顶部导航栏 --- */
        .creative-header {
            height: var(--mobile-header-height);
            background: var(--bg-panel);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--mobile-padding);
            position: relative;
            z-index: 100;
            box-shadow: 0 2px 8px var(--shadow-color-light);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            width: 36px;
            height: 36px;
            border: none;
            background: transparent;
            color: var(--text-dark);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .back-btn:hover {
            background: var(--bg-content);
        }

        .creative-title {
            font-size: var(--mobile-font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-btn {
            padding: 6px 12px;
            background: var(--bg-content);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: var(--mobile-font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .header-btn:hover {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        .header-btn.primary {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        /* --- 分类标签栏 --- */
        .category-tabs {
            background: var(--bg-panel);
            border-bottom: 1px solid var(--border-color);
            padding: 0 var(--mobile-padding);
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .category-tabs::-webkit-scrollbar {
            display: none;
        }

        .tabs-container {
            display: flex;
            gap: 8px;
            padding: 12px 0;
            min-width: max-content;
        }

        .category-tab {
            padding: 8px 16px;
            background: var(--bg-content);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            font-size: var(--mobile-font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .category-tab:hover {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        .category-tab.active {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        .category-icon {
            font-size: 16px;
        }

        /* --- 主内容区 --- */
        .main-content {
            flex: 1;
            overflow-y: auto;
            padding: var(--mobile-padding);
        }

        /* --- 提示词网格 --- */
        .prompts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 16px;
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* --- 提示词卡片 --- */
        .prompt-card {
            background: var(--bg-panel);
            border-radius: var(--mobile-border-radius);
            padding: 20px;
            box-shadow: 0 2px 8px var(--shadow-color);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .prompt-card:active {
            transform: scale(0.98);
        }

        .prompt-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 16px var(--shadow-color);
        }

        .prompt-card.golden {
            background: var(--prompt-bg-golden);
        }

        .prompt-card.novel {
            background: var(--prompt-bg-novel);
        }

        .prompt-card.outline {
            background: var(--prompt-bg-outline);
        }

        .prompt-card.world {
            background: var(--prompt-bg-world);
        }

        .prompt-card.character {
            background: var(--prompt-bg-character);
        }

        .prompt-card.script {
            background: var(--prompt-bg-script);
        }

        .prompt-card.title {
            background: var(--prompt-bg-title);
        }

        .prompt-card.intro {
            background: var(--prompt-bg-intro);
        }

        .prompt-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .prompt-icon {
            font-size: 24px;
            margin-right: 12px;
        }

        .prompt-title {
            font-size: var(--mobile-font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
            flex: 1;
        }

        .prompt-badge {
            background: var(--accent-color);
            color: var(--text-on-primary);
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 500;
        }

        .prompt-desc {
            color: var(--text-light);
            font-size: var(--mobile-font-size-sm);
            line-height: 1.5;
            margin-bottom: 16px;
        }

        .prompt-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: var(--text-light);
        }

        .prompt-meta {
            display: flex;
            gap: 12px;
        }

        .prompt-action {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border: none;
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .prompt-action:hover {
            background: var(--primary-color-hover);
            transform: scale(1.05);
        }

        /* --- 空状态 --- */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            text-align: center;
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: var(--mobile-font-size-lg);
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-dark);
        }

        .empty-desc {
            font-size: var(--mobile-font-size-sm);
            color: var(--text-light);
            line-height: 1.5;
        }

        /* --- 响应式优化 --- */
        @media (max-width: 480px) {
            .prompts-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            
            .prompt-card {
                padding: 16px;
            }
            
            .prompt-icon {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="creative-container">
        <!-- 顶部导航栏 -->
        <header class="creative-header">
            <div class="header-left">
                <button class="back-btn" onclick="goBack()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
                    </svg>
                </button>
                <h1 class="creative-title">创意工坊</h1>
            </div>
            <div class="header-right">
                <button class="header-btn" onclick="showMyPrompts()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
                    </svg>
                    我的
                </button>
                <button class="header-btn primary" onclick="createPrompt()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                    创建
                </button>
            </div>
        </header>

        <!-- 分类标签栏 -->
        <div class="category-tabs">
            <div class="tabs-container">
                <div class="category-tab active" onclick="filterCategory('all')">
                    <span class="category-icon">🌟</span>
                    <span>全部</span>
                </div>
                <div class="category-tab" onclick="filterCategory('golden')">
                    <span class="category-icon">⭐</span>
                    <span>黄金一章</span>
                </div>
                <div class="category-tab" onclick="filterCategory('novel')">
                    <span class="category-icon">📚</span>
                    <span>小说创作</span>
                </div>
                <div class="category-tab" onclick="filterCategory('outline')">
                    <span class="category-icon">📋</span>
                    <span>大纲设计</span>
                </div>
                <div class="category-tab" onclick="filterCategory('world')">
                    <span class="category-icon">🌍</span>
                    <span>世界观</span>
                </div>
                <div class="category-tab" onclick="filterCategory('character')">
                    <span class="category-icon">👤</span>
                    <span>人物设定</span>
                </div>
                <div class="category-tab" onclick="filterCategory('script')">
                    <span class="category-icon">🎬</span>
                    <span>剧本创作</span>
                </div>
                <div class="category-tab" onclick="filterCategory('title')">
                    <span class="category-icon">🏷️</span>
                    <span>标题生成</span>
                </div>
                <div class="category-tab" onclick="filterCategory('intro')">
                    <span class="category-icon">📜</span>
                    <span>简介撰写</span>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <main class="main-content">
            <div class="prompts-grid" id="promptsGrid">
                <!-- 提示词卡片将通过JavaScript动态生成 -->
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <div class="empty-icon">💡</div>
                <div class="empty-title">暂无相关提示词</div>
                <div class="empty-desc">尝试切换其他分类或创建您自己的提示词</div>
            </div>
        </main>
    </div>

    <script>
        // 完整的UGC提示词数据生态系统
        const promptsData = {
            golden: [
                {
                    id: 'g1',
                    title: '黄金开篇模板',
                    desc: '经典的小说开篇结构，快速抓住读者注意力',
                    content: '你是一位经验丰富的小说编辑，请帮我创作一个引人入胜的小说开篇。要求：1. 在前100字内建立冲突或悬念 2. 快速建立主角形象 3. 营造独特的氛围 4. 为后续情节发展埋下伏笔。请根据以下信息创作：\n\n小说类型：[请填写]\n主角设定：[请填写]\n故事背景：[请填写]\n\n请创作一个不超过300字的精彩开篇。',
                    icon: '⭐',
                    likes: 1234,
                    uses: 5678,
                    category: 'golden',
                    author: '开篇大师',
                    tags: ['开篇', '悬念', '人物塑造'],
                    scene: '小说开篇创作',
                    hint: '请根据AI生成的开篇，结合自己的创意进行修改完善'
                },
                {
                    id: 'g2',
                    title: '悬念设置大师',
                    desc: '在开篇就埋下引人入胜的悬念',
                    content: '你是悬疑小说专家，擅长设置引人入胜的悬念。请帮我在小说开篇设置一个强有力的悬念钩子。要求：1. 悬念要与主线剧情相关 2. 既要吸引读者又不能过早暴露真相 3. 为后续情节发展留下空间 4. 符合故事的整体基调。\n\n故事类型：[请填写]\n主要冲突：[请填写]\n目标读者：[请填写]\n\n请设计一个悬念场景，并说明如何在后续章节中逐步揭示。',
                    icon: '🔮',
                    likes: 987,
                    uses: 3456,
                    category: 'golden',
                    author: '悬疑专家',
                    tags: ['悬念', '钩子', '情节设计'],
                    scene: '悬念设置',
                    hint: '使用生成的悬念设计，注意在后续章节中合理展开'
                },
                {
                    id: 'g3',
                    title: '情感共鸣开篇',
                    desc: '通过情感共鸣快速拉近与读者的距离',
                    content: '你是情感描写专家，擅长通过细腻的情感描写建立读者共鸣。请帮我创作一个以情感为核心的小说开篇。要求：1. 选择普遍性的情感体验 2. 通过具体细节展现情感 3. 避免过度煽情 4. 为角色发展奠定基础。\n\n情感主题：[请填写]\n角色背景：[请填写]\n故事环境：[请填写]\n\n请创作一个情感真挚、细节丰富的开篇段落。',
                    icon: '💝',
                    likes: 856,
                    uses: 2987,
                    category: 'golden',
                    author: '情感导师',
                    tags: ['情感', '共鸣', '细节描写'],
                    scene: '情感开篇',
                    hint: '注意情感的真实性，避免过度渲染'
                }
            ],
            novel: [
                {
                    id: 'n1',
                    title: '玄幻小说创作大师',
                    desc: '专业的玄幻小说创作指导，包含完整的修炼体系设计',
                    content: '你是玄幻小说创作专家，拥有丰富的东方玄幻创作经验。请帮我创作玄幻小说内容。要求：1. 构建完整的修炼体系 2. 设计独特的法宝和功法 3. 创造有层次的世界观 4. 平衡战力体系避免崩坏。\n\n创作需求：[请填写具体需求]\n世界背景：[请填写]\n主角设定：[请填写]\n修炼体系：[请填写或让AI设计]\n\n请提供详细的创作建议和具体内容。',
                    icon: '⚔️',
                    likes: 2345,
                    uses: 7890,
                    category: 'novel',
                    author: '玄幻宗师',
                    tags: ['玄幻', '修炼体系', '世界观', '战力平衡'],
                    scene: '玄幻小说创作',
                    hint: '注意修炼体系的逻辑性和战力平衡'
                },
                {
                    id: 'n2',
                    title: '都市言情助手',
                    desc: '现代都市背景的言情小说创作模板',
                    content: '你是都市言情小说专家，擅长现代都市背景的情感故事创作。请帮我创作都市言情内容。要求：1. 贴近现实生活场景 2. 情感发展自然真实 3. 冲突设置合理 4. 人物性格鲜明。\n\n故事背景：[请填写]\n男女主设定：[请填写]\n情感线索：[请填写]\n冲突设置：[请填写]\n\n请创作具体的情节内容或对话场景。',
                    icon: '💕',
                    likes: 1876,
                    uses: 4321,
                    category: 'novel',
                    author: '言情专家',
                    tags: ['都市', '言情', '现实', '情感'],
                    scene: '都市言情创作',
                    hint: '保持情感发展的真实性和合理性'
                },
                {
                    id: 'n3',
                    title: '科幻世界构建师',
                    desc: '构建完整的科幻世界观和技术体系',
                    content: '你是科幻小说世界观设计专家，擅长构建逻辑严密的科幻世界。请帮我设计科幻小说的世界观。要求：1. 科学理论基础扎实 2. 技术发展逻辑合理 3. 社会结构完整 4. 为故事发展服务。\n\n科幻类型：[硬科幻/软科幻]\n时间背景：[请填写]\n核心科技：[请填写]\n社会结构：[请填写]\n\n请提供详细的世界观设定和技术体系说明。',
                    icon: '🚀',
                    likes: 1543,
                    uses: 3210,
                    category: 'novel',
                    author: '科幻架构师',
                    tags: ['科幻', '世界观', '技术体系', '逻辑'],
                    scene: '科幻世界构建',
                    hint: '确保科技设定的逻辑自洽性'
                },
                {
                    id: 'n4',
                    title: '历史小说顾问',
                    desc: '历史背景小说的专业创作指导',
                    content: '你是历史小说创作专家，对各个历史时期都有深入了解。请帮我创作历史小说内容。要求：1. 历史背景准确 2. 人物符合时代特征 3. 语言风格贴合历史 4. 情节发展合理。\n\n历史时期：[请填写]\n主要人物：[请填写]\n历史事件：[请填写]\n创作重点：[请填写]\n\n请提供符合历史背景的创作内容。',
                    icon: '📜',
                    likes: 1234,
                    uses: 2876,
                    category: 'novel',
                    author: '史学专家',
                    tags: ['历史', '考据', '时代背景', '人物塑造'],
                    scene: '历史小说创作',
                    hint: '注意历史细节的准确性'
                }
            ],
            outline: [
                {
                    id: 'o1',
                    title: '三幕式结构大师',
                    desc: '经典的三幕式故事结构设计模板',
                    content: '你是故事结构专家，精通三幕式结构设计。请帮我设计一个完整的三幕式故事大纲。要求：1. 第一幕建立世界和角色（25%） 2. 第二幕发展冲突和转折（50%） 3. 第三幕解决冲突和结局（25%） 4. 关键转折点设置合理。\n\n故事类型：[请填写]\n主要角色：[请填写]\n核心冲突：[请填写]\n故事主题：[请填写]\n\n请提供详细的三幕式结构大纲，包括关键情节点。',
                    icon: '📊',
                    likes: 1654,
                    uses: 4567,
                    category: 'outline',
                    author: '结构大师',
                    tags: ['三幕式', '结构', '情节点', '转折'],
                    scene: '故事结构设计',
                    hint: '注意各幕之间的比例和转折点的设置'
                },
                {
                    id: 'o2',
                    title: '英雄之旅模板',
                    desc: '基于坎贝尔英雄之旅的故事大纲',
                    content: '你是英雄之旅理论专家，熟悉坎贝尔的单一神话模式。请帮我设计基于英雄之旅的故事大纲。要求：1. 包含完整的17个阶段 2. 适应现代故事需求 3. 角色成长弧线清晰 4. 冒险历程引人入胜。\n\n英雄设定：[请填写]\n冒险世界：[请填写]\n导师角色：[请填写]\n最终考验：[请填写]\n\n请提供完整的英雄之旅大纲，标注各个关键阶段。',
                    icon: '🗡️',
                    likes: 1432,
                    uses: 3890,
                    category: 'outline',
                    author: '神话学者',
                    tags: ['英雄之旅', '成长', '冒险', '神话'],
                    scene: '英雄历程设计',
                    hint: '确保英雄的成长弧线完整且有说服力'
                },
                {
                    id: 'o3',
                    title: '多线程情节编织',
                    desc: '复杂多线程故事的情节编织技巧',
                    content: '你是复杂叙事专家，擅长多线程情节的编织和统筹。请帮我设计多线程故事大纲。要求：1. 各线程独立完整 2. 线程间有机关联 3. 交汇点设计巧妙 4. 整体节奏协调。\n\n主线情节：[请填写]\n副线情节：[请填写]\n角色关系：[请填写]\n交汇设计：[请填写]\n\n请提供多线程情节的编织方案和时间线安排。',
                    icon: '🕸️',
                    likes: 1123,
                    uses: 2654,
                    category: 'outline',
                    author: '编织大师',
                    tags: ['多线程', '情节编织', '复杂叙事', '节奏'],
                    scene: '复杂情节设计',
                    hint: '保持各线程的平衡，避免主次不分'
                }
            ],
            world: [
                {
                    id: 'w1',
                    title: '魔法体系设计',
                    desc: '完整的魔法世界观和法则设定',
                    icon: '🔮',
                    likes: 1987,
                    uses: 5432,
                    category: 'world'
                },
                {
                    id: 'w2',
                    title: '未来科技设定',
                    desc: '科幻小说的技术体系和社会结构',
                    icon: '🤖',
                    likes: 1765,
                    uses: 4123,
                    category: 'world'
                }
            ],
            character: [
                {
                    id: 'c1',
                    title: '立体人物塑造',
                    desc: '创造有血有肉的立体角色',
                    icon: '👤',
                    likes: 2109,
                    uses: 6543,
                    category: 'character'
                },
                {
                    id: 'c2',
                    title: '反派角色设计',
                    desc: '设计令人印象深刻的反派角色',
                    icon: '😈',
                    likes: 1654,
                    uses: 3987,
                    category: 'character'
                }
            ],
            script: [
                {
                    id: 's1',
                    title: '电影剧本格式',
                    desc: '标准的电影剧本写作格式和技巧',
                    icon: '🎬',
                    likes: 1432,
                    uses: 2876,
                    category: 'script'
                },
                {
                    id: 's2',
                    title: '对话写作大师',
                    desc: '写出生动自然的角色对话',
                    icon: '💬',
                    likes: 1876,
                    uses: 4321,
                    category: 'script'
                }
            ],
            title: [
                {
                    id: 't1',
                    title: '吸睛标题生成',
                    desc: '创造引人注目的作品标题',
                    icon: '🏷️',
                    likes: 2345,
                    uses: 7654,
                    category: 'title'
                }
            ],
            intro: [
                {
                    id: 'i1',
                    title: '作品简介撰写',
                    desc: '写出吸引读者的作品简介',
                    icon: '📜',
                    likes: 1987,
                    uses: 5432,
                    category: 'intro'
                }
            ]
        };

        let currentCategory = 'all';
        let allPrompts = [];

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTheme();
            initializePrompts();
            renderPrompts();
        });

        // 主题管理
        function loadTheme() {
            const savedTheme = localStorage.getItem('mobile-theme') || 'default';
            document.documentElement.setAttribute('data-theme', savedTheme);
        }

        // 初始化提示词数据
        function initializePrompts() {
            allPrompts = [];
            Object.keys(promptsData).forEach(category => {
                allPrompts = allPrompts.concat(promptsData[category]);
            });
        }

        // 返回功能
        function goBack() {
            window.location.href = 'index.html';
        }

        // 分类筛选
        function filterCategory(category) {
            currentCategory = category;

            // 更新标签状态
            document.querySelectorAll('.category-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.closest('.category-tab').classList.add('active');

            renderPrompts();
        }

        // 渲染提示词
        function renderPrompts() {
            const grid = document.getElementById('promptsGrid');
            const emptyState = document.getElementById('emptyState');

            let filteredPrompts = currentCategory === 'all' ? allPrompts :
                                 allPrompts.filter(prompt => prompt.category === currentCategory);

            if (filteredPrompts.length === 0) {
                grid.style.display = 'none';
                emptyState.style.display = 'flex';
                return;
            }

            grid.style.display = 'grid';
            emptyState.style.display = 'none';

            grid.innerHTML = filteredPrompts.map(prompt => `
                <div class="prompt-card ${prompt.category}" onclick="usePrompt('${prompt.id}')">
                    <div class="prompt-header">
                        <span class="prompt-icon">${prompt.icon}</span>
                        <h3 class="prompt-title">${prompt.title}</h3>
                        <span class="prompt-badge">HOT</span>
                    </div>
                    <p class="prompt-desc">${prompt.desc}</p>
                    <div class="prompt-stats">
                        <div class="prompt-meta">
                            <span>👍 ${prompt.likes}</span>
                            <span>🔥 ${prompt.uses}</span>
                        </div>
                        <button class="prompt-action" onclick="event.stopPropagation(); usePrompt('${prompt.id}')">使用</button>
                    </div>
                </div>
            `).join('');
        }

        // 使用提示词
        function usePrompt(promptId) {
            const prompt = allPrompts.find(p => p.id === promptId);
            if (prompt) {
                // 这里可以跳转到写作界面并应用提示词
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                        type: 'usePrompt',
                        prompt: prompt
                    }, '*');
                } else {
                    alert(`使用提示词: ${prompt.title}`);
                }
            }
        }

        // 显示我的提示词
        function showMyPrompts() {
            alert('我的提示词功能开发中...');
        }

        // 创建提示词
        function createPrompt() {
            alert('创建提示词功能开发中...');
        }
    </script>
</body>
</html>
