<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创意 - 笔尖传奇写作</title>
    <style>
        /* --- 1. 全局与动态配色系统 (与其他页面统一) --- */
        :root {
            /* 默认皮肤 (护眼蓝灰) */
            --bg-main: #F0F3F7;
            --bg-panel: #FAFBFC;
            --bg-panel-secondary: #F3F5F8;
            --bg-content: #F7F9FB;
            --text-dark: #2D3748;
            --text-light: #718096;
            --text-on-primary: #FFFFFF;
            --border-color: #E8EBEF;
            --gutter-color: transparent;
            --shadow-color: rgba(93, 156, 236, 0.08);
            --shadow-color-light: rgba(0, 0, 0, 0.04);
            --shadow-color-heavy: rgba(0, 0, 0, 0.12);
            /* 主题色 */
            --primary-color: #5D9CEC;
            --primary-color-hover: #4A89E2;
            --secondary-color: #8696A7;
            --accent-color: #48BB78;
            --accent-color-hover: #3AA967;
            --warning-color: #FF6B6B;
            --warning-color-hover: #FF5252;
            /* 内容区专用色 */
            --content-header-bg: #E8F2FF;
            --content-header-color: #2B5797;
            /* 布局尺寸 */
            --font-size-base: 16px;
            --font-size-sm: 14px;
            --font-size-lg: 18px;
            --header-height: 52px;
            --sidebar-width: 80px;
            --sidebar-collapsed-width: 4px;
            /* 创意页面专用色 */
            --prompt-bg-golden: #FFF8E1;
            --prompt-bg-novel: #E8F5E9;
            --prompt-bg-outline: #E3F2FD;
            --prompt-bg-world: #F3E5F5;
            --prompt-bg-character: #FFE0B2;
            --prompt-bg-script: #E0F2F1;
            --prompt-bg-title: #FFEFD5;
            --prompt-bg-intro: #F0E6FF;
            --prompt-bg-teardown: #FFE8F0;
        }

        [data-theme="green-leaf"] {
            --bg-main: #EFF3EF;
            --bg-panel: #F8FAF8;
            --bg-panel-secondary: #F0F3F0;
            --bg-content: #F5F8F5;
            --text-dark: #3E4A3E;
            --text-light: #6B7C6B;
            --border-color: #E0E6E0;
            --primary-color: #6A9C89;
            --primary-color-hover: #5A8C79;
            --secondary-color: #8A9B94;
            --accent-color: #E99469;
            --accent-color-hover: #D98459;
            --shadow-color: rgba(106, 156, 137, 0.08);
            --content-header-bg: #E5F2E9;
            --content-header-color: #3A6B4F;
        }

        [data-theme="sepia"] {
            --bg-main: #FBF0D9;
            --bg-panel: #FAF4E8;
            --bg-panel-secondary: #F6ECDA;
            --bg-content: #FAF4E8;
            --text-dark: #5C4B33;
            --text-light: #8B7355;
            --border-color: #EAE0C8;
            --primary-color: #A67B5B;
            --primary-color-hover: #966B4B;
            --secondary-color: #B0A08D;
            --accent-color: #5D9CEC;
            --accent-color-hover: #4A89E2;
            --shadow-color: rgba(166, 123, 91, 0.1);
            --content-header-bg: #F4E6D4;
            --content-header-color: #7A5A3A;
        }

        [data-theme="dark"] {
            --bg-main: #1A202C;
            --bg-panel: #2D3748;
            --bg-panel-secondary: #252E3E;
            --bg-content: #323B4C;
            --text-dark: #E2E8F0;
            --text-light: #A0AEC0;
            --border-color: #4A5568;
            --primary-color: #4A5568;
            --primary-color-hover: #718096;
            --secondary-color: #3B475C;
            --accent-color: #48BB78;
            --accent-color-hover: #3AA967;
            --shadow-color: rgba(0, 0, 0, 0.2);
            --content-header-bg: #3A4558;
            --content-header-color: #CBD5E0;
            --prompt-bg-golden: #4A4A3A;
            --prompt-bg-novel: #3A4A3A;
            --prompt-bg-outline: #3A3A4A;
            --prompt-bg-world: #4A3A4A;
            --prompt-bg-character: #4A4A3A;
            --prompt-bg-script: #3A4A4A;
            --prompt-bg-title: #4A453A;
            --prompt-bg-intro: #453A4A;
            --prompt-bg-teardown: #4A3A45;
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", "Helvetica Neue", sans-serif;
            background-color: var(--bg-main);
            margin: 0;
            padding: 0;
            color: var(--text-dark);
            font-size: var(--font-size-base);
            line-height: 1.7;
            -webkit-font-smoothing: antialiased;
            display: flex;
            height: 100vh;
            overflow: hidden;
            transition: background-color 0.3s, color 0.3s;
        }

        /* --- 左侧导航栏 (优化版) --- */
        .sidebar-wrapper {
            position: relative;
            width: var(--sidebar-width);
            flex-shrink: 0;
            transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar-wrapper.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar {
            position: absolute;
            left: 0;
            top: 0;
            width: var(--sidebar-width);
            height: 100%;
            background: var(--bg-panel);
            box-shadow: 2px 0 8px var(--shadow-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 0;
            z-index: 100;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .sidebar-wrapper.collapsed .sidebar {
            transform: translateX(calc(-1 * var(--sidebar-width) + var(--sidebar-collapsed-width)));
        }

        .sidebar-trigger {
            position: fixed;
            left: 0;
            top: 0;
            width: 20px;
            height: 100%;
            z-index: 101;
        }

        .sidebar-wrapper:not(.collapsed) .sidebar-trigger {
            width: calc(var(--sidebar-width) + 20px);
        }

        .sidebar-content {
            opacity: 1;
            transition: opacity 0.2s;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100%;
        }
        .sidebar-wrapper.collapsed .sidebar-content {
            opacity: 0;
            pointer-events: none;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px var(--shadow-color);
            transition: transform 0.2s;
            flex-shrink: 0;
        }
        .user-avatar:hover {
            transform: scale(1.05);
        }
        .user-avatar img { 
            width: 100%; 
            height: 100%; 
            object-fit: cover; 
        }

        .nav-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            width: 100%;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            color: var(--text-light);
            transition: all 0.2s ease;
            padding: 8px;
            border-radius: 10px;
            width: 60px;
        }
        .nav-item:hover {
            color: var(--primary-color);
            background-color: var(--bg-panel-secondary);
        }
        .nav-item.active {
            color: var(--primary-color);
            background-color: var(--bg-panel-secondary);
        }
        .nav-item-icon {
            font-size: 20px;
            width: 24px;
            height: 24px;
            fill: currentColor;
        }
        .nav-item-text {
            font-size: 11px;
            font-weight: 500;
        }
        .sidebar-footer {
            margin-top: auto;
        }

        /* --- 主内容区 --- */
        .main-content {
            flex-grow: 1;
            display: flex;
            height: 100vh;
            overflow: hidden;
        }

        /* --- 创意界面布局 --- */
        .creative-container {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            background: var(--bg-main);
            position: relative;
        }

        /* 顶部工具栏 */
        .creative-header {
            height: var(--header-height);
            background: var(--bg-panel);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            flex-shrink: 0;
            box-shadow: 0 2px 8px var(--shadow-color-light);
        }

        .header-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        .header-btn {
            padding: 8px 16px;
            background: var(--bg-content);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .header-btn:hover {
            background: var(--bg-panel-secondary);
            border-color: var(--primary-color);
        }

        .btn-primary {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background: var(--primary-color-hover);
            transform: translateY(-1px);
        }

        .btn-workflow {
            background: var(--accent-color);
            color: var(--text-on-primary);
            border-color: var(--accent-color);
        }

        .btn-workflow:hover {
            background: var(--accent-color-hover);
            transform: translateY(-1px);
        }

        /* 创意内容区 */
        .creative-content {
            flex-grow: 1;
            display: flex;
            overflow: hidden;
        }

        /* 左侧分类菜单 */
        .category-sidebar {
            width: 240px;
            background: var(--bg-panel);
            border-right: 1px solid var(--border-color);
            overflow-y: auto;
            padding: 20px;
        }

        .category-section {
            margin-bottom: 25px;
        }

        .category-title {
            font-size: var(--font-size-sm);
            font-weight: 600;
            color: var(--text-light);
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .category-title::after {
            content: '';
            flex: 1;
            height: 1px;
            background: var(--border-color);
        }

        .category-list {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .category-item {
            padding: 10px 14px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 10px;
            color: var(--text-dark);
            font-size: var(--font-size-sm);
        }

        .category-item:hover {
            background: var(--bg-content);
        }

        .category-item.active {
            background: var(--primary-color);
            color: var(--text-on-primary);
        }

        .category-icon {
            font-size: 18px;
            width: 24px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .category-icon img {
            width: 20px;
            height: 20px;
            object-fit: contain;
        }

        .category-name {
            font-size: var(--font-size-sm);
            font-weight: 500;
        }

        .category-count {
            margin-left: auto;
            font-size: 12px;
            opacity: 0.7;
            background: var(--bg-panel-secondary);
            padding: 2px 8px;
            border-radius: 12px;
        }

        .category-item.active .category-count {
            background: rgba(255, 255, 255, 0.2);
        }

        /* 提示词展示区 */
        .prompts-area {
            flex-grow: 1;
            overflow-y: auto;
            padding: 30px;
            background: var(--bg-main);
        }

        .prompts-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .prompts-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-dark);
        }

        .view-toggle {
            display: flex;
            gap: 8px;
            background: var(--bg-panel);
            padding: 4px;
            border-radius: 8px;
        }

        .view-btn {
            padding: 6px 12px;
            background: transparent;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            color: var(--text-light);
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .view-btn.active {
            background: var(--bg-content);
            color: var(--primary-color);
        }

        /* 提示词网格 */
        .prompts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 提示词卡片 */
        .prompt-card {
            background: var(--bg-panel);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px var(--shadow-color);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            opacity: 0;
            transform: translateY(20px);
            animation: cardFadeIn 0.5s ease-out forwards;
        }

        .prompt-card:nth-child(n) {
            animation-delay: calc(0.05s * var(--card-index));
        }

        @keyframes cardFadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .prompt-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 20px var(--shadow-color-heavy);
        }

        /* 根据类型设置不同背景色 */
        .prompt-card.type-golden {
            background: var(--prompt-bg-golden);
        }

        .prompt-card.type-novel {
            background: var(--prompt-bg-novel);
        }

        .prompt-card.type-outline {
            background: var(--prompt-bg-outline);
        }

        .prompt-card.type-world {
            background: var(--prompt-bg-world);
        }

        .prompt-card.type-character {
            background: var(--prompt-bg-character);
        }

        .prompt-card.type-script {
            background: var(--prompt-bg-script);
        }

        .prompt-card.type-title {
            background: var(--prompt-bg-title);
        }

        .prompt-card.type-intro {
            background: var(--prompt-bg-intro);
        }

        .prompt-card.type-teardown {
            background: var(--prompt-bg-teardown);
        }

        .prompt-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .prompt-icon {
            font-size: 2rem;
            margin-right: 12px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .prompt-icon img {
            width: 32px;
            height: 32px;
            object-fit: contain;
        }

        .prompt-title {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--text-dark);
            flex-grow: 1;
        }

        .prompt-actions {
            display: flex;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .prompt-card:hover .prompt-actions {
            opacity: 1;
        }

        .prompt-action {
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.8);
            color: var(--text-dark);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .prompt-action:hover {
            background: var(--bg-content);
            transform: scale(1.1);
        }

        .prompt-desc {
            font-size: var(--font-size-sm);
            color: var(--text-light);
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .prompt-tags {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .prompt-tag {
            padding: 2px 10px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 12px;
            font-size: 12px;
            color: var(--text-dark);
        }

        /* 列表视图 */
        .prompts-list {
            display: none;
            flex-direction: column;
            gap: 12px;
        }

        .prompts-list.active {
            display: flex;
        }

        .prompt-list-item {
            background: var(--bg-panel);
            border-radius: 10px;
            padding: 16px 20px;
            box-shadow: 0 2px 4px var(--shadow-color-light);
            transition: all 0.2s;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .prompt-list-item:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 12px var(--shadow-color);
        }

        .prompt-list-icon {
            font-size: 1.5rem;
            width: 40px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .prompt-list-icon img {
            width: 24px;
            height: 24px;
            object-fit: contain;
        }

        .prompt-list-content {
            flex-grow: 1;
        }

        .prompt-list-title {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 4px;
        }

        .prompt-list-desc {
            font-size: var(--font-size-sm);
            color: var(--text-light);
        }

        .prompt-list-actions {
            display: flex;
            gap: 8px;
        }

        /* 空状态 */
        .empty-state {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px;
            text-align: center;
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .empty-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 10px;
        }

        .empty-desc {
            font-size: var(--font-size-base);
            color: var(--text-light);
            max-width: 400px;
            line-height: 1.6;
        }

        /* 创建提示词弹窗 */
        .modal {
            display: none;
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0);
            z-index: 1000;
            justify-content: center;
            align-items: center;
            transition: background-color 0.3s ease-out;
        }

        .modal.show {
            background: rgba(0, 0, 0, 0.6);
        }

        .modal-content {
            background: var(--bg-panel);
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            transform: scale(0.9) translateY(20px);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .modal.show .modal-content {
            transform: scale(1) translateY(0);
            opacity: 1;
        }

        .modal-header {
            margin-bottom: 24px;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 8px;
        }

        .modal-desc {
            font-size: var(--font-size-sm);
            color: var(--text-light);
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(10px);
            animation: formFadeIn 0.4s ease-out forwards;
        }

        .form-group:nth-child(1) { animation-delay: 0.1s; }
        .form-group:nth-child(2) { animation-delay: 0.15s; }
        .form-group:nth-child(3) { animation-delay: 0.2s; }
        .form-group:nth-child(4) { animation-delay: 0.25s; }
        .form-group:nth-child(5) { animation-delay: 0.3s; }
        .form-group:nth-child(6) { animation-delay: 0.35s; }

        @keyframes formFadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-label {
            display: block;
            font-size: var(--font-size-sm);
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 8px;
        }

        .form-input,
        .form-textarea,
        .form-select {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-content);
            color: var(--text-dark);
            font-size: var(--font-size-sm);
            transition: all 0.2s;
            font-family: inherit;
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        .form-input:focus,
        .form-textarea:focus,
        .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }

        .form-hint {
            font-size: 12px;
            color: var(--text-light);
            margin-top: 4px;
        }

        /* 图标选择器 */
        .icon-selector {
            margin-top: 12px;
        }

        .icon-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 8px;
            padding: 12px;
            background: var(--bg-content);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            max-height: 200px;
            overflow-y: auto;
        }

        .icon-option {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid transparent;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 20px;
        }

        .icon-option:hover {
            background: var(--bg-panel);
            border-color: var(--primary-color);
        }

        .icon-option.selected {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        .icon-option img {
            width: 24px;
            height: 24px;
            object-fit: contain;
        }

        /* 自定义图标上传 */
        .custom-icon-upload {
            margin-top: 12px;
            padding: 12px;
            background: var(--bg-content);
            border: 1px solid var(--border-color);
            border-radius: 8px;
        }

        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .upload-area:hover {
            border-color: var(--primary-color);
            background: var(--bg-panel-secondary);
        }

        .upload-icon {
            font-size: 2rem;
            margin-bottom: 8px;
            opacity: 0.5;
        }

        .upload-text {
            font-size: var(--font-size-sm);
            color: var(--text-light);
        }

        .upload-hint {
            font-size: 12px;
            color: var(--text-light);
            margin-top: 4px;
        }

        .uploaded-icon-preview {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-top: 12px;
            padding: 8px;
            background: var(--bg-panel);
            border-radius: 8px;
        }

        .uploaded-icon-preview img {
            width: 32px;
            height: 32px;
            object-fit: contain;
        }

        /* 标签输入 */
        .tags-input-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-content);
            min-height: 44px;
            align-items: center;
            transition: all 0.2s;
        }

        .tags-input-container:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }

        .tag-item {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 10px;
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-radius: 16px;
            font-size: var(--font-size-sm);
        }

        .tag-remove {
            cursor: pointer;
            font-size: 16px;
            line-height: 1;
            opacity: 0.8;
        }

        .tag-remove:hover {
            opacity: 1;
        }

        .tags-input {
            flex-grow: 1;
            border: none;
            background: transparent;
            font-size: var(--font-size-sm);
            min-width: 100px;
        }

        .tags-input:focus {
            outline: none;
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: var(--font-size-sm);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-cancel {
            background: var(--bg-content);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
        }

        .btn-cancel:hover {
            background: var(--bg-panel-secondary);
        }

        .btn-confirm {
            background: var(--primary-color);
            color: var(--text-on-primary);
        }

        .btn-confirm:hover {
            background: var(--primary-color-hover);
            transform: translateY(-1px);
        }

        /* AI生成按钮 */
        .ai-generate-btn {
            padding: 8px 16px;
            background: var(--bg-panel);
            color: var(--primary-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
            margin-top: 8px;
            width: 100%;
            justify-content: center;
        }

        .ai-generate-btn:hover {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: transparent;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
            transition: background 0.2s;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-light);
        }

        /* 全局流程弹窗样式 */
        .workflow-modal-content {
            max-width: 900px;
            width: 95%;
            padding: 32px;
        }

        .workflow-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border-color);
        }

        .workflow-tabs {
            display: flex;
            gap: 4px;
            background: var(--bg-content);
            padding: 4px;
            border-radius: 10px;
            margin-bottom: 24px;
        }

        .workflow-tab {
            padding: 8px 20px;
            background: transparent;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            color: var(--text-light);
            font-size: var(--font-size-sm);
            font-weight: 500;
            transition: all 0.2s;
        }

        .workflow-tab.active {
            background: var(--bg-panel);
            color: var(--primary-color);
            box-shadow: 0 2px 4px var(--shadow-color-light);
        }

        .workflow-tab:hover:not(.active) {
            color: var(--text-dark);
        }

        .workflow-content {
            min-height: 400px;
        }

        /* 流程列表 */
        .workflow-list {
            display: grid;
            gap: 12px;
        }

        .workflow-item {
            background: var(--bg-content);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 16px 20px;
            display: flex;
            align-items: center;
            gap: 16px;
            transition: all 0.2s;
            cursor: pointer;
        }

        .workflow-item:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px var(--shadow-color);
            transform: translateY(-2px);
        }

        .workflow-icon {
            width: 48px;
            height: 48px;
            background: var(--accent-color);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: var(--text-on-primary);
            flex-shrink: 0;
        }

        .workflow-info {
            flex-grow: 1;
        }

        .workflow-name {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 4px;
        }

        .workflow-desc {
            font-size: var(--font-size-sm);
            color: var(--text-light);
            margin-bottom: 8px;
        }

        .workflow-steps {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: var(--text-light);
        }

        .workflow-step-count {
            background: var(--bg-panel-secondary);
            padding: 2px 8px;
            border-radius: 12px;
        }

        .workflow-actions {
            display: flex;
            gap: 8px;
        }

        .workflow-action {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 8px;
            background: var(--bg-panel);
            color: var(--text-dark);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .workflow-action:hover {
            background: var(--primary-color);
            color: var(--text-on-primary);
            transform: scale(1.1);
        }

        /* 流程编辑器 */
        .workflow-editor {
            display: none;
        }

        .workflow-editor.active {
            display: block;
        }

        .workflow-form {
            margin-bottom: 24px;
        }

        .workflow-steps-container {
            background: var(--bg-content);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .workflow-steps-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .workflow-steps-title {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--text-dark);
        }

        .add-step-btn {
            padding: 6px 12px;
            background: var(--accent-color);
            color: var(--text-on-primary);
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-sm);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all 0.2s;
        }

        .add-step-btn:hover {
            background: var(--accent-color-hover);
            transform: translateY(-1px);
        }

        .workflow-step-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .workflow-step-item {
            background: var(--bg-panel);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 16px;
            position: relative;
            transition: all 0.2s;
        }

        .workflow-step-item.dragging {
            opacity: 0.5;
        }

        .workflow-step-item.drag-over {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px var(--shadow-color);
        }

        .step-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .step-handle {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: move;
            color: var(--text-light);
            transition: color 0.2s;
        }

        .step-handle:hover {
            color: var(--primary-color);
        }

        .step-number {
            width: 28px;
            height: 28px;
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-sm);
            font-weight: 600;
        }

        .step-content {
            flex-grow: 1;
        }

        .step-select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-content);
            color: var(--text-dark);
            font-size: var(--font-size-sm);
            margin-bottom: 8px;
        }

        .step-note {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-content);
            font-size: var(--font-size-sm);
            color: var(--text-dark);
            resize: vertical;
            min-height: 60px;
        }

        .step-remove {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 24px;
            height: 24px;
            border: none;
            background: var(--warning-color);
            color: var(--text-on-primary);
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.2s;
        }

        .workflow-step-item:hover .step-remove {
            opacity: 1;
        }

        .step-remove:hover {
            background: var(--warning-color-hover);
            transform: scale(1.1);
        }

        /* 空流程状态 */
        .workflow-empty {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-light);
        }

        .workflow-empty-icon {
            font-size: 3rem;
            margin-bottom: 16px;
            opacity: 0.3;
        }

        .workflow-empty-text {
            font-size: var(--font-size-base);
            margin-bottom: 20px;
        }

        .create-workflow-btn {
            padding: 10px 24px;
            background: var(--accent-color);
            color: var(--text-on-primary);
            border: none;
            border-radius: 8px;
            font-size: var(--font-size-sm);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .create-workflow-btn:hover {
            background: var(--accent-color-hover);
            transform: translateY(-1px);
        }
    </style>
</head>
<body>

    <!-- 鼠标感应区域 -->
    <div class="sidebar-trigger" id="sidebarTrigger"></div>

    <!-- 左侧导航栏包装器 -->
    <div class="sidebar-wrapper collapsed" id="sidebarWrapper">
        <!-- 左侧导航栏 -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-content">
                <a href="#" class="user-avatar">
                    <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="User Avatar">
                </a>
                <div class="nav-group">
                    <div class="nav-item" title="首页" onclick="window.location.href='首页.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                        </svg>
                        <span class="nav-item-text">首页</span>
                    </div>
                    <div class="nav-item" title="书架" onclick="window.location.href='书架.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/>
                        </svg>
                        <span class="nav-item-text">书架</span>
                    </div>
                    <div class="nav-item active" title="创意" onclick="window.location.href='创意.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14.06,9.02l0.91,0.91L5.66,19.24L4.75,18.33L14.06,9.02 M17.66,3c-0.25,0-0.51,0.1-0.7,0.29l-1.83,1.83l3.75,3.75l1.83-1.83c0.39-0.39,0.39-1.02,0-1.41l-2.34-2.34C18.17,3.09,17.92,3,17.66,3L17.66,3z M12.06,6.19L3,15.25V19.24h3.99l9.06-9.06L12.06,6.19z"/>
                        </svg>
                        <span class="nav-item-text">创意</span>
                    </div>
                    <div class="nav-item" title="对话" onclick="window.location.href='对话.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                        </svg>
                        <span class="nav-item-text">对话</span>
                    </div>
                    <div class="nav-item" title="模拟" onclick="window.location.href='模拟.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z M12 4c1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3 1.34 3 3 3z"/>
                        </svg>
                        <span class="nav-item-text">模拟</span>
                    </div>
                </div>
                <div class="sidebar-footer nav-group">
                    <div class="nav-item" title="教程" onclick="window.location.href='教程.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/>
                        </svg>
                        <span class="nav-item-text">教程</span>
                    </div>
                    <div class="nav-item" title="邀请" onclick="window.location.href='邀请.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                        <span class="nav-item-text">邀请</span>
                    </div>
                    <div class="nav-item" title="夜间模式" onclick="toggleNightMode()">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9.37 5.51C9.19 6.15 9.1 6.82 9.1 7.5c0 4.08 3.32 7.4 7.4 7.4.68 0 1.35-.09 1.99-.27C17.45 17.19 14.93 19 12 19c-3.86 0-7-3.14-7-7 0-2.93 1.81-5.45 4.37-6.49z"/>
                        </svg>
                        <span class="nav-item-text">夜间</span>
                    </div>
                </div>
            </div>
        </nav>
    </div>

    <!-- 主内容区 -->
    <main class="main-content">
        <!-- 创意界面 -->
        <div class="creative-container">
            <!-- 顶部工具栏 -->
            <div class="creative-header">
                <h1 class="header-title">创意提示词库</h1>
                <div class="header-actions">
                    <button class="header-btn" onclick="importPrompts()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9 16h6v-6h4l-7-7-7 7h4zm-4 2h14v2H5z"/>
                        </svg>
                        导入
                    </button>
                    <button class="header-btn" onclick="exportPrompts()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
                        </svg>
                        导出
                    </button>
                    <button class="header-btn btn-workflow" onclick="openWorkflowModal()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9 2v2H7c-1.1 0-2 .9-2 2v2c0 1.1.9 2 2 2h2v2H7c-1.1 0-2 .9-2 2v2c0 1.1.9 2 2 2h2v2h2v-2h2c1.1 0 2-.9 2-2v-2c0-1.1-.9-2-2-2h-2v-2h2c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2h-2V2H9zm2 4v2H7V6h4zm-4 8h4v2H7v-2zm6-4h4v2h-4V10zm0 6h4v2h-4v-2z"/>
                        </svg>
                        全局流程
                    </button>

                    <button class="header-btn btn-primary" onclick="openCreateModal()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                        </svg>
                        创建提示词
                    </button>
                </div>
            </div>

            <!-- 内容区 -->
            <div class="creative-content">
                <!-- 左侧分类菜单 -->
                <div class="category-sidebar">
                    <div class="category-item active" data-category="all">
                        <span class="category-icon">🎯</span>
                        <span class="category-name">我的提示词</span>
                        <span class="category-count">45</span>
                    </div>
                    
                    <div class="category-section">
                        <h3 class="category-title">短篇创作</h3>
                        <div class="category-list">
                            <div class="category-item" data-category="intro">
                                <span class="category-icon">📝</span>
                                <span class="category-name">短篇导语</span>
                                <span class="category-count">5</span>
                            </div>
                            <div class="category-item" data-category="content">
                                <span class="category-icon">📄</span>
                                <span class="category-name">短篇正文</span>
                                <span class="category-count">8</span>
                            </div>
                            <div class="category-item" data-category="outline">
                                <span class="category-icon">📋</span>
                                <span class="category-name">短篇大纲</span>
                                <span class="category-count">4</span>
                            </div>
                        </div>
                    </div>

                    <div class="category-section">
                        <h3 class="category-title">长篇创作</h3>
                        <div class="category-list">
                            <div class="category-item" data-category="golden">
                                <span class="category-icon">✨</span>
                                <span class="category-name">黄金一章</span>
                                <span class="category-count">6</span>
                            </div>
                            <div class="category-item" data-category="expand">
                                <span class="category-icon">📖</span>
                                <span class="category-name">扩写润色</span>
                                <span class="category-count">7</span>
                            </div>
                            <div class="category-item" data-category="title">
                                <span class="category-icon">📕</span>
                                <span class="category-name">书名生成</span>
                                <span class="category-count">3</span>
                            </div>
                            <div class="category-item" data-category="bookintro">
                                <span class="category-icon">📑</span>
                                <span class="category-name">简介撰写</span>
                                <span class="category-count">4</span>
                            </div>
                            <div class="category-item" data-category="longoutline">
                                <span class="category-icon">📔</span>
                                <span class="category-name">长篇大纲</span>
                                <span class="category-count">3</span>
                            </div>
                            <div class="category-item" data-category="volume">
                                <span class="category-icon">📚</span>
                                <span class="category-name">长篇卷纲</span>
                                <span class="category-count">3</span>
                            </div>
                            <div class="category-item" data-category="chapter">
                                <span class="category-icon">📖</span>
                                <span class="category-name">长篇章纲</span>
                                <span class="category-count">4</span>
                            </div>
                        </div>
                    </div>

                    <div class="category-section">
                        <h3 class="category-title">辅助创作</h3>
                        <div class="category-list">
					    <div class="category-item" data-category="script">
                                <span class="category-icon">🎬</span>
                                <span class="category-name">剧本对话</span>
                                <span class="category-count">4</span>
                            </div>
                            <div class="category-item" data-category="world">
                                <span class="category-icon">🌍</span>
                                <span class="category-name">世界观</span>
                                <span class="category-count">4</span>
                            </div>
                            <div class="category-item" data-category="character">
                                <span class="category-icon">👤</span>
                                <span class="category-name">人设</span>
                                <span class="category-count">5</span>
                            </div>
                            <div class="category-item" data-category="brainstorm">
                                <span class="category-icon">💡</span>
                                <span class="category-name">脑洞</span>
                                <span class="category-count">6</span>
                            </div>
                            <div class="category-item" data-category="teardown">
                                <span class="category-icon">📊</span>
                                <span class="category-name">拆书</span>
                                <span class="category-count">3</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 提示词展示区 -->
                <div class="prompts-area">
                    <div class="prompts-header">
                        <h2 class="prompts-title">我的提示词</h2>
                        <div class="view-toggle">
                            <button class="view-btn active" onclick="switchView('grid')">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M4 11h5V5H4v6zm0 7h5v-6H4v6zm6 0h5v-6h-5v6zm6 0h5v-6h-5v6zm-6-7h5V5h-5v6zm6-6v6h5V5h-5z"/>
                                </svg>
                                网格
                            </button>
                            <button class="view-btn" onclick="switchView('list')">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"/>
                                </svg>
                                列表
                            </button>
                        </div>
                    </div>

                    <!-- 网格视图 -->
                    <div class="prompts-grid" id="promptsGrid">
                        <!-- 动态生成的提示词卡片 -->
                    </div>

                    <!-- 列表视图 -->
                    <div class="prompts-list" id="promptsList">
                        <!-- 动态生成的提示词列表 -->
                    </div>

                    <!-- 空状态 -->
                
                    <div class="empty-state" id="emptyState" style="display: none;">
                        <div class="empty-icon">💡</div>
                        <div class="empty-title">暂无提示词</div>
                        <div class="empty-desc">该分类下还没有提示词，点击"创建提示词"开始添加</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 创建提示词弹窗 -->
    <div class="modal" id="createModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">创建提示词</h2>
                <p class="modal-desc">创建您专属的提示词，让AI更好地理解您的创作需求</p>
            </div>
            <form id="promptForm">
                <div class="form-group">
                    <label class="form-label">提示词名称</label>
                    <input type="text" class="form-input" id="promptName" placeholder="例如：玄幻小说开篇" required>
                    <div class="form-hint">给您的提示词起一个易于识别的名称</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">提示词类型</label>
                    <select class="form-select" id="promptType" required>
                        <option value="">请选择类型...</option>
                        <optgroup label="短篇创作">
                            <option value="intro">短篇导语</option>
                            <option value="content">短篇正文</option>
                            <option value="outline">短篇大纲</option>
                        </optgroup>
                        <optgroup label="长篇创作">
                            <option value="golden">黄金一章</option>
                            <option value="expand">扩写润色</option>
                            <option value="title">书名生成</option>
                            <option value="bookintro">简介撰写</option>
                            <option value="longoutline">长篇大纲</option>
                            <option value="volume">长篇卷纲</option>
                            <option value="chapter">长篇章纲</option>
                        </optgroup>
                        <optgroup label="辅助创作">
						    <option value="script">剧本对话</option>
                            <option value="world">世界观</option>
                            <option value="character">人设</option>
                            <option value="brainstorm">脑洞</option>
                            <option value="teardown">拆书</option>
                        </optgroup>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">选择图标</label>
                    <div class="icon-selector">
                        <div class="icon-grid" id="iconGrid">
                            <!-- 动态生成图标选项 -->
                        </div>
                    </div>
                    <div class="custom-icon-upload">
                        <label class="form-label">或上传自定义图标</label>
                        <div class="upload-area" onclick="document.getElementById('iconUpload').click()">
                            <div class="upload-icon">📤</div>
                            <div class="upload-text">点击上传图标</div>
                            <div class="upload-hint">支持 PNG/JPG/SVG，建议尺寸 32x32</div>
                        </div>
                        <input type="file" id="iconUpload" accept="image/*" style="display: none;" onchange="handleIconUpload(event)">
                        <div id="uploadedIconPreview" style="display: none;" class="uploaded-icon-preview">
                            <img id="uploadedIcon" src="" alt="自定义图标">
                            <span>已上传自定义图标</span>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">提示词内容</label>
                    <textarea class="form-textarea" id="promptContent" placeholder="请输入提示词的具体内容..." required></textarea>
                    <div class="form-hint">详细描述AI的角色、任务和输出要求</div>
                </div>
                <button type="button" class="ai-generate-btn" onclick="aiGeneratePrompt()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0l4.6-4.6-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
                    </svg>
                    AI智能生成
                </button>
                <div class="form-group">
                    <label class="form-label">简要描述</label>
                    <input type="text" class="form-input" id="promptDesc" placeholder="一句话描述这个提示词的作用">
                </div>
                <div class="form-group">
                    <label class="form-label">标签（按回车添加）</label>
                    <div class="tags-input-container" id="tagsContainer">
                        <input type="text" class="tags-input" id="tagsInput" placeholder="添加标签...">
                    </div>
                    <div class="form-hint">添加相关标签，方便查找和分类</div>
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn btn-cancel" onclick="closeCreateModal()">取消</button>
                    <button type="submit" class="btn btn-confirm">保存提示词</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 全局流程弹窗 -->
    <div class="modal" id="workflowModal">
        <div class="modal-content workflow-modal-content">
            <div class="workflow-header">
                <div>
                    <h2 class="modal-title">全局提示词流程</h2>
                    <p class="modal-desc">组合多个提示词创建完整的创作流程</p>
                </div>
                <button class="btn-cancel" style="border: none;" onclick="closeWorkflowModal()">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>

            <div class="workflow-tabs">
                <button class="workflow-tab active" onclick="switchWorkflowTab('list')">我的流程</button>
                <button class="workflow-tab" onclick="switchWorkflowTab('editor')">创建流程</button>
            </div>

            <div class="workflow-content">
                <!-- 流程列表 -->
                <div class="workflow-list-container" id="workflowListContainer">
                    <div class="workflow-list" id="workflowList">
                        <!-- 动态生成的流程列表 -->
                    </div>
                    <div class="workflow-empty" id="workflowEmpty" style="display: none;">
                        <div class="workflow-empty-icon">🔗</div>
                        <div class="workflow-empty-text">还没有创建任何流程</div>
                        <button class="create-workflow-btn" onclick="switchWorkflowTab('editor')">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                            </svg>
                            创建第一个流程
                        </button>
                    </div>
                </div>

                <!-- 流程编辑器 -->
                <div class="workflow-editor" id="workflowEditor">
                    <form id="workflowForm">
                        <div class="workflow-form">
                            <div class="form-group">
                                <label class="form-label">流程名称</label>
                                <input type="text" class="form-input" id="workflowName" placeholder="例如：完整小说创作流程" required>
                                <div class="form-hint">给流程起一个清晰的名称</div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">流程描述</label>
                                <textarea class="form-textarea" id="workflowDesc" placeholder="描述这个流程的用途和特点..." rows="3"></textarea>
                                <div class="form-hint">简要说明流程的作用和适用场景</div>
                            </div>
                        </div>

                        <div class="workflow-steps-container">
                            <div class="workflow-steps-header">
                                <h3 class="workflow-steps-title">流程步骤</h3>
                                <button type="button" class="add-step-btn" onclick="addWorkflowStep()">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                                    </svg>
                                    添加步骤
                                </button>
                            </div>
                            <div class="workflow-step-list" id="workflowStepList">
                                <!-- 动态生成的步骤列表 -->
                            </div>
                        </div>

                        <div class="modal-actions">
                            <button type="button" class="btn btn-cancel" onclick="resetWorkflowEditor()">重置</button>
                            <button type="submit" class="btn btn-confirm">保存流程</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 主题同步功能 ---
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'default';
            document.documentElement.setAttribute('data-theme', savedTheme);
        }

        // 初始化主题
        initTheme();

        // 监听localStorage变化，实现跨页面主题同步
        window.addEventListener('storage', (e) => {
            if (e.key === 'theme') {
                document.documentElement.setAttribute('data-theme', e.newValue);
            }
        });

        // 夜间模式快捷切换
        function toggleNightMode() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'default' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        }

        // --- 侧边栏自动隐藏功能 (优化版) ---
        const sidebarWrapper = document.getElementById('sidebarWrapper');
        const sidebarTrigger = document.getElementById('sidebarTrigger');
        let sidebarTimer;

        function showSidebar() {
            clearTimeout(sidebarTimer);
            sidebarWrapper.classList.remove('collapsed');
        }

        function hideSidebar() {
            clearTimeout(sidebarTimer);
            sidebarTimer = setTimeout(() => {
                sidebarWrapper.classList.add('collapsed');
            }, 300);
        }

        sidebarTrigger.addEventListener('mouseenter', showSidebar);
        sidebarWrapper.addEventListener('mouseenter', showSidebar);
        sidebarWrapper.addEventListener('mouseleave', hideSidebar);

        // --- 创意功能核心逻辑 ---
        let currentCategory = 'all';
        let currentView = 'grid';
        let promptsData = [];
        let tags = [];
        let selectedIcon = '';
        let customIconData = null;
        let workflowsData = [];
        let currentWorkflowTab = 'list';
        let workflowSteps = [];
        let editingWorkflowId = null;

        // 图标库
        const iconLibrary = {
            intro: ['📝', '✍️', '🖊️', '📄', '📃', '📋', '📌', '🔖'],
            content: ['📄', '📑', '📜', '📰', '🗞️', '📃', '📝', '✏️'],
            outline: ['📋', '📊', '📈', '📉', '🗂️', '📁', '🗄️', '📂'],
            golden: ['✨', '🌟', '💫', '⭐', '🎯', '🔥', '💎', '👑'],
            expand: ['📖', '📚', '📕', '📗', '📘', '📙', '📓', '📔'],
            title: ['📕', '📗', '📘', '📙', '📚', '📖', '🏷️', '🔤'],
            bookintro: ['📑', '📜', '📄', '📃', '📝', '💬', '💭', '🗨️'],
            longoutline: ['📔', '📓', '📒', '📋', '📊', '📈', '📉', '🗒️'],
            volume: ['📚', '📦', '🗂️', '📊', '🗃️', '📈', '📉', '📐'],
            chapter: ['📖', '📄', '📃', '📑', '📜', '🔢', '🔤', '📝'],
            world: ['🌍', '🌎', '🌏', '🗺️', '🏔️', '🌋', '🏝️', '🌆'],
            character: ['👤', '👥', '🧑', '👨', '👩', '🦸', '🧙', '👑'],
            brainstorm: ['💡', '🧠', '💭', '💫', '✨', '🌟', '⚡', '🔮'],
            script: ['🎬', '🎭', '🎪', '🎨', '🎯', '📺', '🎥', '🎞️'],
            teardown: ['📊', '📈', '📉', '🔍', '📋', '📌', '🗂️', '🔖']
        };

        // 示例提示词数据
        const samplePrompts = [
            {
                id: 1,
                name: '玄幻小说黄金开篇',
                type: 'golden',
                icon: '✨',
                content: '请作为一个经验丰富的玄幻小说作家，创作一个引人入胜的开篇章节...',
                desc: '打造吸引读者的玄幻小说第一章',
                tags: ['玄幻', '开篇', '吸引力']
            },
            {
                id: 2,
                name: '都市言情人物塑造',
                type: 'character',
                icon: '👤',
                content: '请帮我塑造一个立体的都市言情小说主角，包括外貌、性格、背景...',
                desc: '创建生动立体的都市言情角色',
                tags: ['都市', '言情', '人物']
            },
            {
                id: 3,
                name: '科幻世界观构建',
                type: 'world',
                icon: '🌍',
                content: '请构建一个完整的科幻世界观，包括社会制度、科技水平、文化背景...',
                desc: '搭建宏大完整的科幻世界',
                tags: ['科幻', '世界观', '设定']
            },
            {
                id: 4,
                name: '悬疑剧情反转设计',
                type: 'brainstorm',
                icon: '💡',
                content: '请设计一个出人意料的剧情反转，要求逻辑严密，伏笔巧妙...',
                desc: '创造令人震撼的剧情反转',
                tags: ['悬疑', '反转', '剧情']
            },
            {
                id: 5,
                name: '古风诗意描写',
                type: 'expand',
                icon: '📖',
                content: '请用古风诗意的语言，扩写这段场景描写，注重意境营造...',
                desc: '为文字增添古典诗意之美',
                tags: ['古风', '描写', '意境']
            },
            {
                id: 6,
                name: '短篇故事大纲生成',
                type: 'outline',
                icon: '📋',
                content: '请为一个5000字的短篇小说生成详细大纲，包括起承转合...',
                desc: '快速构建短篇故事框架',
                tags: ['短篇', '大纲', '结构']
            },
            {
                id: 7,
                name: '精彩书名生成器',
                type: 'title',
                icon: '📕',
                content: '请根据以下故事梗概，生成10个吸引人的书名建议...',
                desc: '为您的作品起一个响亮的名字',
                tags: ['书名', '创意', '吸引力']
            },
            {
                id: 8,
                name: '小说简介撰写',
                type: 'bookintro',
                icon: '📑',
                content: '请为这部小说撰写一个200字左右的精彩简介，要能吸引读者...',
                desc: '打造引人入胜的作品简介',
                tags: ['简介', '推广', '吸引力']
            },
            {
                id: 9,
                name: '长篇卷纲规划',
                type: 'volume',
                icon: '📚',
                content: '请为这部长篇小说设计完整的卷纲，包括每卷的主要情节和高潮...',
                desc: '规划长篇小说的整体架构',
                tags: ['长篇', '卷纲', '架构']
            },
            {
                id: 10,
                name: '章节大纲细化',
                type: 'chapter',
                icon: '📖',
                content: '请为本卷的每一章制定详细大纲，包括主要事件和人物发展...',
                desc: '细化每章的具体内容安排',
                tags: ['章节', '大纲', '细化']
            },
            {
                id: 11,
                name: '长篇小说整体大纲',
                type: 'longoutline',
                icon: '📔',
                content: '请为这部长篇小说制定完整的故事大纲，包括主线、支线、人物成长轨迹...',
                desc: '构建长篇小说的完整框架',
                tags: ['长篇', '大纲', '整体规划']
            },
            {
                id: 12,
                name: '经典作品拆解分析',
                type: 'teardown',
                icon: '📊',
                content: '请对这部经典作品进行深度拆解，分析其结构、技巧、亮点...',
                desc: '学习经典作品的创作精髓',
                tags: ['拆书', '分析', '学习']
            }
        ];

        // 提示词类型配置
        const promptTypes = {
            intro: { name: '短篇导语', color: 'novel' },
            content: { name: '短篇正文', color: 'novel' },
            outline: { name: '短篇大纲', color: 'outline' },
            golden: { name: '黄金一章', color: 'golden' },
            expand: { name: '扩写润色', color: 'novel' },
            title: { name: '书名生成', color: 'title' },
            bookintro: { name: '简介撰写', color: 'intro' },
            longoutline: { name: '长篇大纲', color: 'outline' },
            volume: { name: '长篇卷纲', color: 'outline' },
            chapter: { name: '长篇章纲', color: 'outline' },
			script: { name: '剧本对话', color: 'script' },
            world: { name: '世界观', color: 'world' },
            character: { name: '人设', color: 'character' },
            brainstorm: { name: '脑洞', color: 'world' },
            teardown: { name: '拆书', color: 'teardown' }
        };

        // 初始化
        function init() {
            // 加载本地存储的提示词（只加载用户创建的）
            const savedPrompts = localStorage.getItem('customPrompts');
            if (savedPrompts) {
                promptsData = JSON.parse(savedPrompts);
            } else {
                promptsData = []; // 初始为空，只包含用户创建的提示词
            }
            
            // 加载本地存储的流程
            const savedWorkflows = localStorage.getItem('customWorkflows');
            if (savedWorkflows) {
                workflowsData = JSON.parse(savedWorkflows);
            }
            
            renderPrompts();
            updateCategoryCounts();
            bindEvents();
            initIconGrid();
            initWorkflowEditor();
        }

        // 初始化图标网格
        function initIconGrid() {
            const iconGrid = document.getElementById('iconGrid');
            const allIcons = [];
            
            // 收集所有图标
            Object.values(iconLibrary).forEach(icons => {
                icons.forEach(icon => {
                    if (!allIcons.includes(icon)) {
                        allIcons.push(icon);
                    }
                });
            });
            
            // 渲染图标选项
            iconGrid.innerHTML = allIcons.map(icon => `
                <div class="icon-option" data-icon="${icon}" onclick="selectIcon('${icon}')">
                    ${icon}
                </div>
            `).join('');
        }

        // 选择图标
        function selectIcon(icon) {
            // 清除之前的选择
            document.querySelectorAll('.icon-option').forEach(el => {
                el.classList.remove('selected');
            });
            
            // 设置新选择
            const iconElement = document.querySelector(`[data-icon="${icon}"]`);
            if (iconElement) {
                iconElement.classList.add('selected');
            }
            
            selectedIcon = icon;
            customIconData = null; // 清除自定义图标
            
            // 隐藏自定义图标预览
            document.getElementById('uploadedIconPreview').style.display = 'none';
        }

        // 处理图标上传
        function handleIconUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            // 检查文件类型
            if (!file.type.match(/image.*/)) {
                alert('请上传图片文件');
                return;
            }
            
            // 检查文件大小（限制为 500KB）
            if (file.size > 500 * 1024) {
                alert('图片大小不能超过 500KB');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = (e) => {
                const img = new Image();
                img.onload = () => {
                    // 创建 canvas 来调整图片大小
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    
                    // 设置目标尺寸
                    const targetSize = 32;
                    canvas.width = targetSize;
                    canvas.height = targetSize;
                    
                    // 绘制并调整图片
                    ctx.drawImage(img, 0, 0, targetSize, targetSize);
                    
                    // 获取调整后的图片数据
                    customIconData = canvas.toDataURL('image/png');
                    
                    // 显示预览
                    document.getElementById('uploadedIcon').src = customIconData;
                    document.getElementById('uploadedIconPreview').style.display = 'flex';
                    
                    // 清除图标库选择
                    document.querySelectorAll('.icon-option').forEach(el => {
                        el.classList.remove('selected');
                    });
                    selectedIcon = '';
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        // 绑定事件
        function bindEvents() {
            // 分类切换
            document.querySelectorAll('.category-item').forEach(item => {
                item.addEventListener('click', function() {
                    document.querySelectorAll('.category-item').forEach(el => el.classList.remove('active'));
                    this.classList.add('active');
                    currentCategory = this.dataset.category;
                    updatePromptsTitle(this.querySelector('.category-name').textContent);
                    renderPrompts();
                });
            });

            // 表单提交
            document.getElementById('promptForm').addEventListener('submit', handleFormSubmit);
            document.getElementById('workflowForm').addEventListener('submit', handleWorkflowSubmit);

            // 标签输入
            document.getElementById('tagsInput').addEventListener('keydown', handleTagInput);
        }

        // 更新分类计数
        function updateCategoryCounts() {
            const counts = {
                all: promptsData.length, // "我的提示词"只计算用户创建的
                intro: 0, content: 0, outline: 0,
                golden: 0, expand: 0, title: 0, bookintro: 0, 
                longoutline: 0, volume: 0, chapter: 0,
                world: 0, character: 0, brainstorm: 0, script: 0, teardown: 0
            };

            // 计算所有提示词（包括预设和用户创建的）的分类数量
            const allPrompts = [...samplePrompts, ...promptsData];
            allPrompts.forEach(prompt => {
                if (counts[prompt.type] !== undefined) {
                    counts[prompt.type]++;
                }
            });
            
            // "我的提示词"特殊处理，只显示用户创建的数量
            counts.all = promptsData.length;

            document.querySelectorAll('.category-item').forEach(item => {
                const category = item.dataset.category;
                const countEl = item.querySelector('.category-count');
                if (countEl && counts[category] !== undefined) {
                    countEl.textContent = counts[category];
                }
            });
        }

        // 更新标题
        function updatePromptsTitle(title) {
            document.querySelector('.prompts-title').textContent = title;
        }

        // 渲染提示词
        function renderPrompts() {
            let filteredPrompts;
            
            if (currentCategory === 'all') {
                // "我的提示词" - 只显示用户自己创建的提示词
                filteredPrompts = promptsData;
            } else {
                // 其他分类 - 显示对应类型的所有提示词（包括预设和用户创建的）
                const allPrompts = [...samplePrompts, ...promptsData];
                filteredPrompts = allPrompts.filter(p => p.type === currentCategory);
            }

            if (filteredPrompts.length === 0) {
                document.getElementById('promptsGrid').style.display = 'none';
                document.getElementById('promptsList').style.display = 'none';
                document.getElementById('emptyState').style.display = 'flex';
                return;
            }

            document.getElementById('emptyState').style.display = 'none';

            if (currentView === 'grid') {
                renderGridView(filteredPrompts);
            } else {
                renderListView(filteredPrompts);
            }
        }

        // 渲染网格视图
        function renderGridView(prompts) {
            const grid = document.getElementById('promptsGrid');
            grid.style.display = 'grid';
            document.getElementById('promptsList').style.display = 'none';
            
            grid.innerHTML = prompts.map((prompt, index) => {
                const typeConfig = promptTypes[prompt.type] || {};
                const iconDisplay = prompt.customIcon 
                    ? `<img src="${prompt.customIcon}" alt="自定义图标">`
                    : prompt.icon;
                
                // 判断是否为用户创建的提示词（有数字ID的是用户创建的）
                const isUserCreated = typeof prompt.id === 'number';
                
                // 根据是否为用户创建决定显示哪些操作按钮
                const actionButtons = isUserCreated ? `
                    <button class="prompt-action" onclick="editPrompt(${prompt.id})" title="编辑">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                        </svg>
                    </button>
                    <button class="prompt-action" onclick="copyPromptContent('${prompt.id}')" title="复制">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                        </svg>
                    </button>
                    <button class="prompt-action" onclick="deletePrompt(${prompt.id})" title="删除">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                        </svg>
                    </button>
                ` : `
                    <button class="prompt-action" onclick="copyPromptContent('${prompt.id}')" title="复制">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                        </svg>
                    </button>
                `;
                
                return `
                    <div class="prompt-card type-${typeConfig.color}" style="--card-index: ${index}">
                        <div class="prompt-header">
                            <span class="prompt-icon">${iconDisplay}</span>
                            <h3 class="prompt-title">${prompt.name}</h3>
                            <div class="prompt-actions">
                                ${actionButtons}
                            </div>
                        </div>
                        <p class="prompt-desc">${prompt.desc}</p>
                        <div class="prompt-tags">
                            ${prompt.tags.map(tag => `<span class="prompt-tag">${tag}</span>`).join('')}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 渲染列表视图
        function renderListView(prompts) {
            const list = document.getElementById('promptsList');
            list.style.display = 'flex';
            list.classList.add('active');
            document.getElementById('promptsGrid').style.display = 'none';
            
            list.innerHTML = prompts.map(prompt => {
                const iconDisplay = prompt.customIcon 
                    ? `<img src="${prompt.customIcon}" alt="自定义图标">`
                    : prompt.icon;
                
                // 判断是否为用户创建的提示词（有数字ID的是用户创建的）
                const isUserCreated = typeof prompt.id === 'number';
                
                // 根据是否为用户创建决定显示哪些操作按钮
                const actionButtons = isUserCreated ? `
                    <button class="prompt-action" onclick="editPrompt(${prompt.id})" title="编辑">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                        </svg>
                    </button>
                    <button class="prompt-action" onclick="copyPromptContent('${prompt.id}')" title="复制">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                        </svg>
                    </button>
                    <button class="prompt-action" onclick="deletePrompt(${prompt.id})" title="删除">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                        </svg>
                    </button>
                ` : `
                    <button class="prompt-action" onclick="copyPromptContent('${prompt.id}')" title="复制">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                        </svg>
                    </button>
                `;
                
                return `
                    <div class="prompt-list-item">
                        <span class="prompt-list-icon">${iconDisplay}</span>
                        <div class="prompt-list-content">
                            <h3 class="prompt-list-title">${prompt.name}</h3>
                            <p class="prompt-list-desc">${prompt.desc}</p>
                        </div>
                        <div class="prompt-list-actions">
                            ${actionButtons}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 切换视图
        function switchView(view) {
            currentView = view;
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.closest('.view-btn').classList.add('active');
            renderPrompts();
        }

        // 打开创建弹窗
        function openCreateModal() {
            const modal = document.getElementById('createModal');
            modal.style.display = 'flex';
            
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
            
            // 重置表单
            document.getElementById('promptForm').reset();
            tags = [];
            selectedIcon = '';
            customIconData = null;
            renderTags();
            
            // 清除图标选择
            document.querySelectorAll('.icon-option').forEach(el => {
                el.classList.remove('selected');
            });
            
            // 隐藏自定义图标预览
            document.getElementById('uploadedIconPreview').style.display = 'none';
            
            // 重置标题
            document.querySelector('.modal-title').textContent = '创建提示词';
        }

        // 关闭创建弹窗
        function closeCreateModal() {
            const modal = document.getElementById('createModal');
            modal.classList.remove('show');
            
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // 处理表单提交
        function handleFormSubmit(e) {
            e.preventDefault();
            
            const promptType = document.getElementById('promptType').value;
            
            // 确定使用的图标
            let finalIcon = selectedIcon || getDefaultIconForType(promptType);
            let customIcon = null;
            
            if (customIconData) {
                customIcon = customIconData;
                finalIcon = ''; // 使用自定义图标时清空默认图标
            }
            
            const formData = {
                id: Date.now(),
                name: document.getElementById('promptName').value,
                type: promptType,
                content: document.getElementById('promptContent').value,
                desc: document.getElementById('promptDesc').value || '自定义提示词',
                tags: tags,
                icon: finalIcon,
                customIcon: customIcon
            };
            
            // 添加到数据中
            promptsData.push(formData);
            
            // 保存到本地存储
            localStorage.setItem('customPrompts', JSON.stringify(promptsData));
            
            // 更新界面
            updateCategoryCounts();
            renderPrompts();
            
            // 关闭弹窗
            closeCreateModal();
            
            // 提示成功
            showNotification('提示词创建成功！');
        }

        // 获取类型对应的默认图标
        function getDefaultIconForType(type) {
            const icons = iconLibrary[type];
            return icons ? icons[0] : '📝';
        }

        // 处理标签输入
        function handleTagInput(e) {
            if (e.key === 'Enter' && e.target.value.trim()) {
                e.preventDefault();
                const tag = e.target.value.trim();
                if (!tags.includes(tag)) {
                    tags.push(tag);
                    renderTags();
                }
                e.target.value = '';
            }
        }

        // 渲染标签
        function renderTags() {
            const container = document.getElementById('tagsContainer');
            const input = document.getElementById('tagsInput');
            
            // 清除现有标签
            container.querySelectorAll('.tag-item').forEach(el => el.remove());
            
            // 添加标签
            tags.forEach((tag, index) => {
                const tagEl = document.createElement('div');
                tagEl.className = 'tag-item';
                tagEl.innerHTML = `
                    ${tag}
                    <span class="tag-remove" onclick="removeTag(${index})">×</span>
                `;
                container.insertBefore(tagEl, input);
            });
        }

        // 移除标签
        function removeTag(index) {
            tags.splice(index, 1);
            renderTags();
        }

        // AI生成提示词
        function aiGeneratePrompt() {
            const type = document.getElementById('promptType').value;
            if (!type) {
                alert('请先选择提示词类型');
                return;
            }
            
            // 模拟AI生成
            const templates = {
                golden: '请作为一位经验丰富的网络小说作家，创作一个引人入胜的开篇章节。要求：\n1. 开篇即高潮，快速吸引读者\n2. 世界观展现自然不突兀\n3. 主角形象立体，特点鲜明\n4. 埋下伏笔，引发读者好奇\n5. 文字生动，画面感强',
                character: '请创建一个立体丰满的人物角色，包括：\n1. 基本信息（姓名、年龄、外貌特征）\n2. 性格特点（优点、缺点、怪癖）\n3. 背景故事（出身、经历、转折点）\n4. 核心动机（目标、恐惧、价值观）\n5. 人物关系（家人、朋友、敌人）',
                world: '请构建一个完整的故事世界观，涵盖：\n1. 地理环境（地形、气候、资源）\n2. 社会结构（政治、经济、文化）\n3. 科技/魔法体系（规则、限制、发展）\n4. 历史背景（重要事件、传说）\n5. 种族/势力（分布、特点、关系）',
                outline: '请为一个短篇小说创建详细大纲：\n1. 故事梗概（一句话概括）\n2. 开端（背景介绍、冲突引入）\n3. 发展（情节推进、矛盾激化）\n4. 高潮（冲突爆发、关键转折）\n5. 结局（问题解决、主题升华）',
                title: '请根据故事内容生成吸引人的书名：\n1. 体现故事核心冲突或主题\n2. 朗朗上口，易于记忆\n3. 具有悬念感或画面感\n4. 符合目标读者群体喜好\n5. 避免与现有作品重名',
                bookintro: '请撰写精彩的作品简介：\n1. 开篇抓人，制造悬念\n2. 简述主要冲突和看点\n3. 突出作品特色和卖点\n4. 控制在150-200字\n5. 留下悬念，引发阅读欲望',
                longoutline: '请制定长篇小说的整体大纲：\n1. 故事主线和核心冲突\n2. 主要人物及其成长轨迹\n3. 重要剧情节点和转折\n4. 支线剧情的设置和作用\n5. 整体节奏和篇幅规划',
                volume: '请设计长篇小说的卷纲结构：\n1. 每卷主题和核心冲突\n2. 重要情节转折点\n3. 人物成长轨迹\n4. 伏笔设置和回收\n5. 卷与卷之间的衔接',
                chapter: '请制定详细的章节大纲：\n1. 章节标题和主要内容\n2. 场景设置和氛围营造\n3. 人物互动和关系发展\n4. 情节推进的节奏控制\n5. 章末悬念或转折设置',
                expand: '请对以下内容进行扩写润色：\n1. 丰富细节描写，增强画面感\n2. 深化人物心理，展现情感变化\n3. 优化语言表达，提升文学性\n4. 保持风格统一，符合整体基调\n5. 适度扩展，避免冗长拖沓',
                teardown: '请对作品进行深度拆解分析：\n1. 故事结构分析（起承转合）\n2. 人物塑造技巧（立体化方法）\n3. 情节设计亮点（冲突与转折）\n4. 文笔风格特色（语言运用）\n5. 创作经验总结（可借鉴之处）'
            };
            
            const content = templates[type] || '请根据以下要求创作内容：\n1. 符合类型特点\n2. 逻辑清晰完整\n3. 细节丰富生动\n4. 有创意和亮点';
            
            document.getElementById('promptContent').value = content;
            
            // 自动选择对应图标
            const typeIcons = iconLibrary[type];
            if (typeIcons && typeIcons.length > 0) {
                selectIcon(typeIcons[0]);
            }
            
            // 动画效果
            const textarea = document.getElementById('promptContent');
            textarea.style.opacity = '0';
            setTimeout(() => {
                textarea.style.transition = 'opacity 0.3s';
                textarea.style.opacity = '1';
            }, 100);
        }

        // 编辑提示词
        function editPrompt(id) {
            const prompt = promptsData.find(p => p.id === id);
            if (!prompt) return;
            
            // 填充表单
            document.getElementById('promptName').value = prompt.name;
            document.getElementById('promptType').value = prompt.type;
            document.getElementById('promptContent').value = prompt.content;
            document.getElementById('promptDesc').value = prompt.desc;
            tags = [...prompt.tags];
            renderTags();
            
            // 处理图标
            if (prompt.customIcon) {
                customIconData = prompt.customIcon;
                document.getElementById('uploadedIcon').src = customIconData;
                document.getElementById('uploadedIconPreview').style.display = 'flex';
                selectedIcon = '';
            } else if (prompt.icon) {
                selectIcon(prompt.icon);
                customIconData = null;
            }
            
            // 打开弹窗
            openCreateModal();
            
            // 修改为编辑模式
            document.querySelector('.modal-title').textContent = '编辑提示词';
            
            // 更新提交处理
            const form = document.getElementById('promptForm');
            form.onsubmit = (e) => {
                e.preventDefault();
                
                // 确定使用的图标
                let finalIcon = selectedIcon || getDefaultIconForType(prompt.type);
                let customIcon = null;
                
                if (customIconData) {
                    customIcon = customIconData;
                    finalIcon = ''; // 使用自定义图标时清空默认图标
                }
                
                // 更新数据
                prompt.name = document.getElementById('promptName').value;
                prompt.type = document.getElementById('promptType').value;
                prompt.content = document.getElementById('promptContent').value;
                prompt.desc = document.getElementById('promptDesc').value;
                prompt.tags = tags;
                prompt.icon = finalIcon;
                prompt.customIcon = customIcon;
                
                // 保存
                localStorage.setItem('customPrompts', JSON.stringify(promptsData));
                
                // 更新界面
                updateCategoryCounts();
                renderPrompts();
                closeCreateModal();
                showNotification('提示词更新成功！');
                
                // 恢复原始提交处理
                form.onsubmit = handleFormSubmit;
                document.querySelector('.modal-title').textContent = '创建提示词';
            };
        }

        // 复制提示词内容（通用函数，支持预设和用户创建的提示词）
        function copyPromptContent(id) {
            // 先在用户创建的提示词中查找
            let prompt = promptsData.find(p => p.id == id);
            
            // 如果没找到，再在预设提示词中查找
            if (!prompt) {
                prompt = samplePrompts.find(p => p.id == id);
            }
            
            if (!prompt) return;
            
            navigator.clipboard.writeText(prompt.content).then(() => {
                showNotification('提示词已复制到剪贴板');
            }).catch(err => {
                // 降级方案
                const textarea = document.createElement('textarea');
                textarea.value = prompt.content;
                textarea.style.position = 'fixed';
                textarea.style.left = '-9999px';
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                showNotification('提示词已复制到剪贴板');
            });
        }
        
        // 复制提示词（保留原函数以兼容现有代码）
        function copyPrompt(id) {
            copyPromptContent(id);
        }

        // 删除提示词
        function deletePrompt(id) {
            if (!confirm('确定要删除这个提示词吗？')) return;
            
            promptsData = promptsData.filter(p => p.id !== id);
            localStorage.setItem('customPrompts', JSON.stringify(promptsData));
            
            updateCategoryCounts();
            renderPrompts();
            showNotification('提示词已删除');
        }

        // 导入提示词
        function importPrompts() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (!file) return;
                
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const imported = JSON.parse(e.target.result);
                        if (Array.isArray(imported)) {
                            // 验证数据格式
                            const validPrompts = imported.filter(p => 
                                p.name && p.type && p.content
                            );
                            
                            if (validPrompts.length === 0) {
                                alert('导入失败：文件中没有有效的提示词');
                                return;
                            }
                            
                            // 为导入的提示词生成新ID
                            const newPrompts = validPrompts.map(p => ({
                                ...p,
                                id: Date.now() + Math.random()
                            }));
                            
                            promptsData = [...promptsData, ...newPrompts];
                            localStorage.setItem('customPrompts', JSON.stringify(promptsData));
                            updateCategoryCounts();
                            renderPrompts();
                            showNotification(`成功导入 ${newPrompts.length} 个提示词`);
                        } else {
                            alert('导入失败：文件格式错误');
                        }
                    } catch (err) {
                        alert('导入失败：文件解析错误');
                    }
                };
                reader.readAsText(file);
            };
            input.click();
        }

        // 导出提示词
        function exportPrompts() {
            if (promptsData.length === 0) {
                alert('没有可导出的提示词');
                return;
            }
            
            // 准备导出数据
            const exportData = promptsData.map(p => ({
                name: p.name,
                type: p.type,
                icon: p.icon,
                customIcon: p.customIcon,
                content: p.content,
                desc: p.desc,
                tags: p.tags
            }));
            
            const dataStr = JSON.stringify(exportData, null, 2);
            const blob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `prompts_${new Date().getTime()}.json`;
            link.click();
            
            URL.revokeObjectURL(url);
            showNotification('提示词已导出');
        }

        // 显示通知
        function showNotification(message) {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: var(--accent-color);
                color: var(--text-on-primary);
                padding: 12px 24px;
                border-radius: 8px;
                box-shadow: 0 4px 12px var(--shadow-color-heavy);
                z-index: 1001;
                animation: slideDown 0.3s ease-out;
                font-size: var(--font-size-sm);
                font-weight: 500;
            `;
            notification.textContent = message;
            
            // 添加动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideDown {
                    from {
                        transform: translateX(-50%) translateY(-100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(-50%) translateY(0);
                        opacity: 1;
                    }
                }
                @keyframes slideUp {
                    from {
                        transform: translateX(-50%) translateY(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(-50%) translateY(-100%);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
            
            document.body.appendChild(notification);
            
            // 3秒后移除
            setTimeout(() => {
                notification.style.animation = 'slideUp 0.3s ease-out';
                setTimeout(() => {
                    notification.remove();
                    style.remove();
                }, 300);
            }, 3000);
        }

        // 点击弹窗外部关闭
        document.getElementById('createModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                closeCreateModal();
            }
        });

        document.getElementById('workflowModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                closeWorkflowModal();
            }
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const createModal = document.getElementById('createModal');
                const workflowModal = document.getElementById('workflowModal');
                
                if (createModal.classList.contains('show')) {
                    closeCreateModal();
                } else if (workflowModal.classList.contains('show')) {
                    closeWorkflowModal();
                }
            }
        });

        // 提示词类型改变时更新图标网格
        document.getElementById('promptType').addEventListener('change', (e) => {
            const type = e.target.value;
            if (type && iconLibrary[type]) {
                // 显示该类型相关的图标
                const iconGrid = document.getElementById('iconGrid');
                const typeIcons = iconLibrary[type];
                const otherIcons = [];
                
                // 收集其他常用图标
                Object.entries(iconLibrary).forEach(([key, icons]) => {
                    if (key !== type) {
                        icons.slice(0, 2).forEach(icon => {
                            if (!typeIcons.includes(icon) && !otherIcons.includes(icon)) {
                                otherIcons.push(icon);
                            }
                        });
                    }
                });
                
                // 合并图标
                const allIcons = [...typeIcons, ...otherIcons.slice(0, 24 - typeIcons.length)];
                
                // 渲染图标选项
                iconGrid.innerHTML = allIcons.map((icon, index) => `
                    <div class="icon-option ${index < typeIcons.length ? 'type-related' : ''}" 
                         data-icon="${icon}" 
                         onclick="selectIcon('${icon}')"
                         title="${index < typeIcons.length ? '推荐图标' : ''}">
                        ${icon}
                    </div>
                `).join('');
                
                // 自动选择第一个推荐图标
                if (!selectedIcon && !customIconData && typeIcons.length > 0) {
                    selectIcon(typeIcons[0]);
                }
            } else {
                // 显示所有图标
                initIconGrid();
            }
        });

        // 全局流程功能
        function openWorkflowModal() {
            const modal = document.getElementById('workflowModal');
            modal.style.display = 'flex';
            
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
            
            renderWorkflowList();
        }

        function closeWorkflowModal() {
            const modal = document.getElementById('workflowModal');
            modal.classList.remove('show');
            
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        function switchWorkflowTab(tab) {
            currentWorkflowTab = tab;
            
            // 更新标签页状态
            document.querySelectorAll('.workflow-tab').forEach(t => {
                t.classList.toggle('active', t.textContent.includes(tab === 'list' ? '我的流程' : '创建流程'));
            });
            
            // 切换内容显示
            document.getElementById('workflowListContainer').style.display = tab === 'list' ? 'block' : 'none';
            document.getElementById('workflowEditor').classList.toggle('active', tab === 'editor');
            
            if (tab === 'list') {
                renderWorkflowList();
            } else {
                if (!editingWorkflowId) {
                    resetWorkflowEditor();
                }
            }
        }

        function renderWorkflowList() {
            const listContainer = document.getElementById('workflowList');
            const emptyState = document.getElementById('workflowEmpty');
            
            if (workflowsData.length === 0) {
                listContainer.style.display = 'none';
                emptyState.style.display = 'block';
            } else {
                listContainer.style.display = 'grid';
                emptyState.style.display = 'none';
                
                listContainer.innerHTML = workflowsData.map(workflow => `
                    <div class="workflow-item">
                        <div class="workflow-icon">🔗</div>
                        <div class="workflow-info">
                            <div class="workflow-name">${workflow.name}</div>
                            <div class="workflow-desc">${workflow.desc || '暂无描述'}</div>
                            <div class="workflow-steps">
                                <span class="workflow-step-count">${workflow.steps.length} 个步骤</span>
                                <span>创建于 ${new Date(workflow.id).toLocaleDateString()}</span>
                            </div>
                        </div>
                        <div class="workflow-actions">
                            <button class="workflow-action" onclick="editWorkflow(${workflow.id})" title="编辑">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                                </svg>
                            </button>
                            <button class="workflow-action" onclick="copyWorkflow(${workflow.id})" title="复制">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                                </svg>
                            </button>
                            <button class="workflow-action" onclick="deleteWorkflow(${workflow.id})" title="删除">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                `).join('');
            }
        }

        function initWorkflowEditor() {
            workflowSteps = [{
                id: Date.now(),
                promptId: '',
                note: ''
            }];
            renderWorkflowSteps();
        }

        function addWorkflowStep() {
            workflowSteps.push({
                id: Date.now(),
                promptId: '',
                note: ''
            });
            renderWorkflowSteps();
        }

        function removeWorkflowStep(stepId) {
            if (workflowSteps.length <= 1) {
                alert('流程至少需要一个步骤');
                return;
            }
            
            workflowSteps = workflowSteps.filter(s => s.id !== stepId);
            renderWorkflowSteps();
        }

        function renderWorkflowSteps() {
            const stepList = document.getElementById('workflowStepList');
            
            stepList.innerHTML = workflowSteps.map((step, index) => `
                <div class="workflow-step-item" data-step-id="${step.id}" draggable="true">
                    <div class="step-header">
                        <div class="step-handle">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M11 18c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2zm-2-8c-1.1 0-2 .9-2 2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm6 4c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0-8c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                            </svg>
                        </div>
                        <div class="step-number">${index + 1}</div>
                        <div class="step-content">
                            <select class="step-select" onchange="updateStepPrompt(${step.id}, this.value)">
                                <option value="">选择提示词...</option>
                                ${promptsData.map(p => `
                                    <option value="${p.id}" ${step.promptId === p.id ? 'selected' : ''}>
                                        ${p.icon || ''} ${p.name}
                                    </option>
                                `).join('')}
                            </select>
                            <textarea class="step-note" 
                                placeholder="为这个步骤添加备注（可选）" 
                                onchange="updateStepNote(${step.id}, this.value)">${step.note || ''}</textarea>
                        </div>
                    </div>
                    ${workflowSteps.length > 1 ? `
                        <button class="step-remove" onclick="removeWorkflowStep(${step.id})" title="删除步骤">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                            </svg>
                        </button>
                    ` : ''}
                </div>
            `).join('');
            
            // 添加拖拽功能
            initDragAndDrop();
        }

        function updateStepPrompt(stepId, promptId) {
            const step = workflowSteps.find(s => s.id === stepId);
            if (step) {
                step.promptId = promptId;
            }
        }

        function updateStepNote(stepId, note) {
            const step = workflowSteps.find(s => s.id === stepId);
            if (step) {
                step.note = note;
            }
        }

        function initDragAndDrop() {
            const items = document.querySelectorAll('.workflow-step-item');
            let draggedItem = null;
            
            items.forEach(item => {
                item.addEventListener('dragstart', function(e) {
                    draggedItem = this;
                    this.classList.add('dragging');
                    e.dataTransfer.effectAllowed = 'move';
                });
                
                item.addEventListener('dragend', function() {
                    this.classList.remove('dragging');
                });
                
                item.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'move';
                    
                    const afterElement = getDragAfterElement(this.parentNode, e.clientY);
                    if (afterElement == null) {
                        this.parentNode.appendChild(draggedItem);
                    } else {
                        this.parentNode.insertBefore(draggedItem, afterElement);
                    }
                });
                
                item.addEventListener('drop', function(e) {
                    e.preventDefault();
                    updateWorkflowStepsOrder();
                });
            });
        }

        function getDragAfterElement(container, y) {
            const draggableElements = [...container.querySelectorAll('.workflow-step-item:not(.dragging)')];
            
            return draggableElements.reduce((closest, child) => {
                const box = child.getBoundingClientRect();
                const offset = y - box.top - box.height / 2;
                
                if (offset < 0 && offset > closest.offset) {
                    return { offset: offset, element: child };
                } else {
                    return closest;
                }
            }, { offset: Number.NEGATIVE_INFINITY }).element;
        }

        function updateWorkflowStepsOrder() {
            const stepElements = document.querySelectorAll('.workflow-step-item');
            const newOrder = Array.from(stepElements).map(el => {
                const stepId = parseInt(el.dataset.stepId);
                return workflowSteps.find(s => s.id === stepId);
            });
            workflowSteps = newOrder;
            renderWorkflowSteps();
        }

        function resetWorkflowEditor() {
            document.getElementById('workflowForm').reset();
            workflowSteps = [{
                id: Date.now(),
                promptId: '',
                note: ''
            }];
            editingWorkflowId = null;
            renderWorkflowSteps();
        }

        function handleWorkflowSubmit(e) {
            e.preventDefault();
            
            const name = document.getElementById('workflowName').value;
            const desc = document.getElementById('workflowDesc').value;
            
            // 验证至少有一个步骤选择了提示词
            const validSteps = workflowSteps.filter(s => s.promptId);
            if (validSteps.length === 0) {
                alert('请至少为一个步骤选择提示词');
                return;
            }
            
            const workflowData = {
                id: editingWorkflowId || Date.now(),
                name: name,
                desc: desc,
                steps: workflowSteps.filter(s => s.promptId),
                created: editingWorkflowId ? (workflowsData.find(w => w.id === editingWorkflowId)?.created || Date.now()) : Date.now(),
                updated: Date.now()
            };
            
            if (editingWorkflowId) {
                // 更新现有流程
                const index = workflowsData.findIndex(w => w.id === editingWorkflowId);
                if (index !== -1) {
                    workflowsData[index] = workflowData;
                }
            } else {
                // 添加新流程
                workflowsData.push(workflowData);
            }
            
            // 保存到本地存储
            localStorage.setItem('customWorkflows', JSON.stringify(workflowsData));
            
            // 切换到列表视图
            switchWorkflowTab('list');
            showNotification(editingWorkflowId ? '流程更新成功！' : '流程创建成功！');
            
            // 重置表单
            resetWorkflowEditor();
        }

        function editWorkflow(id) {
            const workflow = workflowsData.find(w => w.id === id);
            if (!workflow) return;
            
            editingWorkflowId = id;
            
            // 填充表单
            document.getElementById('workflowName').value = workflow.name;
            document.getElementById('workflowDesc').value = workflow.desc || '';
            
            // 恢复步骤
            workflowSteps = workflow.steps.map(s => ({...s}));
            renderWorkflowSteps();
            
            // 切换到编辑器标签
            switchWorkflowTab('editor');
        }

        function copyWorkflow(id) {
            const workflow = workflowsData.find(w => w.id === id);
            if (!workflow) return;
            
            // 创建副本
            const copyWorkflow = {
                ...workflow,
                id: Date.now(),
                name: workflow.name + ' (副本)',
                created: Date.now(),
                updated: Date.now(),
                steps: workflow.steps.map(s => ({...s, id: Date.now() + Math.random()}))
            };
            
            workflowsData.push(copyWorkflow);
            localStorage.setItem('customWorkflows', JSON.stringify(workflowsData));
            
            renderWorkflowList();
            showNotification('流程已复制');
        }

        function deleteWorkflow(id) {
            if (!confirm('确定要删除这个流程吗？')) return;
            
            workflowsData = workflowsData.filter(w => w.id !== id);
            localStorage.setItem('customWorkflows', JSON.stringify(workflowsData));
            
            renderWorkflowList();
            showNotification('流程已删除');
        }

        // 初始化页面
        window.addEventListener('load', () => {
            init();
        });

        // 监听窗口大小变化，优化响应式布局
        window.addEventListener('resize', debounce(() => {
            // 根据窗口大小调整布局
            const width = window.innerWidth;
            if (width < 768) {
                // 移动端适配
                document.querySelector('.category-sidebar').style.display = 'none';
                document.querySelector('.prompts-area').style.padding = '20px';
            } else {
                document.querySelector('.category-sidebar').style.display = 'block';
                document.querySelector('.prompts-area').style.padding = '30px';
            }
        }, 250));

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
</body>
</html>

