<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <title>写作 - 笔尖传奇</title>
    <style>
        /* --- 移动端写作界面CSS --- */
        :root {
            /* 基础颜色 */
            --bg-main: #F0F3F7;
            --bg-panel: #FAFBFC;
            --bg-content: #F7F9FB;
            --text-dark: #2D3748;
            --text-light: #718096;
            --text-on-primary: #FFFFFF;
            --border-color: #E8EBEF;
            --shadow-color: rgba(93, 156, 236, 0.08);
            --shadow-color-light: rgba(0, 0, 0, 0.04);
            --primary-color: #5D9CEC;
            --primary-color-hover: #4A89E2;
            --accent-color: #48BB78;
            --warning-color: #FF6B6B;
            
            /* 移动端尺寸 */
            --mobile-header-height: 56px;
            --mobile-toolbar-height: 48px;
            --mobile-padding: 16px;
            --mobile-border-radius: 12px;
            --mobile-font-size: 16px;
            --mobile-font-size-sm: 14px;
            --mobile-font-size-lg: 18px;
        }

        [data-theme="green-leaf"] {
            --bg-main: #EFF3EF;
            --bg-panel: #F8FAF8;
            --bg-content: #F5F8F5;
            --text-dark: #3E4A3E;
            --text-light: #6B7C6B;
            --border-color: #E0E6E0;
            --primary-color: #6A9C89;
            --primary-color-hover: #5A8C79;
            --accent-color: #E99469;
            --shadow-color: rgba(106, 156, 137, 0.08);
        }

        [data-theme="sepia"] {
            --bg-main: #FBF0D9;
            --bg-panel: #FAF4E8;
            --bg-content: #FAF4E8;
            --text-dark: #5C4B33;
            --text-light: #8B7355;
            --border-color: #EAE0C8;
            --primary-color: #A67B5B;
            --primary-color-hover: #966B4B;
            --accent-color: #5D9CEC;
            --shadow-color: rgba(166, 123, 91, 0.1);
        }

        [data-theme="dark"] {
            --bg-main: #1A202C;
            --bg-panel: #2D3748;
            --bg-content: #323B4C;
            --text-dark: #E2E8F0;
            --text-light: #A0AEC0;
            --border-color: #4A5568;
            --primary-color: #4A5568;
            --primary-color-hover: #718096;
            --accent-color: #48BB78;
            --shadow-color: rgba(0, 0, 0, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", "Helvetica Neue", sans-serif;
            background-color: var(--bg-main);
            color: var(--text-dark);
            font-size: var(--mobile-font-size);
            line-height: 1.6;
            height: 100vh;
            overflow: hidden;
            transition: background-color 0.3s, color 0.3s;
        }

        /* --- 移动端写作容器 --- */
        .writing-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* --- 顶部导航栏 --- */
        .writing-header {
            height: var(--mobile-header-height);
            background: var(--bg-panel);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--mobile-padding);
            position: relative;
            z-index: 100;
            box-shadow: 0 2px 8px var(--shadow-color-light);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            width: 36px;
            height: 36px;
            border: none;
            background: transparent;
            color: var(--text-dark);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .back-btn:hover {
            background: var(--bg-content);
        }

        .work-title {
            font-size: var(--mobile-font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-btn {
            padding: 8px 12px;
            background: var(--bg-content);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: var(--mobile-font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .header-btn:hover {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        .header-btn.primary {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        /* --- 工具栏 --- */
        .writing-toolbar {
            height: var(--mobile-toolbar-height);
            background: var(--bg-panel);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            padding: 0 var(--mobile-padding);
            gap: 8px;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .writing-toolbar::-webkit-scrollbar {
            display: none;
        }

        .toolbar-btn {
            padding: 6px 12px;
            background: var(--bg-content);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: var(--mobile-font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .toolbar-btn:hover {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        .toolbar-btn.active {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        .toolbar-btn.theme-btn {
            width: 32px;
            height: 32px;
            padding: 0;
            border-radius: 50%;
            justify-content: center;
        }

        .toolbar-btn.theme-btn.default {
            background: #5D9CEC;
        }

        .toolbar-btn.theme-btn.green-leaf {
            background: #6A9C89;
        }

        .toolbar-btn.theme-btn.sepia {
            background: #A67B5B;
        }

        .toolbar-btn.theme-btn.dark {
            background: #4A5568;
        }

        .toolbar-divider {
            width: 1px;
            height: 24px;
            background: var(--border-color);
            margin: 0 4px;
        }

        /* --- 主编辑区域 --- */
        .writing-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .editor-container {
            flex: 1;
            padding: var(--mobile-padding);
            overflow-y: auto;
        }

        .editor-textarea {
            width: 100%;
            height: 100%;
            min-height: 300px;
            border: none;
            outline: none;
            background: transparent;
            color: var(--text-dark);
            font-size: var(--mobile-font-size);
            line-height: 1.8;
            font-family: inherit;
            resize: none;
            padding: 0;
        }

        .editor-textarea::placeholder {
            color: var(--text-light);
        }

        /* --- 底部输入区域 --- */
        .input-section {
            background: var(--bg-panel);
            border-top: 1px solid var(--border-color);
            padding: var(--mobile-padding);
            box-shadow: 0 -2px 8px var(--shadow-color-light);
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-textarea {
            flex: 1;
            min-height: 40px;
            max-height: 120px;
            border: 1px solid var(--border-color);
            border-radius: var(--mobile-border-radius);
            padding: 12px;
            font-size: var(--mobile-font-size);
            font-family: inherit;
            background: var(--bg-content);
            color: var(--text-dark);
            resize: none;
            outline: none;
            transition: border-color 0.2s;
        }

        .input-textarea:focus {
            border-color: var(--primary-color);
        }

        .send-btn {
            width: 48px;
            height: 48px;
            border: none;
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-radius: var(--mobile-border-radius);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        .send-btn:hover {
            background: var(--primary-color-hover);
            transform: scale(1.05);
        }

        .send-btn:disabled {
            background: var(--text-light);
            cursor: not-allowed;
            transform: none;
        }

        /* --- 字数统计 --- */
        .word-count {
            position: absolute;
            top: var(--mobile-padding);
            right: var(--mobile-padding);
            font-size: 12px;
            color: var(--text-light);
            background: var(--bg-panel);
            padding: 4px 8px;
            border-radius: 6px;
            box-shadow: 0 2px 4px var(--shadow-color);
            z-index: 10;
        }

        /* --- 模式切换面板 --- */
        .mode-panel {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: var(--mobile-padding);
        }

        .mode-content {
            background: var(--bg-panel);
            border-radius: var(--mobile-border-radius);
            padding: 24px;
            max-width: 400px;
            width: 100%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .mode-title {
            font-size: var(--mobile-font-size-lg);
            font-weight: 600;
            margin-bottom: 16px;
            text-align: center;
        }

        .mode-options {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .mode-option {
            padding: 16px;
            background: var(--bg-content);
            border: 2px solid var(--border-color);
            border-radius: var(--mobile-border-radius);
            cursor: pointer;
            transition: all 0.2s;
        }

        .mode-option:hover {
            border-color: var(--primary-color);
        }

        .mode-option.selected {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: var(--text-on-primary);
        }

        .option-name {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .option-desc {
            font-size: var(--mobile-font-size-sm);
            opacity: 0.8;
        }

        /* --- 响应式优化 --- */
        @media (max-width: 480px) {
            .work-title {
                max-width: 150px;
            }
            
            .header-btn {
                padding: 6px 8px;
                font-size: 12px;
            }
            
            .toolbar-btn {
                padding: 4px 8px;
                font-size: 12px;
            }
        }

        /* --- 动画效果 --- */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .mode-content {
            animation: slideUp 0.3s ease-out;
        }

        /* --- 加载状态 --- */
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid var(--text-light);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="writing-container">
        <!-- 顶部导航栏 -->
        <header class="writing-header">
            <div class="header-left">
                <button class="back-btn" onclick="goBack()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
                    </svg>
                </button>
                <h1 class="work-title" onclick="editTitle()">我的作品</h1>
            </div>
            <div class="header-right">
                <button class="header-btn" onclick="showModePanel()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    模式
                </button>
                <button class="header-btn primary" onclick="saveWork()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7l-4-4zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm3-10H5V5h10v4z"/>
                    </svg>
                    保存
                </button>
            </div>
        </header>

        <!-- 工具栏 -->
        <div class="writing-toolbar">
            <button class="toolbar-btn active" onclick="setMode('write')">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M14.06,9.02l0.91,0.91L5.66,19.24L4.75,18.33L14.06,9.02z"/>
                </svg>
                写作
            </button>
            <button class="toolbar-btn" onclick="setMode('outline')">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"/>
                </svg>
                大纲
            </button>
            <button class="toolbar-btn" onclick="setMode('character')">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
                </svg>
                角色
            </button>
            <button class="toolbar-btn" onclick="setMode('setting')">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                设定
            </button>
            <button class="toolbar-btn" onclick="setMode('research')">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                </svg>
                资料
            </button>

            <div class="toolbar-divider"></div>

            <button class="toolbar-btn" onclick="undoAction()">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8z"/>
                </svg>
                撤回
            </button>
            <button class="toolbar-btn" onclick="redoAction()">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18.4 10.6C16.55 8.99 14.15 8 11.5 8c-4.65 0-8.58 3.03-9.96 7.22L3.9 16c1.05-3.19 4.05-5.5 7.6-5.5 1.95 0 3.73.72 5.12 1.88L13 16h9V7l-3.6 3.6z"/>
                </svg>
                重做
            </button>
            <button class="toolbar-btn" onclick="formatText()">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M5 4v3h5.5v12h3V7H19V4z"/>
                </svg>
                排版
            </button>

            <div class="toolbar-divider"></div>

            <!-- 主题切换按钮 -->
            <button class="toolbar-btn theme-btn default" onclick="setTheme('default')" title="默认主题"></button>
            <button class="toolbar-btn theme-btn green-leaf" onclick="setTheme('green-leaf')" title="豆沙绿"></button>
            <button class="toolbar-btn theme-btn sepia" onclick="setTheme('sepia')" title="羊皮纸"></button>
            <button class="toolbar-btn theme-btn dark" onclick="setTheme('dark')" title="暗夜模式"></button>
        </div>

        <!-- 主编辑区域 -->
        <main class="writing-main">
            <div class="editor-container">
                <div class="word-count" id="wordCount">0 字</div>
                <textarea 
                    class="editor-textarea" 
                    id="mainEditor"
                    placeholder="开始您的创作之旅..."
                    oninput="updateWordCount()"
                ></textarea>
            </div>
        </main>

        <!-- 底部输入区域 -->
        <section class="input-section">
            <div class="input-container">
                <textarea 
                    class="input-textarea" 
                    id="promptInput"
                    placeholder="输入提示词或指令..."
                    onkeydown="handleInputKeydown(event)"
                ></textarea>
                <button class="send-btn" id="sendBtn" onclick="sendPrompt()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                </button>
            </div>
        </section>
    </div>

    <!-- 模式选择面板 -->
    <div class="mode-panel" id="modePanel">
        <div class="mode-content">
            <h3 class="mode-title">选择创作模式</h3>
            <div class="mode-options">
                <div class="mode-option" onclick="selectMode('simple')">
                    <div class="option-name">🌟 简约模式</div>
                    <div class="option-desc">适合新手，简单易用</div>
                </div>
                <div class="mode-option" onclick="selectMode('professional')">
                    <div class="option-name">⚡ 专业模式</div>
                    <div class="option-desc">功能完整，适合专业创作</div>
                </div>
                <div class="mode-option" onclick="selectMode('brainstorm')">
                    <div class="option-name">💡 脑洞模式</div>
                    <div class="option-desc">激发创意，自由发挥</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentMode = 'write';
        let isLoading = false;
        let autoSaveTimer = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTheme();
            loadWorkData();
            setupAutoSave();
            
            // 自动调整输入框高度
            const inputTextarea = document.getElementById('promptInput');
            inputTextarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
        });

        // 主题管理
        function loadTheme() {
            const savedTheme = localStorage.getItem('mobile-theme') || 'default';
            document.documentElement.setAttribute('data-theme', savedTheme);
            updateThemeButtons(savedTheme);
        }

        function setTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('mobile-theme', theme);
            updateThemeButtons(theme);
        }

        function updateThemeButtons(theme) {
            document.querySelectorAll('.theme-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.theme-btn.${theme}`).classList.add('active');
        }

        // 返回功能
        function goBack() {
            if (confirm('确定要离开吗？未保存的内容将丢失。')) {
                window.location.href = 'index.html';
            }
        }

        // 标题编辑
        function editTitle() {
            const titleElement = document.querySelector('.work-title');
            const currentTitle = titleElement.textContent;
            const newTitle = prompt('请输入作品标题:', currentTitle);
            if (newTitle && newTitle.trim()) {
                titleElement.textContent = newTitle.trim();
                saveWorkData();
            }
        }

        // 模式切换
        function setMode(mode) {
            currentMode = mode;
            document.querySelectorAll('.toolbar-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 这里可以根据不同模式加载不同的内容
            loadModeContent(mode);
        }

        function loadModeContent(mode) {
            const editor = document.getElementById('mainEditor');
            const savedContent = localStorage.getItem(`mobile-writing-${mode}`) || '';
            editor.value = savedContent;
            updateWordCount();
        }

        // 字数统计
        function updateWordCount() {
            const editor = document.getElementById('mainEditor');
            const text = editor.value;
            const wordCount = text.length;
            document.getElementById('wordCount').textContent = `${wordCount} 字`;
        }

        // 输入处理
        function handleInputKeydown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendPrompt();
            }
        }

        // 发送提示词
        function sendPrompt() {
            const input = document.getElementById('promptInput');
            const prompt = input.value.trim();
            
            if (!prompt || isLoading) return;
            
            isLoading = true;
            updateSendButton();
            
            // 模拟AI响应
            setTimeout(() => {
                const editor = document.getElementById('mainEditor');
                const response = generateAIResponse(prompt);
                
                if (editor.value) {
                    editor.value += '\n\n' + response;
                } else {
                    editor.value = response;
                }
                
                input.value = '';
                input.style.height = 'auto';
                updateWordCount();
                saveWorkData();
                
                isLoading = false;
                updateSendButton();
            }, 1500);
        }

        function updateSendButton() {
            const sendBtn = document.getElementById('sendBtn');
            if (isLoading) {
                sendBtn.innerHTML = '<div class="loading"></div>';
                sendBtn.disabled = true;
            } else {
                sendBtn.innerHTML = '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/></svg>';
                sendBtn.disabled = false;
            }
        }

        // 模拟AI响应
        function generateAIResponse(prompt) {
            const responses = [
                '根据您的提示，我为您生成了以下内容：\n\n在这个充满神秘色彩的世界里，主人公踏上了一段不平凡的旅程...',
                '基于您的创意，故事可以这样展开：\n\n夜幕降临，城市的霓虹灯闪烁着，仿佛在诉说着什么秘密...',
                '让我为您的故事添加一些细节：\n\n角色的内心独白揭示了更深层的动机，推动着情节向前发展...'
            ];
            return responses[Math.floor(Math.random() * responses.length)];
        }

        // 模式面板
        function showModePanel() {
            document.getElementById('modePanel').style.display = 'flex';
        }

        function selectMode(mode) {
            // 这里可以根据选择的模式进行相应的配置
            console.log('选择模式:', mode);
            document.getElementById('modePanel').style.display = 'none';
        }

        // 点击面板外部关闭
        document.getElementById('modePanel').addEventListener('click', function(e) {
            if (e.target === this) {
                this.style.display = 'none';
            }
        });

        // 数据保存和加载
        function saveWorkData() {
            const editor = document.getElementById('mainEditor');
            const title = document.querySelector('.work-title').textContent;
            
            localStorage.setItem(`mobile-writing-${currentMode}`, editor.value);
            localStorage.setItem('mobile-work-title', title);
            localStorage.setItem('mobile-work-lastSaved', new Date().toISOString());
        }

        function loadWorkData() {
            const savedTitle = localStorage.getItem('mobile-work-title');
            if (savedTitle) {
                document.querySelector('.work-title').textContent = savedTitle;
            }
            
            loadModeContent(currentMode);
        }

        function saveWork() {
            saveWorkData();
            
            // 显示保存成功提示
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg>已保存';
            
            setTimeout(() => {
                btn.innerHTML = originalText;
            }, 2000);
        }

        // 工具栏功能
        function undoAction() {
            document.execCommand('undo');
            showToast('已撤回');
        }

        function redoAction() {
            document.execCommand('redo');
            showToast('已重做');
        }

        function formatText() {
            const editor = document.getElementById('mainEditor');
            if (editor) {
                let text = editor.value;

                // 统一段落间距
                text = text.replace(/\n{3,}/g, '\n\n');

                // 去除行首行尾空格
                text = text.split('\n').map(line => line.trim()).join('\n');

                // 标点符号后添加适当空格（中英文混排）
                text = text.replace(/([。！？])([A-Za-z])/g, '$1 $2');
                text = text.replace(/([A-Za-z])([，。！？])/g, '$1$2');

                editor.value = text;
                updateWordCount();
                showToast('智能排版完成');
            }
        }

        // 自动保存
        function setupAutoSave() {
            const editor = document.getElementById('mainEditor');
            editor.addEventListener('input', function() {
                clearTimeout(autoSaveTimer);
                autoSaveTimer = setTimeout(saveWorkData, 2000);
            });
        }

        // 提示消息
        function showToast(message) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: var(--text-dark);
                color: var(--text-on-primary);
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 1000;
                animation: fadeIn 0.3s ease-out;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 2000);
        }

        // 防止意外离开
        window.addEventListener('beforeunload', function(e) {
            saveWorkData();
        });

        // 监听来自父页面的消息
        window.addEventListener('message', function(event) {
            if (event.data.type === 'setMode') {
                // 设置写作模式
                console.log('设置模式:', event.data.mode);
            } else if (event.data.type === 'usePrompt') {
                // 使用提示词
                const prompt = event.data.prompt;
                const input = document.getElementById('promptInput');
                if (input) {
                    input.value = `使用提示词: ${prompt.title}`;
                }
            }
        });
    </script>
</body>
</html>
