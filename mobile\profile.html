<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <title>我的 - 笔尖传奇</title>
    <style>
        /* --- 移动端个人中心CSS --- */
        :root {
            /* 基础颜色 */
            --bg-main: #F0F3F7;
            --bg-panel: #FAFBFC;
            --bg-content: #F7F9FB;
            --text-dark: #2D3748;
            --text-light: #718096;
            --text-on-primary: #FFFFFF;
            --border-color: #E8EBEF;
            --shadow-color: rgba(93, 156, 236, 0.08);
            --shadow-color-light: rgba(0, 0, 0, 0.04);
            --shadow-color-heavy: rgba(0, 0, 0, 0.12);
            --primary-color: #5D9CEC;
            --primary-color-hover: #4A89E2;
            --accent-color: #48BB78;
            --warning-color: #FF6B6B;

            /* 移动端尺寸 */
            --mobile-header-height: 56px;
            --mobile-bottom-nav-height: 60px;
            --mobile-padding: 16px;
            --mobile-border-radius: 12px;
            --mobile-font-size: 16px;
            --mobile-font-size-sm: 14px;
            --mobile-font-size-lg: 18px;
        }

        [data-theme="green-leaf"] {
            --bg-main: #EFF3EF;
            --bg-panel: #F8FAF8;
            --bg-content: #F5F8F5;
            --text-dark: #3E4A3E;
            --text-light: #6B7C6B;
            --border-color: #E0E6E0;
            --primary-color: #6A9C89;
            --primary-color-hover: #5A8C79;
            --accent-color: #E99469;
            --shadow-color: rgba(106, 156, 137, 0.08);
        }

        [data-theme="sepia"] {
            --bg-main: #FBF0D9;
            --bg-panel: #FAF4E8;
            --bg-content: #FAF4E8;
            --text-dark: #5C4B33;
            --text-light: #8B7355;
            --border-color: #EAE0C8;
            --primary-color: #A67B5B;
            --primary-color-hover: #966B4B;
            --accent-color: #5D9CEC;
            --shadow-color: rgba(166, 123, 91, 0.1);
        }

        [data-theme="dark"] {
            --bg-main: #1A202C;
            --bg-panel: #2D3748;
            --bg-content: #323B4C;
            --text-dark: #E2E8F0;
            --text-light: #A0AEC0;
            --border-color: #4A5568;
            --primary-color: #4A5568;
            --primary-color-hover: #718096;
            --accent-color: #48BB78;
            --shadow-color: rgba(0, 0, 0, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", "Helvetica Neue", sans-serif;
            background-color: var(--bg-main);
            color: var(--text-dark);
            font-size: var(--mobile-font-size);
            line-height: 1.6;
            height: 100vh;
            overflow-x: hidden;
            transition: background-color 0.3s, color 0.3s;
        }

        /* --- 个人中心容器 --- */
        .profile-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            padding-bottom: var(--mobile-bottom-nav-height);
        }

        /* --- 用户信息头部 --- */
        .profile-header {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: var(--text-on-primary);
            padding: 60px var(--mobile-padding) 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .profile-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.3;
        }

        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 16px;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            position: relative;
            z-index: 1;
        }

        .user-name {
            font-size: var(--mobile-font-size-lg);
            font-weight: 600;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .user-title {
            font-size: var(--mobile-font-size-sm);
            opacity: 0.9;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .user-stats {
            display: flex;
            justify-content: center;
            gap: 32px;
            position: relative;
            z-index: 1;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: var(--mobile-font-size-lg);
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: var(--mobile-font-size-sm);
            opacity: 0.8;
        }

        /* --- 主内容区 --- */
        .profile-content {
            flex: 1;
            padding: var(--mobile-padding);
        }

        /* --- 功能菜单 --- */
        .menu-section {
            background: var(--bg-panel);
            border-radius: var(--mobile-border-radius);
            margin-bottom: 16px;
            box-shadow: 0 2px 8px var(--shadow-color);
            overflow: hidden;
        }

        .menu-title {
            padding: 16px 20px;
            font-size: var(--mobile-font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-content);
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .menu-item:active {
            background: var(--bg-content);
            transform: scale(0.98);
        }

        .menu-item:hover {
            background: var(--bg-content);
        }

        .menu-icon {
            width: 24px;
            height: 24px;
            margin-right: 16px;
            color: var(--primary-color);
            flex-shrink: 0;
        }

        .menu-content {
            flex: 1;
        }

        .menu-label {
            font-size: var(--mobile-font-size);
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 2px;
        }

        .menu-desc {
            font-size: var(--mobile-font-size-sm);
            color: var(--text-light);
        }

        .menu-arrow {
            width: 16px;
            height: 16px;
            color: var(--text-light);
            flex-shrink: 0;
        }

        .menu-badge {
            background: var(--warning-color);
            color: var(--text-on-primary);
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 10px;
            margin-right: 8px;
        }

        /* --- 主题切换 --- */
        .theme-switcher {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-left: auto;
        }

        .theme-option {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 2px solid var(--border-color);
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }

        .theme-option.active {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(93, 156, 236, 0.2);
        }

        .theme-option.light {
            background: linear-gradient(135deg, #F0F3F7, #FAFBFC);
        }

        .theme-option.dark {
            background: linear-gradient(135deg, #1A202C, #2D3748);
        }

        /* --- 底部导航栏 --- */
        .mobile-bottom-nav {
            height: var(--mobile-bottom-nav-height);
            background: var(--bg-panel);
            border-top: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-around;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 100;
            box-shadow: 0 -2px 8px var(--shadow-color-light);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            color: var(--text-light);
            transition: all 0.2s ease;
            padding: 8px 12px;
            border-radius: 8px;
            min-width: 60px;
        }

        .nav-item:active {
            transform: scale(0.95);
        }

        .nav-item.active {
            color: var(--primary-color);
        }

        .nav-item-icon {
            font-size: 20px;
            width: 24px;
            height: 24px;
        }

        .nav-item-text {
            font-size: 12px;
            font-weight: 500;
        }

        /* --- 响应式优化 --- */
        @media (max-width: 480px) {
            .user-stats {
                gap: 24px;
            }

            .stat-number {
                font-size: var(--mobile-font-size);
            }
        }

        /* --- 动画效果 --- */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .menu-section {
            animation: fadeInUp 0.6s ease-out;
        }

        .menu-section:nth-child(2) {
            animation-delay: 0.1s;
        }

        .menu-section:nth-child(3) {
            animation-delay: 0.2s;
        }
    </style>
</head>
<body>
    <div class="profile-container">
        <!-- 用户信息头部 -->
        <header class="profile-header">
            <div class="user-avatar">👤</div>
            <div class="user-name" onclick="editUserName()">创作者</div>
            <div class="user-title">笔尖传奇作家</div>
            <div class="user-stats">
                <div class="stat-item">
                    <div class="stat-number" id="totalWords">0</div>
                    <div class="stat-label">总字数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalBooks">0</div>
                    <div class="stat-label">作品数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="writingDays">0</div>
                    <div class="stat-label">创作天数</div>
                </div>
            </div>
        </header>

        <!-- 主内容区 -->
        <main class="profile-content">
            <!-- 创作工具 -->
            <section class="menu-section">
                <div class="menu-title">创作工具</div>
                <div class="menu-item" onclick="openWritingTools()">
                    <svg class="menu-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M14.06,9.02l0.91,0.91L5.66,19.24L4.75,18.33L14.06,9.02 M17.66,3c-0.25,0-0.51,0.1-0.7,0.29l-1.83,1.83l3.75,3.75l1.83-1.83c0.39-0.39,0.39-1.02,0-1.41l-2.34-2.34C18.17,3.09,17.92,3,17.66,3L17.66,3z M12.06,6.19L3,15.25V19.24h3.99l9.06-9.06L12.06,6.19z"/>
                    </svg>
                    <div class="menu-content">
                        <div class="menu-label">写作助手</div>
                        <div class="menu-desc">AI智能写作，提升创作效率</div>
                    </div>
                    <svg class="menu-arrow" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                    </svg>
                </div>
                <div class="menu-item" onclick="openPromptLibrary()">
                    <svg class="menu-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    <div class="menu-content">
                        <div class="menu-label">提示词库</div>
                        <div class="menu-desc">丰富的创作提示词模板</div>
                    </div>
                    <svg class="menu-arrow" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                    </svg>
                </div>
                <div class="menu-item" onclick="openCharacterManager()">
                    <svg class="menu-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
                    </svg>
                    <div class="menu-content">
                        <div class="menu-label">角色管理</div>
                        <div class="menu-desc">创建和管理小说角色</div>
                    </div>
                    <svg class="menu-arrow" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                    </svg>
                </div>
            </section>

            <!-- 数据统计 -->
            <section class="menu-section">
                <div class="menu-title">数据统计</div>
                <div class="menu-item" onclick="openWritingStats()">
                    <svg class="menu-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                    </svg>
                    <div class="menu-content">
                        <div class="menu-label">创作统计</div>
                        <div class="menu-desc">查看详细的创作数据分析</div>
                    </div>
                    <svg class="menu-arrow" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                    </svg>
                </div>
                <div class="menu-item" onclick="openGoalSetting()">
                    <svg class="menu-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2-7h-3V2h-2v2H8V2H6v2H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H3V9h14v11z"/>
                    </svg>
                    <div class="menu-content">
                        <div class="menu-label">目标设定</div>
                        <div class="menu-desc">设置每日创作目标</div>
                    </div>
                    <svg class="menu-arrow" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                    </svg>
                </div>
            </section>

            <!-- 设置选项 -->
            <section class="menu-section">
                <div class="menu-title">设置</div>
                <div class="menu-item">
                    <svg class="menu-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    <div class="menu-content">
                        <div class="menu-label">主题设置</div>
                        <div class="menu-desc">选择您喜欢的界面主题</div>
                    </div>
                    <div class="theme-switcher">
                        <div class="theme-option light" onclick="setTheme('default')" title="默认主题" style="background: linear-gradient(135deg, #F0F3F7, #FAFBFC);"></div>
                        <div class="theme-option" onclick="setTheme('green-leaf')" title="豆沙绿" style="background: linear-gradient(135deg, #EFF3EF, #F8FAF8);"></div>
                        <div class="theme-option" onclick="setTheme('sepia')" title="羊皮纸" style="background: linear-gradient(135deg, #FBF0D9, #FAF4E8);"></div>
                        <div class="theme-option dark" onclick="setTheme('dark')" title="暗夜模式" style="background: linear-gradient(135deg, #1A202C, #2D3748);"></div>
                    </div>
                </div>
                <div class="menu-item" onclick="openBackupRestore()">
                    <svg class="menu-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    <div class="menu-content">
                        <div class="menu-label">备份与恢复</div>
                        <div class="menu-desc">保护您的创作成果</div>
                    </div>
                    <svg class="menu-arrow" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                    </svg>
                </div>
                <div class="menu-item" onclick="openHelp()">
                    <svg class="menu-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M11,18H13V16H11V18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,20C7.59,20 4,16.41 4,12C7.59,4 4,12A10,10 0 0,0 12,2M12,6A4,4 0 0,0 8,10H10A2,2 0 0,1 12,8A2,2 0 0,1 14,10C14,12 11,11.75 11,15H13C13,12.75 16,12.5 16,10A4,4 0 0,0 12,6Z"/>
                    </svg>
                    <div class="menu-content">
                        <div class="menu-label">帮助与反馈</div>
                        <div class="menu-desc">使用指南和问题反馈</div>
                    </div>
                    <svg class="menu-arrow" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                    </svg>
                </div>
                <div class="menu-item" onclick="openAbout()">
                    <svg class="menu-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                    </svg>
                    <div class="menu-content">
                        <div class="menu-label">关于应用</div>
                        <div class="menu-desc">版本信息和开发团队</div>
                    </div>
                    <svg class="menu-arrow" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                    </svg>
                </div>
            </section>
        </main>

        <!-- 底部导航栏 -->
        <nav class="mobile-bottom-nav">
            <div class="nav-item" onclick="window.location.href='index.html'">
                <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                </svg>
                <span class="nav-item-text">首页</span>
            </div>
            <div class="nav-item" onclick="window.location.href='writing.html'">
                <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M14.06,9.02l0.91,0.91L5.66,19.24L4.75,18.33L14.06,9.02 M17.66,3c-0.25,0-0.51,0.1-0.7,0.29l-1.83,1.83l3.75,3.75l1.83-1.83c0.39-0.39,0.39-1.02,0-1.41l-2.34-2.34C18.17,3.09,17.92,3,17.66,3L17.66,3z M12.06,6.19L3,15.25V19.24h3.99l9.06-9.06L12.06,6.19z"/>
                </svg>
                <span class="nav-item-text">写作</span>
            </div>
            <div class="nav-item" onclick="window.location.href='chat.html'">
                <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                </svg>
                <span class="nav-item-text">对话</span>
            </div>
            <div class="nav-item" onclick="window.location.href='library.html'">
                <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/>
                </svg>
                <span class="nav-item-text">书架</span>
            </div>
            <div class="nav-item active">
                <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
                </svg>
                <span class="nav-item-text">我的</span>
            </div>
        </nav>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTheme();
            loadUserData();
            updateStats();
        });

        // 主题管理
        function loadTheme() {
            const savedTheme = localStorage.getItem('mobile-theme') || 'default';
            document.documentElement.setAttribute('data-theme', savedTheme);
            updateThemeButtons(savedTheme);
        }

        function setTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('mobile-theme', theme);
            updateThemeButtons(theme);
        }

        function updateThemeButtons(theme) {
            document.querySelectorAll('.theme-option').forEach(btn => {
                btn.classList.remove('active');
            });

            // 根据主题名称添加active类
            const themeMap = {
                'default': 0,
                'green-leaf': 1,
                'sepia': 2,
                'dark': 3
            };

            const themeIndex = themeMap[theme] || 0;
            const themeButtons = document.querySelectorAll('.theme-option');
            if (themeButtons[themeIndex]) {
                themeButtons[themeIndex].classList.add('active');
            }
        }

        // 用户数据管理
        function loadUserData() {
            const savedName = localStorage.getItem('mobile-user-name');
            if (savedName) {
                document.querySelector('.user-name').textContent = savedName;
            }
        }

        function editUserName() {
            const currentName = document.querySelector('.user-name').textContent;
            const newName = prompt('请输入您的昵称:', currentName);
            if (newName && newName.trim()) {
                document.querySelector('.user-name').textContent = newName.trim();
                localStorage.setItem('mobile-user-name', newName.trim());
            }
        }

        // 统计数据更新
        function updateStats() {
            // 从本地存储获取统计数据
            const books = JSON.parse(localStorage.getItem('mobile-books') || '[]');
            const totalWords = books.reduce((sum, book) => sum + book.wordCount, 0);
            const totalBooks = books.length;

            // 计算创作天数（简化计算）
            const firstBookDate = books.length > 0 ? new Date(books[books.length - 1].lastModified) : new Date();
            const today = new Date();
            const writingDays = Math.max(1, Math.ceil((today - firstBookDate) / (1000 * 60 * 60 * 24)));

            // 更新显示
            document.getElementById('totalWords').textContent = totalWords.toLocaleString();
            document.getElementById('totalBooks').textContent = totalBooks;
            document.getElementById('writingDays').textContent = writingDays;
        }

        // 功能菜单点击事件
        function openWritingTools() {
            window.location.href = 'writing.html';
        }

        function openPromptLibrary() {
            showToast('提示词库功能开发中...');
        }

        function openCharacterManager() {
            showToast('角色管理功能开发中...');
        }

        function openWritingStats() {
            showToast('创作统计功能开发中...');
        }

        function openGoalSetting() {
            showToast('目标设定功能开发中...');
        }

        function openBackupRestore() {
            if (confirm('是否要导出所有创作数据？')) {
                exportData();
            }
        }

        function openHelp() {
            showToast('帮助功能开发中...');
        }

        function openAbout() {
            alert('笔尖传奇写作 v1.0.0\n\n一款专为移动端优化的智能写作工具\n\n© 2024 笔尖传奇团队');
        }

        // 数据导出功能
        function exportData() {
            const data = {
                books: JSON.parse(localStorage.getItem('mobile-books') || '[]'),
                chatHistory: JSON.parse(localStorage.getItem('mobile-chat-history') || '[]'),
                userSettings: {
                    theme: localStorage.getItem('mobile-theme'),
                    userName: localStorage.getItem('mobile-user-name')
                },
                exportDate: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `笔尖传奇数据备份_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showToast('数据导出成功！');
        }

        // 提示消息
        function showToast(message) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: var(--text-dark);
                color: var(--text-on-primary);
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 1000;
                animation: fadeIn 0.3s ease-out;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 2000);
        }

        // 底部导航激活状态管理
        function setActiveNav(index) {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach((item, i) => {
                item.classList.toggle('active', i === index);
            });
        }
    </script>
</body>
</html>