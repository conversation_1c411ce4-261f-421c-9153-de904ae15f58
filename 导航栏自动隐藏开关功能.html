<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航栏自动隐藏开关功能 - 优化代码</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", sans-serif;
            line-height: 1.6;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
            border-left: 4px solid #667eea;
            padding-left: 15px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            overflow-x: auto;
        }
        pre {
            margin: 0;
            white-space: pre-wrap;
        }
        code {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        .feature-list {
            background: #e8f4fd;
            border-left: 4px solid #2196F3;
            padding: 15px 20px;
            margin: 15px 0;
        }
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 导航栏自动隐藏开关功能 - 完整优化代码</h1>
        
        <div class="feature-list">
            <h3>✨ 功能特性</h3>
            <ul>
                <li>在个人中心添加设置选项，可控制导航栏自动隐藏功能</li>
                <li>提供开关按钮，用户可以启用/禁用自动隐藏</li>
                <li>设置持久化保存到本地存储</li>
                <li>包含引导提示开关功能</li>
                <li>支持重置所有设置功能</li>
                <li>完整的移动端适配</li>
                <li>优雅的UI设计和动画效果</li>
            </ul>
        </div>

        <h2>1. HTML 结构 - 个人中心菜单添加设置选项</h2>
        <div class="code-block">
            <pre><code>&lt;!-- 在个人中心菜单中添加设置选项 --&gt;
&lt;button class="user-center-menu-item" onclick="openSettings()"&gt;
    &lt;div class="user-center-menu-item-icon"&gt;⚙️&lt;/div&gt;
    &lt;div class="user-center-menu-item-text"&gt;设置&lt;/div&gt;
    &lt;div class="user-center-menu-item-arrow"&gt;›&lt;/div&gt;
&lt;/button&gt;</code></pre>
        </div>

        <h2>2. HTML 结构 - 设置界面弹窗</h2>
        <div class="code-block">
            <pre><code>&lt;!-- 设置界面 --&gt;
&lt;div class="settings-modal" id="settingsModal"&gt;
    &lt;div class="settings-content"&gt;
        &lt;div class="settings-header"&gt;
            &lt;div class="settings-title"&gt;⚙️ 设置&lt;/div&gt;
            &lt;button class="settings-close" onclick="closeSettingsModal()"&gt;&amp;times;&lt;/button&gt;
        &lt;/div&gt;
        &lt;div class="settings-body"&gt;
            &lt;div class="settings-section"&gt;
                &lt;div class="settings-section-title"&gt;界面设置&lt;/div&gt;
                &lt;div class="settings-item"&gt;
                    &lt;div class="settings-item-info"&gt;
                        &lt;div class="settings-item-title"&gt;导航栏自动隐藏&lt;/div&gt;
                        &lt;div class="settings-item-desc"&gt;启用后，导航栏会在鼠标离开时自动隐藏，悬停左侧边缘可重新显示&lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="settings-item-control"&gt;
                        &lt;label class="toggle-switch"&gt;
                            &lt;input type="checkbox" id="autoHideSidebarToggle" checked onchange="toggleAutoHideSidebar()"&gt;
                            &lt;span class="toggle-slider"&gt;&lt;/span&gt;
                        &lt;/label&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;div class="settings-item"&gt;
                    &lt;div class="settings-item-info"&gt;
                        &lt;div class="settings-item-title"&gt;引导提示&lt;/div&gt;
                        &lt;div class="settings-item-desc"&gt;控制是否显示新手引导提示&lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="settings-item-control"&gt;
                        &lt;label class="toggle-switch"&gt;
                            &lt;input type="checkbox" id="showGuideToggle" onchange="toggleGuideDisplay()"&gt;
                            &lt;span class="toggle-slider"&gt;&lt;/span&gt;
                        &lt;/label&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="settings-section"&gt;
                &lt;div class="settings-section-title"&gt;其他设置&lt;/div&gt;
                &lt;div class="settings-item"&gt;
                    &lt;div class="settings-item-info"&gt;
                        &lt;div class="settings-item-title"&gt;重置所有设置&lt;/div&gt;
                        &lt;div class="settings-item-desc"&gt;将所有设置恢复为默认值&lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="settings-item-control"&gt;
                        &lt;button class="btn btn-secondary btn-sm" onclick="resetAllSettings()"&gt;重置&lt;/button&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="settings-actions"&gt;
                &lt;button class="btn btn-secondary" onclick="closeSettingsModalAndReturnToUserCenter()"&gt;返回&lt;/button&gt;
                &lt;button class="btn btn-primary" onclick="saveSettings()"&gt;保存设置&lt;/button&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;</code></pre>
        </div>

        <h2>3. CSS 样式 - 设置界面样式</h2>
        <div class="code-block">
            <pre><code>/* --- 设置界面样式 --- */
.settings-modal {
    display: none;
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1003;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}
.settings-modal.show {
    opacity: 1;
}
.settings-content {
    background: var(--bg-panel);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    width: 600px;
    max-width: 90vw;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}
.settings-modal.show .settings-content {
    transform: scale(1) translateY(0);
}
.settings-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    padding: 25px 30px;
    color: white;
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
}
.settings-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0;
    flex: 1;
}
.settings-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}
.settings-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}
.settings-body {
    padding: 30px;
    max-height: 60vh;
    overflow-y: auto;
}
.settings-section {
    margin-bottom: 30px;
}
.settings-section:last-child {
    margin-bottom: 0;
}
.settings-section-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--border-color);
}
.settings-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid var(--border-color);
}
.settings-item:last-child {
    border-bottom: none;
}
.settings-item-info {
    flex: 1;
    margin-right: 20px;
}
.settings-item-title {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 5px;
}
.settings-item-desc {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    line-height: 1.5;
}
.settings-item-control {
    flex-shrink: 0;
}
.settings-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}</code></pre>
        </div>

        <h2>4. CSS 样式 - 开关按钮样式</h2>
        <div class="code-block">
            <pre><code>/* 开关按钮样式 */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}
.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}
.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--border-color);
    transition: 0.3s;
    border-radius: 24px;
}
.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}
input:checked + .toggle-slider {
    background-color: var(--primary-color);
}
input:checked + .toggle-slider:before {
    transform: translateX(26px);
}
.toggle-slider:hover {
    box-shadow: 0 0 0 3px var(--shadow-color);
}

/* 小按钮样式 */
.btn-sm {
    padding: 8px 16px;
    font-size: var(--font-size-sm);
}</code></pre>
        </div>

        <h2>5. JavaScript - 设置管理和变量定义</h2>
        <div class="code-block">
            <pre><code>// --- 设置管理 ---
let settings = {
    autoHideSidebar: true,
    showGuide: true
};

// 加载设置
function loadSettings() {
    const savedSettings = localStorage.getItem('userSettings');
    if (savedSettings) {
        settings = { ...settings, ...JSON.parse(savedSettings) };
    }

    // 更新UI
    document.getElementById('autoHideSidebarToggle').checked = settings.autoHideSidebar;
    document.getElementById('showGuideToggle').checked = settings.showGuide;
}

// 保存设置
function saveSettingsToStorage() {
    localStorage.setItem('userSettings', JSON.stringify(settings));
}

// --- 导航栏自动隐藏/显示逻辑 (重写版) ---
let sidebarTimer;
let autoHideEnabled = true;</code></pre>
        </div>

        <h2>6. JavaScript - 修改导航栏隐藏逻辑</h2>
        <div class="code-block">
            <pre><code>// 鼠标进入感应区域
sidebarTrigger.addEventListener('mouseenter', () => {
    clearTimeout(sidebarTimer);
    sidebarWrapper.classList.remove('collapsed');

    if (isGuideActive) {
        showToast('很好！现在点击头像打开个人中心', 'info');
    }
});

// 鼠标离开感应区域
sidebarTrigger.addEventListener('mouseleave', (e) => {
    if (!autoHideEnabled) return; // 如果禁用自动隐藏，则不执行隐藏逻辑

    // 检查鼠标是否移到了导航栏上
    const toElement = e.relatedTarget;
    if (toElement && !sidebarWrapper.contains(toElement)) {
        sidebarTimer = setTimeout(() => {
            sidebarWrapper.classList.add('collapsed');
        }, 300);
    }
});

// 鼠标在导航栏区域内移动时保持显示
sidebarWrapper.addEventListener('mouseenter', () => {
    clearTimeout(sidebarTimer);
});

// 鼠标离开整个导航栏区域
sidebarWrapper.addEventListener('mouseleave', () => {
    if (!autoHideEnabled) return; // 如果禁用自动隐藏，则不执行隐藏逻辑

    sidebarTimer = setTimeout(() => {
        sidebarWrapper.classList.add('collapsed');
    }, 300);
});</code></pre>
        </div>

        <h2>7. JavaScript - 设置界面功能函数</h2>
        <div class="code-block">
            <pre><code>// --- 设置功能 ---
function openSettings() {
    closeUserCenter();
    loadSettings(); // 加载当前设置
    const modal = document.getElementById('settingsModal');
    modal.style.display = 'flex';
    requestAnimationFrame(() => {
        modal.classList.add('show');
    });
}

function closeSettingsModal() {
    const modal = document.getElementById('settingsModal');
    modal.classList.remove('show');
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

function closeSettingsModalAndReturnToUserCenter() {
    const settingsModal = document.getElementById('settingsModal');
    const userCenterModal = document.getElementById('userCenterModal');

    // 同时开始两个动画
    settingsModal.classList.remove('show');
    userCenterModal.style.display = 'flex';

    // 延迟一点点让设置弹窗开始退出动画
    setTimeout(() => {
        userCenterModal.classList.add('show');
    }, 150);

    // 清理设置弹窗
    setTimeout(() => {
        settingsModal.style.display = 'none';
    }, 300);
}

function toggleAutoHideSidebar() {
    const toggle = document.getElementById('autoHideSidebarToggle');
    settings.autoHideSidebar = toggle.checked;
    autoHideEnabled = toggle.checked;

    if (!autoHideEnabled) {
        // 如果禁用自动隐藏，确保导航栏显示
        clearTimeout(sidebarTimer);
        sidebarWrapper.classList.remove('collapsed');
        showToast('导航栏自动隐藏已禁用', 'info');
    } else {
        showToast('导航栏自动隐藏已启用', 'info');
    }
}

function toggleGuideDisplay() {
    const toggle = document.getElementById('showGuideToggle');
    settings.showGuide = toggle.checked;

    if (toggle.checked) {
        localStorage.removeItem('guidePermanentlyClosed');
        showToast('引导提示已启用', 'info');
    } else {
        localStorage.setItem('guidePermanentlyClosed', 'true');
        document.getElementById('guideTooltip').classList.remove('show');
        showToast('引导提示已禁用', 'info');
    }
}

function resetAllSettings() {
    if (confirm('确定要重置所有设置吗？这将恢复默认配置。')) {
        // 重置设置
        settings = {
            autoHideSidebar: true,
            showGuide: true
        };

        // 清除本地存储
        localStorage.removeItem('userSettings');
        localStorage.removeItem('guidePermanentlyClosed');

        // 更新UI
        document.getElementById('autoHideSidebarToggle').checked = true;
        document.getElementById('showGuideToggle').checked = true;

        // 应用设置
        autoHideEnabled = true;

        showToast('所有设置已重置为默认值', 'info');
    }
}

function saveSettings() {
    saveSettingsToStorage();
    showToast('设置已保存', 'info');
    setTimeout(() => {
        closeSettingsModalAndReturnToUserCenter();
    }, 1000);
}</code></pre>
        </div>

        <h2>8. JavaScript - 事件监听器和初始化</h2>
        <div class="code-block">
            <pre><code>// 添加设置弹窗的外部点击关闭事件
document.getElementById('settingsModal').addEventListener('click', function(e) {
    if (e.target === this) closeSettingsModal();
});

// 键盘快捷键支持 - 在现有的ESC键监听中添加
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        // ... 其他弹窗的处理 ...
        const settingsModal = document.getElementById('settingsModal');

        if (settingsModal.classList.contains('show')) {
            closeSettingsModal();
        }
        // ... 其他弹窗的处理 ...
    }
});

// --- 初始化 ---
document.addEventListener('DOMContentLoaded', function() {
    // 加载用户设置
    loadSettings();
    autoHideEnabled = settings.autoHideSidebar;

    // 检查是否永久关闭了引导
    const guidePermanentlyClosed = localStorage.getItem('guidePermanentlyClosed');

    if (!guidePermanentlyClosed && settings.showGuide) {
        // 每次页面加载都显示引导（除非用户选择永久关闭）
        setTimeout(() => {
            document.getElementById('guideTooltip').classList.add('show');
        }, 1500);
    }

    // ... 其他初始化代码 ...
});</code></pre>
        </div>

        <h2>9. CSS - 移动端适配</h2>
        <div class="code-block">
            <pre><code>/* 移动端适配 */
@media (max-width: 768px) {
    .user-center-content, .recharge-full-content, .profile-content,
    .guide-content, .purchase-content, .prompt-gifts-content, .settings-content {
        width: 95vw;
        margin: 0 auto;
    }
}

@media (max-width: 480px) {
    .user-center-header, .recharge-full-header, .profile-header,
    .guide-header, .purchase-header, .exchange-header,
    .prompt-gifts-header, .settings-header {
        padding: 20px 15px;
    }
    .user-center-name, .recharge-full-title, .profile-title,
    .guide-title, .purchase-title, .prompt-gifts-title, .settings-title {
        font-size: var(--font-size-base);
    }
    .settings-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    .settings-item-info {
        margin-right: 0;
    }
    .settings-actions {
        flex-direction: column;
        gap: 10px;
    }
    .settings-actions .btn {
        width: 100%;
        text-align: center;
    }
}</code></pre>
        </div>

        <div class="feature-list">
            <h3>🎯 使用说明</h3>
            <ul>
                <li><strong>安装：</strong>将以上代码按顺序添加到您的HTML文件中</li>
                <li><strong>个人中心：</strong>在个人中心菜单中会出现"设置"选项</li>
                <li><strong>导航栏控制：</strong>在设置中可以开启/关闭导航栏自动隐藏功能</li>
                <li><strong>引导提示：</strong>可以控制是否显示新手引导提示</li>
                <li><strong>设置保存：</strong>所有设置会自动保存到本地存储</li>
                <li><strong>重置功能：</strong>可以一键重置所有设置为默认值</li>
                <li><strong>移动端：</strong>完全适配移动端设备</li>
            </ul>
        </div>

        <div class="feature-list">
            <h3>🔧 技术特点</h3>
            <ul>
                <li>使用localStorage进行设置持久化</li>
                <li>优雅的CSS3动画和过渡效果</li>
                <li>响应式设计，支持各种屏幕尺寸</li>
                <li>模块化的JavaScript代码结构</li>
                <li>完整的错误处理和用户反馈</li>
                <li>符合现代Web开发最佳实践</li>
            </ul>
        </div>
    </div>
</body>
</html>
