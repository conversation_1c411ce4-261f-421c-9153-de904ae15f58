<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <title>书架 - 笔尖传奇</title>
    <style>
        /* --- 移动端书架界面CSS --- */
        :root {
            /* 基础颜色 */
            --bg-main: #F0F3F7;
            --bg-panel: #FAFBFC;
            --bg-content: #F7F9FB;
            --text-dark: #2D3748;
            --text-light: #718096;
            --text-on-primary: #FFFFFF;
            --border-color: #E8EBEF;
            --shadow-color: rgba(93, 156, 236, 0.08);
            --shadow-color-light: rgba(0, 0, 0, 0.04);
            --shadow-color-heavy: rgba(0, 0, 0, 0.12);
            --primary-color: #5D9CEC;
            --primary-color-hover: #4A89E2;
            --accent-color: #48BB78;
            --warning-color: #FF6B6B;

            /* 移动端尺寸 */
            --mobile-header-height: 56px;
            --mobile-bottom-nav-height: 60px;
            --mobile-padding: 16px;
            --mobile-border-radius: 12px;
            --mobile-font-size: 16px;
            --mobile-font-size-sm: 14px;
            --mobile-font-size-lg: 18px;
        }

        [data-theme="dark"] {
            --bg-main: #1A202C;
            --bg-panel: #2D3748;
            --bg-content: #323B4C;
            --text-dark: #E2E8F0;
            --text-light: #A0AEC0;
            --border-color: #4A5568;
            --primary-color: #4A5568;
            --primary-color-hover: #718096;
            --accent-color: #48BB78;
            --shadow-color: rgba(0, 0, 0, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", "Helvetica Neue", sans-serif;
            background-color: var(--bg-main);
            color: var(--text-dark);
            font-size: var(--mobile-font-size);
            line-height: 1.6;
            height: 100vh;
            overflow-x: hidden;
            transition: background-color 0.3s, color 0.3s;
        }

        /* --- 书架容器 --- */
        .library-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* --- 顶部导航栏 --- */
        .library-header {
            height: var(--mobile-header-height);
            background: var(--bg-panel);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--mobile-padding);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
            box-shadow: 0 2px 8px var(--shadow-color-light);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            width: 36px;
            height: 36px;
            border: none;
            background: transparent;
            color: var(--text-dark);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .back-btn:hover {
            background: var(--bg-content);
        }

        .library-title {
            font-size: var(--mobile-font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-btn {
            padding: 6px 12px;
            background: var(--bg-content);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: var(--mobile-font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .header-btn:hover {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        .header-btn.primary {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        /* --- 搜索栏 --- */
        .search-section {
            padding: var(--mobile-padding);
            background: var(--bg-panel);
            border-bottom: 1px solid var(--border-color);
            margin-top: var(--mobile-header-height);
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 100%;
            height: 44px;
            border: 1px solid var(--border-color);
            border-radius: 22px;
            padding: 0 20px 0 44px;
            font-size: var(--mobile-font-size);
            background: var(--bg-content);
            color: var(--text-dark);
            outline: none;
            transition: border-color 0.2s;
        }

        .search-input:focus {
            border-color: var(--primary-color);
        }

        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            pointer-events: none;
        }

        /* --- 筛选标签 --- */
        .filter-section {
            padding: 0 var(--mobile-padding) var(--mobile-padding);
            background: var(--bg-panel);
            border-bottom: 1px solid var(--border-color);
        }

        .filter-tabs {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .filter-tabs::-webkit-scrollbar {
            display: none;
        }

        .filter-tab {
            padding: 8px 16px;
            background: var(--bg-content);
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            font-size: var(--mobile-font-size-sm);
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
            flex-shrink: 0;
        }

        .filter-tab:hover {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        .filter-tab.active {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        /* --- 主内容区 --- */
        .main-content {
            flex: 1;
            overflow-y: auto;
            padding-bottom: var(--mobile-bottom-nav-height);
        }

        /* --- 书籍列表 --- */
        .books-section {
            padding: var(--mobile-padding);
        }

        .books-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
            gap: 16px;
        }

        .book-card {
            background: var(--bg-panel);
            border-radius: var(--mobile-border-radius);
            padding: 16px;
            box-shadow: 0 2px 8px var(--shadow-color);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .book-card:active {
            transform: scale(0.98);
        }

        .book-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 16px var(--shadow-color-heavy);
        }

        .book-cover {
            width: 100%;
            height: 120px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: 8px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .book-cover::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        }

        .book-info {
            text-align: center;
        }

        .book-title {
            font-size: var(--mobile-font-size-sm);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 4px;
            line-height: 1.3;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .book-meta {
            font-size: 12px;
            color: var(--text-light);
            margin-bottom: 8px;
        }

        .book-progress {
            width: 100%;
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .book-progress-bar {
            height: 100%;
            background: var(--accent-color);
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .book-stats {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
            color: var(--text-light);
        }

        /* --- 空状态 --- */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            text-align: center;
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: var(--mobile-font-size-lg);
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-dark);
        }

        .empty-desc {
            font-size: var(--mobile-font-size-sm);
            color: var(--text-light);
            line-height: 1.5;
            margin-bottom: 24px;
        }

        .empty-action {
            padding: 12px 24px;
            background: var(--primary-color);
            color: var(--text-on-primary);
            border: none;
            border-radius: var(--mobile-border-radius);
            font-size: var(--mobile-font-size);
            cursor: pointer;
            transition: all 0.2s;
        }

        .empty-action:hover {
            background: var(--primary-color-hover);
            transform: translateY(-2px);
        }

        /* --- 底部导航栏 --- */
        .mobile-bottom-nav {
            height: var(--mobile-bottom-nav-height);
            background: var(--bg-panel);
            border-top: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-around;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 100;
            box-shadow: 0 -2px 8px var(--shadow-color-light);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            color: var(--text-light);
            transition: all 0.2s ease;
            padding: 8px 12px;
            border-radius: 8px;
            min-width: 60px;
        }

        .nav-item:active {
            transform: scale(0.95);
        }

        .nav-item.active {
            color: var(--primary-color);
        }

        .nav-item-icon {
            font-size: 20px;
            width: 24px;
            height: 24px;
        }

        .nav-item-text {
            font-size: 12px;
            font-weight: 500;
        }

        /* --- 响应式优化 --- */
        @media (max-width: 480px) {
            .books-grid {
                grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
                gap: 12px;
            }

            .book-cover {
                height: 100px;
                font-size: 28px;
            }
        }

        /* --- 动画效果 --- */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .book-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .book-card:nth-child(even) {
            animation-delay: 0.1s;
        }
    </style>
</head>
<body>
    <div class="library-container">
        <!-- 顶部导航栏 -->
        <header class="library-header">
            <div class="header-left">
                <button class="back-btn" onclick="goBack()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
                    </svg>
                </button>
                <h1 class="library-title">我的书架</h1>
            </div>
            <div class="header-right">
                <button class="header-btn" onclick="sortBooks()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z"/>
                    </svg>
                    排序
                </button>
                <button class="header-btn primary" onclick="createNewBook()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                    新建
                </button>
            </div>
        </header>

        <!-- 搜索栏 -->
        <section class="search-section">
            <div class="search-container">
                <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
                <input
                    type="text"
                    class="search-input"
                    placeholder="搜索书籍标题、类型..."
                    oninput="searchBooks(this.value)"
                >
            </div>
        </section>

        <!-- 筛选标签 -->
        <section class="filter-section">
            <div class="filter-tabs">
                <button class="filter-tab active" onclick="filterBooks('all')">全部</button>
                <button class="filter-tab" onclick="filterBooks('recent')">最近</button>
                <button class="filter-tab" onclick="filterBooks('long')">长篇</button>
                <button class="filter-tab" onclick="filterBooks('medium')">中篇</button>
                <button class="filter-tab" onclick="filterBooks('short')">短篇</button>
                <button class="filter-tab" onclick="filterBooks('script')">剧本</button>
                <button class="filter-tab" onclick="filterBooks('draft')">草稿</button>
            </div>
        </section>

        <!-- 主内容区 -->
        <main class="main-content">
            <section class="books-section">
                <div class="books-grid" id="booksGrid">
                    <!-- 书籍卡片将通过JavaScript动态生成 -->
                </div>

                <!-- 空状态 -->
                <div class="empty-state" id="emptyState" style="display: none;">
                    <div class="empty-icon">📚</div>
                    <div class="empty-title">书架空空如也</div>
                    <div class="empty-desc">开始您的第一部作品，记录创作的每一个灵感时刻</div>
                    <button class="empty-action" onclick="createNewBook()">创建新作品</button>
                </div>
            </section>
        </main>

        <!-- 底部导航栏 -->
        <nav class="mobile-bottom-nav">
            <div class="nav-item" onclick="window.location.href='index.html'">
                <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                </svg>
                <span class="nav-item-text">首页</span>
            </div>
            <div class="nav-item" onclick="window.location.href='writing.html'">
                <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M14.06,9.02l0.91,0.91L5.66,19.24L4.75,18.33L14.06,9.02 M17.66,3c-0.25,0-0.51,0.1-0.7,0.29l-1.83,1.83l3.75,3.75l1.83-1.83c0.39-0.39,0.39-1.02,0-1.41l-2.34-2.34C18.17,3.09,17.92,3,17.66,3L17.66,3z M12.06,6.19L3,15.25V19.24h3.99l9.06-9.06L12.06,6.19z"/>
                </svg>
                <span class="nav-item-text">写作</span>
            </div>
            <div class="nav-item" onclick="window.location.href='chat.html'">
                <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                </svg>
                <span class="nav-item-text">对话</span>
            </div>
            <div class="nav-item active">
                <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/>
                </svg>
                <span class="nav-item-text">书架</span>
            </div>
            <div class="nav-item" onclick="window.location.href='profile.html'">
                <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
                </svg>
                <span class="nav-item-text">我的</span>
            </div>
        </nav>
    </div>

    <script>
        // 全局变量
        let books = [];
        let filteredBooks = [];
        let currentFilter = 'all';

        // 模拟书籍数据
        const mockBooks = [
            {
                id: 1,
                title: "星辰大海的征途",
                type: "long",
                genre: "科幻",
                wordCount: 125000,
                chapters: 45,
                progress: 75,
                lastModified: "2024-01-15",
                cover: "🚀",
                status: "writing"
            },
            {
                id: 2,
                title: "江南烟雨",
                type: "medium",
                genre: "言情",
                wordCount: 68000,
                chapters: 28,
                progress: 90,
                lastModified: "2024-01-12",
                cover: "🌸",
                status: "completed"
            },
            {
                id: 3,
                title: "悬疑档案",
                type: "short",
                genre: "悬疑",
                wordCount: 25000,
                chapters: 12,
                progress: 100,
                lastModified: "2024-01-10",
                cover: "🔍",
                status: "completed"
            },
            {
                id: 4,
                title: "修仙传说",
                type: "long",
                genre: "玄幻",
                wordCount: 280000,
                chapters: 89,
                progress: 45,
                lastModified: "2024-01-08",
                cover: "⚔️",
                status: "writing"
            },
            {
                id: 5,
                title: "都市风云",
                type: "medium",
                genre: "都市",
                wordCount: 95000,
                chapters: 35,
                progress: 60,
                lastModified: "2024-01-05",
                cover: "🏙️",
                status: "writing"
            },
            {
                id: 6,
                title: "时光剧本",
                type: "script",
                genre: "剧情",
                wordCount: 45000,
                chapters: 15,
                progress: 30,
                lastModified: "2024-01-03",
                cover: "🎭",
                status: "draft"
            }
        ];

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTheme();
            loadBooks();
            renderBooks();
        });

        // 主题管理
        function loadTheme() {
            const savedTheme = localStorage.getItem('mobile-theme') || 'default';
            document.documentElement.setAttribute('data-theme', savedTheme);
        }

        // 返回功能
        function goBack() {
            window.location.href = 'index.html';
        }

        // 加载书籍数据
        function loadBooks() {
            const savedBooks = localStorage.getItem('mobile-books');
            if (savedBooks) {
                books = JSON.parse(savedBooks);
            } else {
                books = mockBooks;
                saveBooks();
            }
            filteredBooks = books;
        }

        // 保存书籍数据
        function saveBooks() {
            localStorage.setItem('mobile-books', JSON.stringify(books));
        }

        // 渲染书籍列表
        function renderBooks() {
            const booksGrid = document.getElementById('booksGrid');
            const emptyState = document.getElementById('emptyState');

            if (filteredBooks.length === 0) {
                booksGrid.style.display = 'none';
                emptyState.style.display = 'flex';
                return;
            }

            booksGrid.style.display = 'grid';
            emptyState.style.display = 'none';

            booksGrid.innerHTML = filteredBooks.map(book => `
                <div class="book-card" onclick="openBook(${book.id})">
                    <div class="book-cover">${book.cover}</div>
                    <div class="book-info">
                        <div class="book-title">${book.title}</div>
                        <div class="book-meta">${getTypeText(book.type)} · ${book.wordCount.toLocaleString()}字</div>
                        <div class="book-progress">
                            <div class="book-progress-bar" style="width: ${book.progress}%"></div>
                        </div>
                        <div class="book-stats">
                            <span>${book.chapters}章</span>
                            <span>${book.progress}%</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 获取类型文本
        function getTypeText(type) {
            const typeMap = {
                'long': '长篇',
                'medium': '中篇',
                'short': '短篇',
                'script': '剧本'
            };
            return typeMap[type] || type;
        }

        // 筛选书籍
        function filterBooks(filter) {
            currentFilter = filter;

            // 更新筛选标签状态
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // 筛选逻辑
            if (filter === 'all') {
                filteredBooks = books;
            } else if (filter === 'recent') {
                filteredBooks = books.sort((a, b) => new Date(b.lastModified) - new Date(a.lastModified)).slice(0, 10);
            } else if (filter === 'draft') {
                filteredBooks = books.filter(book => book.status === 'draft');
            } else {
                filteredBooks = books.filter(book => book.type === filter);
            }

            renderBooks();
        }

        // 搜索书籍
        function searchBooks(query) {
            if (!query.trim()) {
                filterBooks(currentFilter);
                return;
            }

            filteredBooks = books.filter(book =>
                book.title.toLowerCase().includes(query.toLowerCase()) ||
                book.genre.toLowerCase().includes(query.toLowerCase()) ||
                getTypeText(book.type).includes(query)
            );

            renderBooks();
        }

        // 排序书籍
        function sortBooks() {
            const options = [
                '📅 最近修改',
                '🔤 标题排序',
                '📊 字数排序',
                '📈 进度排序',
                '⭐ 评分排序'
            ];

            const choice = prompt(`选择排序方式:\n\n${options.join('\n')}\n\n请输入数字(1-${options.length}):`);

            if (!choice || choice < 1 || choice > options.length) return;

            switch(parseInt(choice)) {
                case 1:
                    filteredBooks.sort((a, b) => new Date(b.lastModified) - new Date(a.lastModified));
                    break;
                case 2:
                    filteredBooks.sort((a, b) => a.title.localeCompare(b.title));
                    break;
                case 3:
                    filteredBooks.sort((a, b) => b.wordCount - a.wordCount);
                    break;
                case 4:
                    filteredBooks.sort((a, b) => b.progress - a.progress);
                    break;
                case 5:
                    filteredBooks.sort((a, b) => (b.rating || 0) - (a.rating || 0));
                    break;
            }

            renderBooks();
            showToast(`已按${options[choice-1]}排序`);
        }

        // 创建新书籍
        function createNewBook() {
            const bookInfo = showCreateBookDialog();
            if (!bookInfo) return;

            const newBook = {
                id: Date.now(),
                title: bookInfo.title,
                type: bookInfo.type,
                genre: bookInfo.genre,
                wordCount: 0,
                chapters: 0,
                progress: 0,
                lastModified: new Date().toISOString().split('T')[0],
                cover: getRandomCover(),
                status: 'draft',
                rating: 0,
                tags: bookInfo.tags || []
            };

            books.unshift(newBook);
            saveBooks();
            filterBooks(currentFilter);
            showToast('作品创建成功！');

            // 跳转到写作界面
            setTimeout(() => {
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                        type: 'openBook',
                        bookId: newBook.id
                    }, '*');
                } else {
                    window.location.href = `writing.html?bookId=${newBook.id}`;
                }
            }, 500);
        }

        // 显示创建书籍对话框
        function showCreateBookDialog() {
            const title = prompt('请输入作品标题:');
            if (!title || !title.trim()) return null;

            const types = [
                { id: 'long', name: '长篇小说' },
                { id: 'medium', name: '中篇小说' },
                { id: 'short', name: '短篇小说' },
                { id: 'script', name: '剧本' }
            ];

            const typeChoice = prompt(`选择作品类型:\n\n${types.map((t, i) => `${i+1}. ${t.name}`).join('\n')}\n\n请输入数字(1-${types.length}):`);
            const selectedType = types[parseInt(typeChoice) - 1];

            if (!selectedType) return null;

            const genres = ['玄幻', '都市', '言情', '科幻', '悬疑', '历史', '军事', '游戏', '其他'];
            const genreChoice = prompt(`选择作品类型:\n\n${genres.map((g, i) => `${i+1}. ${g}`).join('\n')}\n\n请输入数字(1-${genres.length}):`);
            const selectedGenre = genres[parseInt(genreChoice) - 1] || '其他';

            return {
                title: title.trim(),
                type: selectedType.id,
                genre: selectedGenre
            };
        }

        // 获取随机封面
        function getRandomCover() {
            const covers = ['📖', '📚', '📝', '✍️', '📜', '📋', '📄', '📃', '🎭', '🎬', '⚔️', '🌟', '💫', '🔮', '🏰', '🌸'];
            return covers[Math.floor(Math.random() * covers.length)];
        }

        // 打开书籍
        function openBook(bookId) {
            if (window.parent && window.parent !== window) {
                window.parent.postMessage({
                    type: 'openBook',
                    bookId: bookId
                }, '*');
            } else {
                window.location.href = `writing.html?bookId=${bookId}`;
            }
        }

        // 提示消息
        function showToast(message) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: var(--text-dark);
                color: var(--text-on-primary);
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 1000;
                animation: fadeIn 0.3s ease-out;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 2000);
        }

        // 底部导航激活状态管理
        function setActiveNav(index) {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach((item, i) => {
                item.classList.toggle('active', i === index);
            });
        }
    </script>
</body>
</html>